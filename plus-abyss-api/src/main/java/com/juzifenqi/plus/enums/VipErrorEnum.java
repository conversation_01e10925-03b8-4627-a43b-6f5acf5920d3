package com.juzifenqi.plus.enums;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/1/2 11:44 AM
 */
public enum VipErrorEnum {

    /**
     *
     */
    ERROR_100000(100000, "RPC请求用户中心异常，请稍后再试！"), ERROR_100001(100001,
            "RPC请求短信服务异常，请稍后再试！"), ERROR_100002(100002, "缺少请求参数，请确认后重新尝试！"), ERROR_100003(100003,
            "短时间内不允许重复请求！"), ERROR_100004(100004, "RPC请求订单中心异常，请稍后再试！"),

    PLUS_ERR_CODE_200000(200000, "用户ID不能为空！"), PLUS_ERR_CODE_200001(200001,
            "用户ID[userId]、渠道ID[channelId]、配置类型[configId]必传！"), PLUS_ERR_CODE_200002(200002,
            "你已成为付费会员"), PLUS_ERR_CODE_200003(200003, "账户状态异常,请稍后重试"), PLUS_ERR_CODE_200004(200004,
            "你暂时不是付费会员,请购买后在进行操作"), PLUS_ERR_CODE_200005(200005,
            "暂未定义相应付费会员类型,请联系管理员进行核实"), PLUS_ERR_CODE_200006(200006,
            "方案ID不能为空！"), PLUS_ERR_CODE_200007(200007, "权益模块ID不能为空！"), PLUS_ERR_CODE_200008(200008,
            "订单编号不能为空"), PLUS_ERR_CODE_200009(200009, "未查到对应的订单信息，请确认"), PLUS_ERR_CODE_200010(
            200010, "此订单是已支付状态，请确认"), PLUS_ERR_CODE_200011(200011,
            "此订单对应的方案不存在或已下架，不能继续支付"), PLUS_ERR_CODE_200012(200012,
            "分期期数不能为空"), PLUS_ERR_CODE_200013(200013, "订单金额不能为空"), PLUS_ERR_CODE_200014(200014,
            "订单不存在，请确认"), PLUS_ERR_CODE_200015(200015, "订单不是待支付状态，请确认"), PLUS_ERR_CODE_200016(
            200016, "您不是该订单的下单用户，请确认"), PLUS_ERR_CODE_200017(200017,
            "订单已失效，请重新下单。"), PLUS_ERR_CODE_200018(200018, "请先取消会员卡-"), PLUS_ERR_CODE_200019(200019,
            "请先取消最后一张会员卡"), PLUS_ERR_CODE_200020(200020, "获取用户分层规则异常"), PLUS_ERR_CODE_200021(200021,
            "请输入有效的sku"), PLUS_ERR_CODE_200022(200022, "系统超时，请返回重新查看"), PLUS_ERR_CODE_200023(200023,
            "当前会员已售罄"),PLUS_ERR_CODE_200024(200024,"新支付系统查询无结果"),


    PLUS_ERROR_300001(300001, "您还不是会员，请开通后重试"), PLUS_ERROR_300002(300002,
            "调用用户中心未获取到用户数据"), PLUS_ERROR_300003(300003,
            "您已是桔享plus会员，请到会员详情页查看享有的权益"), PLUS_ERROR_300010(300010,
            "未查询到您的桔享Plus领取记录,请确认后重新尝试！"), PLUS_ERROR_300011(300011,
            "您的优惠券已经领取过，不允许重复领取！"), PLUS_ERROR_300012(300012, "您的订单已经取消，不能领取！"), PLUS_ERROR_300013(
            300013, "查询PLUS会员信息时出现未知异常，请稍后重试！"), PLUS_ERROR_300014(300014,
            "取消PLUS会员信息出现未知异常，请稍后重试！"), PLUS_ERROR_300015(300015,
            "当前用户不是PLUS会员，无法取消，请确认后重试！"), PLUS_ERROR_300016(300016,
            "当前用户不是PLUS会员"), PLUS_ERROR_300017(300017, "您的优惠券未达到领取条件"), PLUS_ERROR_300018(300018,
            "未查询到您的待领取优惠券记录"), PLUS_ERROR_300019(300019, "已使用额度,不可以取消会员"), PLUS_ERROR_300302(300302,
            "暂时不可使用，请先支付会员费用"),PLUS_ERROR_300303(300303,
            "系统发放中，请稍后查看"),

    /**
     * 获取小白熊卡密异常
     */
    XBX_ERROR_400001(400001, "系统开小差，辛苦重新获取～"), XBX_ERROR_400002(400002,
            "您还不是桔享会员，请开通后重试！"), XBX_ERROR_400003(400003, "当前方案无生活权益"), XBX_ERROR_400004(400004,
            "未获取到方案权益包信息"), XBX_ERROR_400005(400005, "未获取到用户购买会员订单，请确认"), XBX_ERROR_400006(400006,
            "该用户已选择了会员权益，不能重复获取"), XBX_ERROR_400007(400007, "未查到生活权益初始信息"), XBX_ERROR_400008(400008,
            "选择的权益数量与方案设置不匹配"),

    /**
     * 风控熔断接口
     */
    PLUS_ERROR_500001(500001, "系统开小差，请稍后重试"), PLUS_ERROR_500002(500002, "会员名额已售罄，正在努力上架中..."),

    /**
     * 商品购买判断
     */
    PLUS_ERROR_600001(600001, "商品spu不能为空"),

    /**
     * 取消会员校验
     */
    PLUS_ERROR_700001(700001, "未查到此会员购买记录，无法取消"), PLUS_ERROR_700002(700002,
            "当前购买记录不是支付成功状态，无法取消"), PLUS_ERROR_700003(700003,
            "该用户已不是桔享Plus会员，无法取消"), PLUS_ERROR_700004(700004, "该会员已使用开卡礼，无法取消",
            3), PLUS_ERROR_700005(700005, "该会员已使用息费折扣券权益，无法取消", 8), PLUS_ERROR_700006(700006,
            "该会员已使用拒就赔权益，无法取消", 1), PLUS_ERROR_700007(700007, "该会员已使用多买多送权益，无法取消",
            2), PLUS_ERROR_700008(700008, "未获取到会员信息"), PLUS_ERROR_700009(700009,
            "该会员购买过半价商品，不允许取消"), PLUS_ERROR_700010(700010, "该会员已使用会员提额权益，无法取消"), PLUS_ERROR_700011(
            700011, "调订单服务判断该会员是否购买半价商品异常，请稍后重试"), PLUS_ERROR_700012(700012,
            "该会员已使用抽奖获得的优惠券，无法取消"), PLUS_ERROR_700015(700015,
            "调订单服务查询用户在商城(商品,借款)下单数量异常，请稍后重试"), PLUS_ERROR_700016(700016,
            "调订单服务更改订单状态异常，请稍后重试"), PLUS_ERROR_700017(700017,
            "该会员已使用还款优惠权益，无法取消"), PLUS_ERROR_700018(700018, "请求认证卡服务取消会员信息出错"), PLUS_ERROR_700019(
            700019, "请求还款卡服务取消会员信息出错"), PLUS_ERROR_700020(700020, "该用户已经使用过还款券",
            4), PLUS_ERROR_700021(700021, "该用户已认证成功/认证中，无法取消会员", 5), PLUS_ERROR_700022(700022,
            "该会员已使用品牌专区权益，无法取消", 6), PLUS_ERROR_700023(700023, "该会员已使用生日关怀权益，无法取消",
            7), PLUS_ERROR_700024(700024, "该会员取消订单对应会员状态已过期，无法取消"), PLUS_ERROR_700025(700025,
            "续费当日不可以取消"), PLUS_ERROR_700026(700026, "该用户购买时间已经超过7天，无法取消"), PLUS_ERROR_700027(700027,
            "该会员已使用加速权益，无法取消"), PLUS_ERROR_700028(700028, "该会员已使用虚拟商品权益，无法取消"), PLUS_ERROR_700029(
            700029, "该用户存在息费补贴订单，无法取消，订单号："), PLUS_ERROR_700030(700030,
            "降息卡关联订单获取失败"), PLUS_ERROR_700031(700031, "有777渠道的在途订单"), PLUS_ERROR_700032(700032,
            "批量取消订单已超上限"), PLUS_ERROR_700033(700033, "该批次号已存在，请重新输入"), PLUS_ERROR_700034(700034,
            "批量取消未查到会员订单"),

    PLUS_ERROR_800003(800003, "未查到有效方案"), PLUS_ERROR_800004(800004, "查询权益状态异常"), PLUS_ERROR_800005(
            800005, "您不符合参与活动条件"), PLUS_ERROR_800006(800006, "您已领取了此优惠券"), PLUS_ERROR_800007(800007,
            "您已领取了生活权益福利"), PLUS_ERROR_800008(800008, "领取失败，请稍后重试"), PLUS_ERROR_800009(800009,
            "所够方案未查到活动设置，请确认"), PLUS_ERROR_800010(800010, "活动未开始"), PLUS_ERROR_800011(800011,
            "活动已结束"), PLUS_ERROR_800013(800013, "未查到优惠券,请联系管理员"), PLUS_ERROR_800014(800014,
            "通知订单更改利率失败"), PLUS_ERROR_800015(800015, "会员扣减金额超过会员价，不允许取消会员"), PLUS_ERROR_800016(
            800016, "当前渠道不可售此会员"), PLUS_ERROR_800017(800017, "购物返现下单笔数不可重复"), PLUS_ERROR_800018(
            800018, "购物返现下单笔数必须连续且从1开始"), PLUS_ERROR_800019(800019,
            "退卡金额超过会员价，不允许取消会员"), PLUS_ERROR_800020(800020, "未查到有效续费信息"),

    PLUS_ERROR_900000(900000, "会员金额小于方案配置的会员购买首付金额"),
    ;

    private Integer code;
    private String  message;
    private Integer type;

    VipErrorEnum(Integer code, String message) {
        this.code = code;
        this.message = message;
    }

    VipErrorEnum(Integer code, String message, Integer type) {
        this.code = code;
        this.message = message;
        this.type = type;
    }

    public Integer getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }

    public static Integer getVipCodeByType(Integer type) {
        for (VipErrorEnum code : VipErrorEnum.values()) {
            if (type.equals(code.getType())) {
                return code.getCode();
            }
        }
        return null;
    }

    public static String getVipMessageByType(Integer type) {
        for (VipErrorEnum code : VipErrorEnum.values()) {
            if (type.equals(code.getType())) {
                return code.getMessage();
            }
        }
        return null;
    }

    public static void main(String[] args) {
        Integer vipCodeByType = getVipCodeByType(1);
        System.out.println(vipCodeByType);
    }
}
