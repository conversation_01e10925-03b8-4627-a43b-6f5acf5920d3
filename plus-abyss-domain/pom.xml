<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
        xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.juzifenqi</groupId>
        <artifactId>plus-abyss</artifactId>
        <version>ykd-1.0.9-6233498373-SNAPSHOT</version>
    </parent>

    <artifactId>plus-abyss-domain</artifactId>
    <version>ykd-1.0-SNAPSHOT</version>

    <properties>
        <mapstruct.version>1.4.1.Final</mapstruct.version>
    </properties>

    <dependencies>

        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>plus-abyss-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.juzifenqi</groupId>
            <artifactId>plus-abyss-external</artifactId>
            <version>ykd-1.0.1-SNAPSHOT</version>
        </dependency>

        <!-- mapStruct -->
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <dependency>
            <groupId>org.mapstruct</groupId>
            <artifactId>mapstruct-processor</artifactId>
            <version>${mapstruct.version}</version>
        </dependency>
        <!-- mapStruct end -->

        <!-- alibaba -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>1.0.26</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
            <version>2.0.3.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>


        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.jupiter</groupId>
                    <artifactId>junit-jupiter-api</artifactId>
                </exclusion>
            </exclusions>
            <scope>test</scope>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>3.1.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.skywalking</groupId>
            <artifactId>apm-toolkit-logback-1.x</artifactId>
            <version>6.0.0-GA</version>
        </dependency>

        <dependency>
            <groupId>com.taobao.arthas</groupId>
            <artifactId>arthas-spring-boot-starter</artifactId>
            <version>3.5.0</version>
        </dependency>

        <!-- mybatis -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.2</version>
        </dependency>
        <!-- mybatis end-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson-spring-boot-starter</artifactId>
            <version>3.16.2</version>
        </dependency>

        <dependency>
            <groupId>org.apache.shardingsphere</groupId>
            <artifactId>sharding-jdbc-spring-boot-starter</artifactId>
            <version>4.1.1</version>
        </dependency>
        <dependency>
            <groupId>org.bouncycastle</groupId>
            <artifactId>bcprov-jdk15on</artifactId>
            <version>1.60</version>
        </dependency>

        <dependency>
            <groupId>com.yikoudai</groupId>
            <artifactId>cove-open-entity</artifactId>
            <version>1.0.1.open-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.juzishuke.innovate</groupId>
            <artifactId>dss-sdk</artifactId>
            <version>1.3.21.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>com.juzishuke.framework</groupId>
            <artifactId>common</artifactId>
            <version>2.3.1.RELEASE</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <profileActive>local</profileActive>
            </properties>
        </profile>
        <profile>
            <id>dev1</id>
            <properties>
                <profileActive>dev1</profileActive>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <profileActive>test</profileActive>
            </properties>
        </profile>
        <profile>
            <id>pre</id>
            <properties>
                <profileActive>pre</profileActive>
            </properties>
        </profile>
        <profile>
            <id>lt</id>
            <properties>
                <profileActive>lt</profileActive>
            </properties>
        </profile>
        <profile>
            <id>prd</id>
            <properties>
                <profileActive>prd</profileActive>
            </properties>
        </profile>
        <profile>
            <id>sonar</id>
            <properties>
                <sonar.host.url>http://test-sonar.juzishuke.com:9000</sonar.host.url>
                <sonar.login>****************************************</sonar.login>
                <sonar.projectKey>plus-abyss</sonar.projectKey>
            </properties>
        </profile>
    </profiles>

    <build>
        <finalName>plus-abyss</finalName>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>dev1/*</exclude>
                    <exclude>test/*</exclude>
                    <exclude>pre/*</exclude>
                    <exclude>prd/*</exclude>
                </excludes>
            </resource>
            <resource>
                <directory>src/main/resources/${profileActive}</directory>
            </resource>
        </resources>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-deploy-plugin</artifactId>
                <version>2.8.2</version>
                <configuration>
                    <skip>true</skip>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
