package com.juzifenqi.plus.test.plus;


import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusMarketApi;
import com.juzifenqi.plus.dto.req.market.*;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.market.PlusLoanConfirmMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLoanMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusOrderListMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusPaySuccessMarketResp;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.annotation.Resource;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlusMarketApiTest {


    @Resource
    private IPlusMarketApi marketApi;


    @Test
    public void paySuccessMarketTest() {
        PlusPaySuccessMarketReq req = new PlusPaySuccessMarketReq();
        req.setChannelId(803003);
        req.setUserId(1000000946);
        req.setBizSource(2);
        PlusAbyssResult<PlusPaySuccessMarketResp> result1 =
                marketApi.paySuccessMarket(req);
        System.out.println(result1);
        PlusOrderListMarketReq req2 = new PlusOrderListMarketReq();
        req2.setChannelId(803003);
        req2.setUserId(1000000946);
        req2.setBizSource(2);
        PlusAbyssResult<PlusOrderListMarketResp> result2 = marketApi.orderListMarket(req2);

        System.out.println(result2);

        PlusLoanConfirmMarketReq req3 = new PlusLoanConfirmMarketReq();
        req3.setChannelId(803003);
        req3.setUserId(1000000946);
        req3.setBizSource(2);
        PlusAbyssResult<PlusLoanConfirmMarketResp> result3 = marketApi.loanConfirmMarket(req3);
        System.out.println(result3);

        PlusLoanMarketReq req4  = new PlusLoanMarketReq();
        req4.setChannelId(803003);
        req4.setUserId(1000000946);
        req4.setBizSource(2);
        PlusAbyssResult<PlusLoanMarketResp> result4 = marketApi.loanMarket(req4);
        System.out.println(result4);

        PlusBillListMarketReq req5 = new PlusBillListMarketReq();
        req5.setChannelId(803003);
        req5.setUserId(1000000946);
        req5.setBizSource(2);
        PlusAbyssResult<PlusOrderListMarketResp> result5 = marketApi.orderDetailMarket(req5);
        System.out.println(result5);

    }


}
