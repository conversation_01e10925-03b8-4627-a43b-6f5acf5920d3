package com.juzifenqi.plus.test.order;

import com.aliyun.openservices.ons.api.Message;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderNodeApplication;
import java.nio.charset.StandardCharsets;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
@Slf4j
public class IPlusOrderNodeApplicationTest {

    @Resource
    private IPlusOrderNodeApplication iPlusOrderNodeApplication;

    @Test
    public void testDealMq() {
        // dev1环境数据
        Message message = new Message();
        message.setBody(("{\n" + "    \"bizScene\": 1,\n" + "    \"channel\": 2,\n"
                + "    \"compulsoryNotarization\": 0,\n" + "    \"createTime\": 1727094182000,\n"
                + "    \"isFullPayment\": 100,\n" + "    \"memberId\": 170350802,\n"
                + "    \"memberShip\": 0,\n" + "    \"moneyOrder\": 369.00,\n"
                + "    \"orderChildNode\": \"QXDD\",\n" + "    \"orderNode\": \"QXDD\",\n"
                + "    \"orderNodeDesc\": \"取消订单\",\n"
                + "    \"orderSn\": \"992406201946171376\",\n" + "    \"orderState\": 222,\n"
                + "    \"orderType\": 1203,\n" + "    \"platform\": 10,\n"
                + "    \"productId\": 100026082,\n" + "    \"sellerId\": 0,\n"
                + "    \"source\": 0\n" + "}").getBytes(StandardCharsets.UTF_8));
        iPlusOrderNodeApplication.dealMq(message, "订单中心节点变更通知");
    }

}
