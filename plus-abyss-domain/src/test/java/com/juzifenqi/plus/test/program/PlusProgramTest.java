package com.juzifenqi.plus.test.program;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusDetailApi;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.api.admin.IPlusCommonAdminApi;
import com.juzifenqi.plus.api.admin.IPlusProgramAdminApi;
import com.juzifenqi.plus.dto.req.admin.common.CreatePlusBlackReq;
import com.juzifenqi.plus.dto.req.admin.common.PlusDiscountConfigQueryReq;
import com.juzifenqi.plus.dto.req.admin.program.CopyProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.EnableChannelManageReq;
import com.juzifenqi.plus.dto.req.admin.program.MultiplexChannelProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.SaveDiffPriceReq;
import com.juzifenqi.plus.dto.req.admin.program.SavePlusProgramReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.admin.program.DiffPriceDetailResp;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramDetailResp;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.order.application.IPlusOrderShuntApplication;
import com.juzifenqi.plus.module.order.model.event.order.ShuntEvent;
import com.juzifenqi.plus.module.program.application.IPlusProgramApplication;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProgramExtendMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductTypeMapper;
import java.math.BigDecimal;
import java.util.Arrays;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlusProgramTest {

    @Autowired
    private IPlusProgramAdminApi    adminApi;
    @Autowired
    private IPlusProgramApplication application;
    @Autowired
    private IPlusCommonAdminApi     commonAdminApi;
    @Autowired
    private IPlusOrderJobApi        jobApi;
    @Autowired
    private IPlusDetailApi          plusDetailApi;


    @Test
    public void test3() {
        /*CreateProgramEditPriceReq req = new CreateProgramEditPriceReq();
        req.setProgramId(15173);
        req.setLinePrice(new BigDecimal("200"));
        req.setPrice(new BigDecimal("100"));
        req.setExecuteTime(new Date());
        req.setOptId(1);
        req.setOptName("张剑锋");
        req.setOldPrice(new BigDecimal("1"));
        req.setOldLinePrice(new BigDecimal("11"));
        adminApi.addProgramEditPrice(req);*/
        /*PlusProgramEditQueryReq req = new PlusProgramEditQueryReq();
        req.setProgramId(15173);
        adminApi.getProgramEditPriceList(req);*/
        /*application.executeProgramEditPriceTask();*/
        /*CreatePlusBlackReq req = new CreatePlusBlackReq();
        req.setBlackType(1);
        req.setConfigId(1);
        req.setChannelId(1);
        req.setOptUserId(1);
        req.setOptUserName("张剑锋");
        req.setUserIds(Arrays.asList(1,2,3,4,5,6,7));
        commonAdminApi.batchSavePlusBlack(req);*/
        /*PlusBlackQueryReq req = new PlusBlackQueryReq();
        req.setBlackType(1);
        req.setPageNo(2);
        commonAdminApi.getPlusBlackList(req);*/
       /* DelPlusBlackReq req = new DelPlusBlackReq();
        req.setBlackId(104);
        req.setOptUserId(1);
        req.setOptUserName("张剑锋");
        commonAdminApi.deleteBlackById(req);*/
        CreatePlusBlackReq req = new CreatePlusBlackReq();
        req.setBlackType(1);
        req.setConfigId(8);
        req.setChannelId(1);
        req.setOptUserId(1);
        req.setOptUserName("张剑锋");
        req.setUserIds(Arrays.asList(321));
        commonAdminApi.batchDeleteBlack(req);

        /*commonAdminApi.getPlusBlackLogList(1);*/
        //jobApi.executePlusBlackTask(1, 1);
    }

    @Test
    public void test2() {
       /* CreatePlusDiscountConfigReq req = new CreatePlusDiscountConfigReq();
        req.setOptUserId(1);
        req.setOptUserName("张剑锋");
        req.setConfTag(1);
        req.setConfName("张剑锋测试2");
        req.setConfType(2);
        req.setConfigId(5);
        req.setDiscountRate(new BigDecimal("0.4"));
        req.setEffectiveTime(100);
        List<CreatePlusDiscountConditionReq> list = new ArrayList<>();
        CreatePlusDiscountConditionReq conditionReq = new CreatePlusDiscountConditionReq();
        conditionReq.setConditionField(
                DiscountConditionFieldEnum.ONE_YEAR_LOAN_SUCCESS_TIMES.getCode());
        conditionReq.setConditionKey(DiscountConditionKeyEnum.GT.getCode());
        conditionReq.setConditionVal("12");

        CreatePlusDiscountConditionReq conditionReq2 = new CreatePlusDiscountConditionReq();
        conditionReq2.setConditionField(
                DiscountConditionFieldEnum.ONE_YEAR_LOAN_SUCCESS_TIMES.getCode());
        conditionReq2.setConditionKey(DiscountConditionKeyEnum.LT.getCode());
        conditionReq2.setConditionVal("21");
        list.add(conditionReq);
        list.add(conditionReq2);
        req.setConditionList(list);
        commonAdminApi.addPlusDiscountConfig(req);*/

        /* commonAdminApi.getDiscountById(22);*/

        /*CreatePlusDiscountConfigReq req = new CreatePlusDiscountConfigReq();
        req.setId(22);
        req.setOptUserId(1);
        req.setOptUserName("张剑锋");
        req.setConfTag(1);
        req.setConfName("张剑锋测试2");
        req.setConfType(2);
        req.setConfigId(5);
        req.setDiscountRate(new BigDecimal("0.78"));
        req.setEffectiveTime(100);
        List<CreatePlusDiscountConditionReq> list = new ArrayList<>();
        CreatePlusDiscountConditionReq conditionReq = new CreatePlusDiscountConditionReq();
        conditionReq.setConditionField(
                DiscountConditionFieldEnum.TODAY_BROWSE_PLUS_ADVERT_TIME.getCode());
        conditionReq.setConditionKey(DiscountConditionKeyEnum.GT.getCode());
        conditionReq.setConditionVal("100");

        CreatePlusDiscountConditionReq conditionReq2 = new CreatePlusDiscountConditionReq();
        conditionReq2.setConditionField(
                DiscountConditionFieldEnum.TODAY_BROWSE_PLUS_ADVERT_TIME.getCode());
        conditionReq2.setConditionKey(DiscountConditionKeyEnum.LT.getCode());
        conditionReq2.setConditionVal("200");
        list.add(conditionReq);
        list.add(conditionReq2);
        req.setConditionList(list);
        commonAdminApi.editPlusDiscountConfig(req);*/
        /*EnablePlusDiscountConfigReq req = new EnablePlusDiscountConfigReq();
        req.setId(22);
        req.setConfState(0);
        req.setOptUserName("zjf");
        req.setOptUserId(1);
        commonAdminApi.enablePlusDiscountConfig(req);*/
        PlusDiscountConfigQueryReq req = new PlusDiscountConfigQueryReq();
        req.setConfigId(1);
        req.setConfType(1);
        commonAdminApi.getPlusDiscountList(req);
    }

    @Test
    public void tes3() {
        /*String str = "{\"id\":1,\"channelId\":888,\"plusConfigIds\":\"1,4\",\"plusChannelFunctions\":[{\"configId\":1,\"openMode\":\"1,3\",\"paySuccessUrl\":\"111\",\"plusProfit\":\"1,2,3,4\",\"pricingMode\":\"1,2\",\"payLaterDeduct\":1,\"delayReturnCard\":1,\"delayDays\":3,\"rapidReturnCard\":1,\"noteReach\":\"1,2\"},{\"configId\":4,\"openMode\":\"1,2,3,4\",\"paySuccessUrl\":\"\",\"plusProfit\":\"8,5,18,25\",\"pricingMode\":\"1,2\",\"payLaterDeduct\":0,\"delayReturnCard\":0,\"delayDays\":null,\"rapidReturnCard\":0,\"noteReach\":\"2,3\"}]}";
        EditChannelManagerReq req  = JSONObject.parseObject(str,EditChannelManagerReq.class);
        adminApi.editChannaManager(req);*/
        EnableChannelManageReq req = new EnableChannelManageReq();
        req.setId(1);
        req.setState(2);
        req.setOptUserId(1);
        req.setOptUserName("张剑锋");
        adminApi.enableChannelManage(req);
    }

    @Test
    public void test4() {
        /*String str = "{\"channel\":1,\"configId\":1,\"backstageName\":\"张剑锋测试无12221敌哈哈\",\"name\":\"张剑锋2121测22试无敌哈哈2\",\"programmeDays\":180,\"mallMobilePrice\":1,\"memberPrice\":120,\"effectiveTime\":\"2024-06-12 15:00:06\",\"showTime\":0,\"amountContent\":\"方案省钱文案\",\"marketContent\":\"方案营销文案\",\"showBuyRecord\":1,\"beSetRecovery\":1,\"recoveryImg\":\"https://test-mall-group.oss-cn-beijing.aliyuncs.com/240612/b1daf4eb-bb1f-4855-8ad2-572450789fb2.png\",\"showHomePage\":1,\"modelId\":\"8,1,2,3,4,6,12,5\",\"isRenew\":1,\"renewOpenTime\":100,\"isShowFrame\":1,\"frameType\":0,\"interval\":0,\"frameImage\":\"https://test-mall-group.oss-cn-beijing.aliyuncs.com/240612/3ba721aa-8c28-4d4a-b8e5-7fb9714289c8.png\",\"alternateProgram\":15158,\"userExplain\":\"<p>方案说明</p>\",\"userRules\":\"<p>权益规则</p>\",\"afterPayState\":1}";
        SavePlusProgramReq savePlusProgramReq = JSONObject.parseObject(str,
                SavePlusProgramReq.class);
        savePlusProgramReq.setOptUserId(1);
        savePlusProgramReq.setOptUserName("张剑锋");
        savePlusProgramReq.setRdSendType(1);
        savePlusProgramReq.setPopUp(1);
        savePlusProgramReq.setTwoPopUp(1);
        savePlusProgramReq.setRdChoose(1);
        savePlusProgramReq.setConfigId(10);
        adminApi.addProgram(savePlusProgramReq);*/

        String str = "{\"channel\":1,\"configId\":1,\"backstageName\":\"剑锋测试无敌哈哈\",\"name\":\"剑锋测试无敌哈哈2\",\"programmeDays\":360,\"mallMobilePrice\":108,\"memberPrice\":600,\"effectiveTime\":\"2024-06-12 15:00:06\",\"showTime\":0,\"amountContent\":\"方案省钱2文案\",\"marketContent\":\"方案2营销文案\",\"showBuyRecord\":1,\"beSetRecovery\":1,\"recoveryImg\":\"https://test-mall-group.oss-cn-beijing.aliyuncs.com/240612/b1daf4eb-bb1f-4855-8ad2-572450789fb2.png\",\"showHomePage\":1,\"modelId\":\"1,2,3,4,5,6\",\"isRenew\":1,\"renewOpenTime\":100,\"isShowFrame\":1,\"frameType\":0,\"interval\":0,\"frameImage\":\"https://test-mall-group.oss-cn-beijing.aliyuncs.com/240612/3ba721aa-8c28-4d4a-b8e5-7fb9714289c8.png\",\"alternateProgram\":15158,\"userExplain\":\"<p>方案说2明</p>\",\"userRules\":\"<p>权益2规则</p>\",\"afterPayState\":1,\"id\":15324,\"isRenewId\":169,\"programmeStatus\":0}";
        SavePlusProgramReq savePlusProgramReq = JSONObject.parseObject(str,
                SavePlusProgramReq.class);
        savePlusProgramReq.setOptUserId(1);
        savePlusProgramReq.setOptUserName("张剑锋");
        savePlusProgramReq.setRdSendType(2);
        savePlusProgramReq.setPopUp(2);
        savePlusProgramReq.setTwoPopUp(2);
        savePlusProgramReq.setRdChoose(2);
        savePlusProgramReq.setConfigId(10);
        adminApi.editProgram(savePlusProgramReq);

       /* adminApi.getProgramList(new ProgramQueryReq());
        SavePlusProgramReq req = new SavePlusProgramReq();
        req.setOptUserName("zjf ");
        req.setOptUserId(1);
        req.setId(15182);
        adminApi.programEffective(req);*/
    }

    @Test
    public void test5() {
        MultiplexChannelProgramReq req = new MultiplexChannelProgramReq();
        req.setOptUserId(1);
        req.setOptUser("hxf");
        req.setSourceChannelId(85003);
        req.setSourceConfigId(13);
        req.setTargetChannelId(888);
        req.setSourceProgramIds(Arrays.asList(15159));
        adminApi.multiplexChannelProgram(req);
    }

    @Test
    public void test6() {
        CopyProgramReq req = new CopyProgramReq();
        req.setOptUserId(1);
        req.setOptUser("hxf");
        req.setId(15338);
        adminApi.copyProgram(req);
    }

    @Test
    public void test9() {
        PlusAbyssResult<ProgramDetailResp> programDetail = adminApi.getProgramDetail(15338);
        System.out.println(JSONObject.toJSONString(programDetail.getResult()));
    }

    @Autowired
    private IPlusProgramExtendMapper extendMapper;

    @Test
    public void test7() {
        extendMapper.getProgramExtendByPid(1);
    }

    @Autowired
    private MemberProfitsQueryModel queryModel;

    @Autowired
    private IPlusProgramProductTypeMapper programProductTypeMapper;
    @Autowired
    private IPlusOrderJobApi  orderJobApi;


    @Test
    public void test8() {
        orderJobApi.repayPlanJob();
    }

    @Autowired
    private IPlusOrderShuntApplication shuntApplication;

    @Test
    public void test4444(){
        ShuntEvent event = new ShuntEvent();
        event.setUserId(7);
        event.setAmount(new BigDecimal("1"));
        event.setChannelId(1);
        event.setConfigId(1);
        shuntApplication.shuntSupplier(event);
    }

    @Test
    public void test10() {
        String str = "{\"channelId\":1,\"channelName\":\"商城\",\"configId\":12,\"diffList\":[{\"availableMinPrice\":1,\"availableMaxPrice\":100,\"promoteMinPrice\":1,\"promoteMaxPrice\":100,\"programId\":15251,\"loanRfmMinScore\":1,\"loanRfmMaxScore\":100,\"plusRfmMinScore\":1,\"plusRfmMaxScore\":100,\"marketReachMinScore\":1,\"marketReachMaxScore\":100,\"userLastMinNumber\":0,\"userLastMaxNumber\":99,\"memberUser\":true}]}";
        SaveDiffPriceReq req = JSONObject.parseObject(str, SaveDiffPriceReq.class);
        adminApi.addDiffPrice(req);
    }

    @Test
    public void test11() {
        String str = "{\"id\":24,\"channelId\":1,\"channelName\":\"商城\",\"configId\":12,\"diffList\":[{\"availableMinPrice\":1,\"availableMaxPrice\":100,\"promoteMinPrice\":1,\"promoteMaxPrice\":100,\"programId\":15251,\"loanRfmMinScore\":2,\"loanRfmMaxScore\":200,\"plusRfmMinScore\":2,\"plusRfmMaxScore\":200,\"marketReachMinScore\":2,\"marketReachMaxScore\":200,\"userLastMinNumber\":2,\"userLastMaxNumber\":98,\"memberUser\":false}]}";
        SaveDiffPriceReq req = JSONObject.parseObject(str, SaveDiffPriceReq.class);
        adminApi.editDiffPrice(req);
    }

    @Test
    public void test12() {
        PlusAbyssResult<DiffPriceDetailResp> diffPriceDetail = adminApi.getDiffPriceDetail(24);
        System.out.println(
                "查询差异化详情:" + JSONObject.toJSONString(diffPriceDetail.getResult()));
    }

}
