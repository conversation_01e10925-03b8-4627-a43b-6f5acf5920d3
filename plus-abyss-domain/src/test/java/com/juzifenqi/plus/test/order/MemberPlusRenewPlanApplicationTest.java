package com.juzifenqi.plus.test.order;

import com.juzifenqi.plus.module.order.application.IMemberPlusRenewPlanApplication;
import com.juzifenqi.plus.module.order.model.contract.IMemberPlusRenewPlanRepository;
import com.juzifenqi.plus.module.order.model.event.MemberPlusRenewPlanCreateEvent;
import com.juzifenqi.plus.test.AbstractTest;
import com.juzifenqi.plus.test.utils.TestDataUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
public class MemberPlusRenewPlanApplicationTest extends AbstractTest {

    @Autowired
    private IMemberPlusRenewPlanApplication memberPlusRenewPlanApplication;

    @Test
    public void generateTest() {
        MemberPlusRenewPlanCreateEvent event = TestDataUtils
                .readJsonFromFile("order/memberPlusRenewPlanCreateEvent.json",
                        MemberPlusRenewPlanCreateEvent.class);
        memberPlusRenewPlanApplication.generate(event);
    }

}
