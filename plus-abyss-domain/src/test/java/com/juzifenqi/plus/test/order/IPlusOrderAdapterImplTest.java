package com.juzifenqi.plus.test.order;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.module.order.adapter.IPlusOrderAdapter;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderPayResultEntity;
import com.juzifenqi.plus.test.AbstractTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2024/9/5
 */

public class IPlusOrderAdapterImplTest extends AbstractTest {

    @Autowired
    private IPlusOrderAdapter adapter;

    @Test
    public void testQueryPayRecord() {
        OrderPayResultEntity orderPayResultEntity = adapter.queryPayRecord("123123");
        System.out.println(JSON.toJSONString(orderPayResultEntity));
    }
}
