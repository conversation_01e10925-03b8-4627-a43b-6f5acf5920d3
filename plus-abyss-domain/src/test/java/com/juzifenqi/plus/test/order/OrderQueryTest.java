package com.juzifenqi.plus.test.order;

import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IMemberOrderQueryApi;
import com.juzifenqi.plus.api.IPlusResubmitGroupApi;
import com.juzifenqi.plus.dto.req.order.RdzxOrderCostQueryReq;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 订单查询测试
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/2 14:46
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class OrderQueryTest {

    @Autowired
    private IMemberOrderQueryApi queryApi;
    @Autowired
    private IPlusResubmitGroupApi groupApi;


    @Test
    public void test1() {
        RdzxOrderCostQueryReq req = new RdzxOrderCostQueryReq();
        req.setPlusOrderSn("992401171627050186");
        queryApi.getRdzxOrderCost(req);
    }

    @Test
    public void test2() {
        RdzxOrderCostQueryReq req = new RdzxOrderCostQueryReq();
        req.setUserId(7);
        queryApi.getRdzxCost(req);
    }

    @Test
    public void test3() {
        groupApi.getRecord("24070217011266521136");
    }
}
