package com.juzifenqi.plus.test.repository;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.entity.DefrayQueryV2RespEntity;
import com.juzifenqi.plus.module.common.entity.RefundRespEntity;
import com.juzifenqi.plus.test.AbstractTest;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @Date 2024/9/7
 */
public class FmsRepositoryTest extends AbstractTest {

    @Autowired
    private IFmsRepository fmsRepository;

    @Test
    public void testForQueryDefrayV2(){
        DefrayQueryV2RespEntity defrayQueryV2RespEntity = fmsRepository.queryDefrayV2(
                "901831952738767216726", null);
        System.out.println(JSON.toJSONString(defrayQueryV2RespEntity));

        RefundRespEntity refundRespEntity = fmsRepository.queryRefundResultV2("1725531271774753004",
                null);

        System.out.println(JSON.toJSONString(refundRespEntity));


        DefrayQueryV2RespEntity defrayQueryV2RespEntity2 = fmsRepository.queryDefrayV2(
                null, "121831952740776288259");
        System.out.println(JSON.toJSONString(defrayQueryV2RespEntity2));

        RefundRespEntity refundRespEntity2 = fmsRepository.queryRefundResultV2(null,
                "R1831897337484943380");
        System.out.println(JSON.toJSONString(refundRespEntity2));
    }

}
