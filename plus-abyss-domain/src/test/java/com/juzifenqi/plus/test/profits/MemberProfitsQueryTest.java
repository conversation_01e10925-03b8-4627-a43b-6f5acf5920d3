package com.juzifenqi.plus.test.profits;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IMemberProfitQueryApi;
import com.juzifenqi.plus.dto.req.member.MemberCouponSendInfoReq;
import com.juzifenqi.plus.dto.req.member.MemberCouponSendInfoReq.CouponQueryInfo;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.member.MemberCouponSendInfoResp;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 用户权益查询api测试
 *
 * <AUTHOR>
 * @date 2024/8/16 15:50
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class MemberProfitsQueryTest {

    @Autowired
    private IMemberProfitQueryApi queryApi;

    @Test
    public void test7() {
        MemberCouponSendInfoReq req = new MemberCouponSendInfoReq();
        req.setUserId(310118162);
        List<CouponQueryInfo> list = new ArrayList<>();
        CouponQueryInfo one = new CouponQueryInfo();
        one.setCouponUserId(1);
        one.setSendCouponTime("2024-08-14 14:14:00");
        list.add(one);
        CouponQueryInfo two = new CouponQueryInfo();
        two.setCouponNo("shangcheng202404151100502404153724031802");
        two.setSendCouponTime("2024-08-16 14:15:00");
        list.add(two);
        req.setCouponList(list);
        PlusAbyssResult<List<MemberCouponSendInfoResp>> result = queryApi.getCouponSendInfoList(
                req);
        System.out.println("查询会员优惠券信息:" + JSONObject.toJSONString(result));
    }

}
