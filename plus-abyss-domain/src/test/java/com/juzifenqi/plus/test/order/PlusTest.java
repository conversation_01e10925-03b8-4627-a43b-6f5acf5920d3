package com.juzifenqi.plus.test.order;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IOrderDefrayApi;
import com.juzifenqi.plus.api.IPlusCashBackApi;
import com.juzifenqi.plus.api.IPlusLogApi;
import com.juzifenqi.plus.api.IPlusMarketApi;
import com.juzifenqi.plus.api.IPlusOrderApi;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.api.IPlusProfitSendPlanApi;
import com.juzifenqi.plus.api.IPlusRefundRecordApi;
import com.juzifenqi.plus.api.admin.IPlusOrderAdminApi;
import com.juzifenqi.plus.api.admin.IPlusOrderBatchCancelApi;
import com.juzifenqi.plus.api.admin.IPlusProfitsAdminApi;
import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.dto.req.CancelRenewReq;
import com.juzifenqi.plus.dto.req.CreateDefrayApplyReq;
import com.juzifenqi.plus.dto.req.CreateRefundRecordReq;
import com.juzifenqi.plus.dto.req.DefrayPayCallbackReq;
import com.juzifenqi.plus.dto.req.OrderRefundExecuteReq;
import com.juzifenqi.plus.dto.req.PlusOrderCancelReq;
import com.juzifenqi.plus.dto.req.PlusOrderCreateReq;
import com.juzifenqi.plus.dto.req.PlusPayCallbackReq;
import com.juzifenqi.plus.dto.req.PlusRenewInfoReq;
import com.juzifenqi.plus.dto.req.RefundExecuteReq;
import com.juzifenqi.plus.dto.req.UpdOrderStateReq;
import com.juzifenqi.plus.dto.req.VirtualGoodsOrderCreateReq;
import com.juzifenqi.plus.dto.req.VirtualProductRechargeReq;
import com.juzifenqi.plus.dto.req.admin.PlusRefundBatchCancelReq;
import com.juzifenqi.plus.dto.req.market.PlusBillListMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusLeadPageMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusLoanConfirmMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusLoanConfirmRdzxMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusOrderListMarketReq;
import com.juzifenqi.plus.dto.req.market.PlusPaySuccessMarketReq;
import com.juzifenqi.plus.dto.req.profits.CreatePlusProModelReq;
import com.juzifenqi.plus.dto.req.profits.CreatePlusProgramLmkVirtualReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.PlusOptLogResp;
import com.juzifenqi.plus.dto.resp.PlusOrderCancelResp;
import com.juzifenqi.plus.dto.resp.PlusOrderCreateResp;
import com.juzifenqi.plus.dto.resp.PlusRenewInfoResp;
import com.juzifenqi.plus.dto.resp.VirtualGoodsOrderCreateResp;
import com.juzifenqi.plus.dto.resp.market.PlusBillListMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLeadPageMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLoanConfirmMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusLoanConfirmRdzxMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusOrderListMarketResp;
import com.juzifenqi.plus.dto.resp.market.PlusPaySuccessMarketResp;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusLogBusinessTypeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.enums.PlusSwitchEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusInfoApplication;
import com.juzifenqi.plus.module.asserts.model.MemberProfitsQueryModel;
import com.juzifenqi.plus.module.asserts.model.event.PlusMemberCardOpenEvent;
import com.juzifenqi.plus.module.common.IShortUrlExternalRepository;
import com.juzifenqi.plus.module.common.repository.external.acl.feishu.FeiShuModel;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderDefrayApplication;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import javax.annotation.Resource;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;


@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlusTest {

    @Autowired
    private IMemberPlusInfoApplication memberCardApplication;
    @Autowired
    private IPlusProgramQueryModel     programQueryModel;
    @Autowired
    private IPlusOrderApi              plusOrderApi;
    @Autowired
    private IPlusOrderBatchCancelApi   batchCancelApi;
    @Autowired
    private IShortUrlExternalRepository shortUrlExternalRepository;
    @Autowired
    private IPlusOrderAdminApi orderAdminApi;

    @Test
    public void openCardTest() {
        PlusMemberCardOpenEvent event = new PlusMemberCardOpenEvent();
        event.setUserId(*********);
        event.setChannelId(1);
        event.setPlusOrderSn("992405151555240847");
        event.setProgramEntity(programQueryModel.getById(15307));
        PlusOrderEntity order = new PlusOrderEntity();
        order.setChannelId(1);
        order.setProgramId(15307);
        order.setUserId(*********);
        order.setConfigId(10);
        order.setProgramName("融担卡二期测试");
        event.setOrderEntity(order);
        memberCardApplication.openCard(event);
    }

    @Test
    public void shortUrlTest(){
        String shortUrl = "https://test1-mall.juzishuke.com/vip/rd/index.html?plusOrderSn=992405151555240847";
        shortUrlExternalRepository.generateShortUrl(shortUrl);
    }

    @Test
    public void createOrderTest() {
        PlusOrderCreateReq req = new PlusOrderCreateReq();
        req.setChannelId(1);
        req.setUserId(310117982);
        req.setPayType(PlusOrderPayTypeEnum.PAY_AFTER.getValue());
        req.setProgramId(15251);
        CreateOrderContext context = new CreateOrderContext();
        context.setLoanOrderSn("24070609303857317982");
        context.setRelationBusinessType(1);
        req.setCreateOrderContext(context);
        PlusAbyssResult<PlusOrderCreateResp> order = plusOrderApi.createOrder(req);
        System.out.println(JSON.toJSONString(order));
    }

    @Test
    public void payCallbackTest() {
        PlusPayCallbackReq req = new PlusPayCallbackReq();
        req.setFlag(1);
        req.setMemberId(310115378);
        req.setPayStatus("S");
        req.setOrderSn("992406271501178299");
        plusOrderApi.payCallBack(req);
    }

    @Test
    public void testRpcOrder() {
        CancelRenewReq event = new CancelRenewReq();
        event.setOrderSn("992401151404527678");
        event.setRemark("测试取消");
        event.setUserId(310113666);
        event.setChannelId(1);
        event.setFlag(2);
        plusOrderApi.cancelRenew(event);
    }

    @Autowired
    private MemberProfitsQueryModel memberProfitsQueryModel;

    @Test
    public void renewTest() {
        PlusRenewInfoReq req = new PlusRenewInfoReq();
        req.setUserId(310113666);
        req.setConfigId(12);
        req.setChannelId(1);
        req.setOrderSn("992309221044327494");
        PlusAbyssResult<PlusRenewInfoResp> renewInfo = plusOrderApi.getRenewInfo(req);
        System.out.println(JSON.toJSONString(renewInfo));
    }

    @Autowired
    private FeiShuModel feiShuModel;

    @Test
    public void testFeishu() {
        feiShuModel.sendTextMsg("1111");
    }

    @Autowired
    private IPlusLogApi iPlusLogApi;

    @Test
    public void testLog() {
        PlusAbyssResult<List<PlusOptLogResp>> logList = iPlusLogApi.getLogList(
                "23060113583438514305", PlusLogBusinessTypeEnum.CONTRACT_UPLOAD.getCode());
        System.out.println(JSON.toJSONString(logList));
    }

    @Autowired
    private IPlusCashBackApi plusCashBackApi;

    @Test
    public void test222O() {
        DefrayPayCallbackReq req = new DefrayPayCallbackReq();
        req.setPayStatus(3);
        req.setOrderSn("1");
        req.setFailReason("哈哈");
        req.setSerialNumber("992311201648567867");
        plusCashBackApi.defrayPayCallback(req);
    }


    @Test
    public void createVirOrderTest() {
        PlusOrderCancelReq req = new PlusOrderCancelReq();
        req.setPlusOrderSn("992401221422187298");
        req.setRemark("我是测试备注");
        req.setCancelReason(1);
        req.setCancelType(PlusCancelTypeEnum.CONDITION.getValue());
        req.setOptUserId(1);
        req.setOptUserName("hah");
        PlusAbyssResult<PlusOrderCancelResp> result = plusOrderApi.checkPreCancel(req);
        System.out.println(JSON.toJSONString(result));
    }

    @Autowired
    private IOrderDefrayApi orderDefrayApi;

    @Test
    public void cancelExpireOrderTest() {
        CreateDefrayApplyReq req = new CreateDefrayApplyReq();
        req.setCardId(1111);
        req.setOptId(1);
        req.setOptName("刘天奇");
        req.setOrderSn("992408271433241734");
        req.setRatio(new BigDecimal("1"));
        req.setCtCardNo("fewfwefwefw");
        req.setCustomerName("ewfewfewfwefw");
        req.setCancelType(PlusCancelTypeEnum.NO_EXPIRE_PAYMENT.getValue());
        req.setCancelReason(1);
        orderDefrayApi.createDefrayApply(req);
    }

    @Test
    public void cancelOrderTest() {
        // 取消订单
        PlusOrderCancelReq cancelEvent = new PlusOrderCancelReq();
        cancelEvent.setCancelReason(1);
        cancelEvent.setRemark("全新测试");
        cancelEvent.setPlusOrderSn("992408011625288829");
        cancelEvent.setOptUserId(1);
        cancelEvent.setOptUserName("刘天奇");
        cancelEvent.setCancelType(PlusCancelTypeEnum.CONDITION.getValue());
        PlusAbyssResult<PlusOrderCancelResp> result = plusOrderApi.cancelOrder(cancelEvent);
        System.out.println(JSON.toJSONString(result));
    }

    @Autowired
    private IPlusOrderApplication orderApplication;

    @Test
    public void cancelOrderNotifyTest() {
        OrderRefundNotifyEntity entity = new OrderRefundNotifyEntity();
        entity.setStatus("S");
        entity.setThirdPayNum("YLT9924100820204505011728391346975");
        entity.setSerialNumber("R1843633086580277293");
        entity.setRefundType(0);
        entity.setOrderId("992410082020450501");
        entity.setRefundAmount(new BigDecimal("19.9"));
        orderApplication.orderRefundNotify(entity);
    }

    @Autowired
    private IPlusOrderDefrayApplication orderDefrayApplication;

    @Test
    public void orderDefrayTest() {
        orderDefrayApplication.orderDefrayExecute();
    }

    @Test
    public void setOrderDefrayNotifyTest() {
        OrderRefundNotifyEntity entity = new OrderRefundNotifyEntity();
        entity.setStatus("S");
        entity.setRefundAmount(new BigDecimal("49.90"));
        entity.setThirdPayNum("DFTK9924082714332417341725959011709");
        entity.setSerialNumber("121833452279798571060");
        orderDefrayApplication.changeCardDefrayPayRefund(entity);
    }

    @Test
    public void fewfwef() {
        UpdOrderStateReq event = new UpdOrderStateReq();
        event.setChangeReasonCode(1);
        event.setOrderState(3);
        event.setOptUserId(1);
        event.setOptUserName("大大大");
        event.setOrderSn("992312151307449724");
        event.setRefundAmount(BigDecimal.ONE);
        plusOrderApi.updOrderStateByCustomer(event);
    }

    @Autowired
    private IPlusOrderJobApi plusOrderJobApi;

    @Test
    public void testfefer() {
        OrderRefundExecuteReq req = new OrderRefundExecuteReq();
        req.setDealState(1);
        req.setSize(1);
        req.setChannelId(1);
        plusOrderJobApi.orderRefundExecute(req);
    }

    @Autowired
    private IPlusRefundRecordApi refundRecordApi;

    @Test
    public void refundRecordApiTest() {
        CreateRefundRecordReq req = new CreateRefundRecordReq();
        req.setChannelId(1);
        req.setUserId(7);
        req.setConfigId(1);
        req.setCancelType(PlusCancelTypeEnum.DELAY.getValue());
        req.setOrderStatus(502);
        req.setLoanOrderSn("21212121212121");
        req.setSwitchCode(PlusSwitchEnum.ENJOY_SPEED_REFUND.getCode());
        refundRecordApi.createRefundRecord(req);
    }

    @Test
    public void delayJobTest() {
        //plusOrderJobApi.delayRefundExecute(1);
        RefundExecuteReq req = new RefundExecuteReq();
        req.setOptState(0);
        req.setSize(1);
        plusOrderJobApi.refundExecute(req);
    }

    @Test
    public void renewJobTest() {
        String str = "[{\"id\":1962,\"sort\":1},{\"id\":1963,\"sort\":19},{\"id\":1964,\"sort\":20}]";
        plusProfitsAdminApi.editPlusProModelSort(
                JSONObject.parseArray(str, CreatePlusProModelReq.class));
    }

    @Test
    public void batchCancelWorkTest() {
        PlusRefundBatchCancelReq req = new PlusRefundBatchCancelReq();
        req.setOptUserId(2);
        req.setOptUserName("hxf");
        req.setBatchNo("2");
        req.setOrderSns(Arrays.asList("992312281425067843"));
        PlusAbyssResult<Boolean> resp = batchCancelApi.batchCancelOrdersWork(req);
        System.out.println("resp:" + (resp.getSuccess() ? "调用成功" : resp.getMessage()));
    }

    @Autowired
    private IPlusProfitsAdminApi plusProfitsAdminApi;

    @Test
    public void batchCancelTest() {
        plusOrderJobApi.invalidOrder();
    }

    @Autowired
    private IPlusMarketApi plusMarketApi;

    @Test
    public void batchCancelListTest() {
        CreatePlusProgramLmkVirtualReq req = new CreatePlusProgramLmkVirtualReq();
        req.setUpdateUserName("11");
        req.setUpdateUserId(1);
        req.setVirtualStatus(1);
        req.setSku("n451WWe000");
        plusProfitsAdminApi.updateVirtualState(req);
    }

    @Test
    public void paySuccessMarketTest() {
        PlusPaySuccessMarketReq req = new PlusPaySuccessMarketReq();
        req.setUserId(7);
        req.setChannelId(1);
        PlusAbyssResult<PlusPaySuccessMarketResp> result = plusMarketApi.paySuccessMarket(req);
        System.out.println(JSON.toJSONString(result));
    }

    @Test
    public void orderListMarketTest() {
        PlusOrderListMarketReq req = new PlusOrderListMarketReq();
        req.setUserId(7);
        req.setChannelId(1);
        PlusAbyssResult<PlusOrderListMarketResp> result = plusMarketApi.orderListMarket(req);
        System.out.println("订单列表页-加速卡营销结果:" + JSON.toJSONString(result));
    }

    @Test
    public void billListMarketTest() {
        PlusBillListMarketReq req = new PlusBillListMarketReq();
        req.setUserId(7);
        req.setChannelId(1);
        PlusAbyssResult<PlusBillListMarketResp> result = plusMarketApi.billListMarket(req);
        System.out.println("账单列表页-还款卡营销结果:" + JSON.toJSONString(result));
    }

    @Test
    public void loanConfirmMarketTest() {
        PlusLoanConfirmRdzxMarketReq req = new PlusLoanConfirmRdzxMarketReq();
        req.setUserId(7);
        req.setChannelId(1);
        req.setLoanAmount(new BigDecimal("50000"));
        req.setLoanRate("2:0.24,4:0.36");
        PlusAbyssResult<PlusLoanConfirmRdzxMarketResp> result = plusMarketApi.loanConfirmRdzxMarket(req);
        System.out.println("确认借款页-融担卡营销结果:" + JSON.toJSONString(result));
    }

    @Test
    public void leadPageMarketTest() {
        PlusLeadPageMarketReq req = new PlusLeadPageMarketReq();
        req.setUserId(7);
        req.setChannelId(1);
        PlusAbyssResult<PlusLeadPageMarketResp> result = plusMarketApi.leadPageMarket(req);
        System.out.println("导流页-桔省卡营销结果:" + JSON.toJSONString(result));
    }

    @Test
    public void loanPageMarketTest() {
        PlusLoanConfirmMarketReq req = new PlusLoanConfirmMarketReq();
        req.setUserId(7);
        req.setChannelId(1);
        PlusAbyssResult<PlusLoanConfirmMarketResp> result = plusMarketApi.loanConfirmMarket(req);
        System.out.println("借款首页营销结果:" + JSON.toJSONString(result));
    }

    @Test
    public void createVirtualOrderTest() {
        VirtualGoodsOrderCreateReq req = new VirtualGoodsOrderCreateReq();
        req.setUserId(*********);
        req.setChannelId(1);
        req.setPlusOrderSn("992405151555240847");
        req.setRechargeAccount("***********");
        req.setModelId(PlusModelEnum.LYFF.getModelId());
        PlusAbyssResult<VirtualGoodsOrderCreateResp> result = plusOrderApi.createVirtualGoodsOrder(
                req);
        System.out.println("创建权益0元发放虚拟权益订单:" + JSON.toJSONString(result));
    }

    @Resource
    private IPlusProfitSendPlanApi sendPlanApi;

    @Test
    public void test111() {
//        CancelRdzxOrderReq req = new CancelRdzxOrderReq();
//        req.setOptId(1);
//        req.setOptName("e12e");
//        req.setOrderSn("992310231407025634");
//        rdzxApi.cancelOrder(req);
        VirtualProductRechargeReq req = new VirtualProductRechargeReq();
        req.setChannelId(1);
        req.setUserId(1);
        req.setOrderSn("992406281447486020");
        req.setRechargeAccount("111111");
        req.setProgramId(1);
        sendPlanApi.lmkVirtualRecharge(req);
    }
}
