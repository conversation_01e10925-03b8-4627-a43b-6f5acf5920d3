package com.juzifenqi.plus.test.cashback;

import com.juzifenqi.plus.api.IPlusCashBackApi;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.dto.req.DefrayPayReq;
import com.juzifenqi.plus.dto.req.QueryCashBackRecordReq;
import com.juzifenqi.plus.dto.resp.CashbackRecordPageResp;
import com.juzifenqi.plus.dto.resp.MemberPlusCashBackRecordResp;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.module.order.model.contract.IMemberPlusRenewPlanRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberPlusRenewPlanEntity;
import com.juzifenqi.plus.test.AbstractTest;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;


@Slf4j
public class IPlusCashBackApiTest extends AbstractTest {


    @Autowired
    private IPlusCashBackApi plusCashBackApi;

    @Autowired
    private IPlusOrderJobApi jobApi;

    @Test
    public void divideCashBackListTest() {
        QueryCashBackRecordReq param = new QueryCashBackRecordReq();
        PlusAbyssResult<List<CashbackRecordPageResp>> abyssResult = plusCashBackApi.divideCashBackList(
                param);
        System.out.println(abyssResult.getResult());
    }

    @Test
    public void defrayPayTest() {
        DefrayPayReq defrayPayReq = new DefrayPayReq();
        PlusAbyssResult<Boolean> abyssResult = plusCashBackApi.defrayPay(defrayPayReq);
        System.out.println(abyssResult.getResult());
    }

    @Test
    public void getRecordListTest() {
        PlusAbyssResult<List<MemberPlusCashBackRecordResp>> recordListByOrderSn = plusCashBackApi.getRecordListByOrderSn(
                "345");
        System.out.println(recordListByOrderSn.getResult());
    }

    @Autowired
    private IMemberPlusRenewPlanRepository renewPlanRepository;

    @Test
    public void aa() {
        String orderSn = "992309191808596782";
        MemberPlusRenewPlanEntity byPlusOrderSn = renewPlanRepository.getByPlusOrderSn(
                orderSn);
        System.out.println(byPlusOrderSn.getGroupId().equals(orderSn));
    }

    @Test
    public void microDefray() {
        jobApi.microDefrayPayExecute();
    }

    @Test
    public void hkfxMicroDefrayPayJob() {
        jobApi.hkfxMicroDefrayPayJob();
    }
}
