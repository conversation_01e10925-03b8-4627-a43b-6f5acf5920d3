package com.juzifenqi.plus.test.sendPlan;

import com.juzifenqi.plus.api.IPlusOrderApi;
import com.juzifenqi.plus.dto.pojo.CreateOrderContext;
import com.juzifenqi.plus.dto.req.PlusOrderCreateReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.PlusOrderCreateResp;
import com.juzifenqi.plus.enums.PlusOrderPayTypeEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusCashbackRecordRepository;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoDetailRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusMemberCardEntity;
import com.juzifenqi.plus.module.asserts.model.converter.IPlusMemberCardModelConverter;
import com.juzifenqi.plus.module.asserts.model.event.MemberPlusSendPlanCreateEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.LoanOrderFinishEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.PlusOrderPayFinishEvent;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.test.AbstractTest;
import com.juzifenqi.plus.test.utils.TestDataUtils;
import java.util.Date;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
public class MemberPlusSendPlanApplicationTest extends AbstractTest {

    @Autowired
    private IMemberPlusSendPlanApplication memberPlusSendPlanApplication;

    @Autowired
    private IPlusProgramQueryModel IPlusProgramQueryModel;

    @Autowired
    private IPlusOrderApi                   plusOrderApi;
    @Autowired
    private IMemberPlusInfoDetailRepository memberPlusInfoDetailRepository;
    private IPlusMemberCardModelConverter   converter = IPlusMemberCardModelConverter.instance;

    @Test
    public void generateMemberProfitsSendPlanTest() {
        MemberPlusSendPlanCreateEvent event = new MemberPlusSendPlanCreateEvent();
        event.setChannelId(1);
        event.setUserId(7);
        PlusMemberCardEntity result = new PlusMemberCardEntity();
        result.setOrderSn("992309132033545160");
        result.setChannelId(1);
        result.setUserId(7);
        result.setConfigId(12);
        result.setMemberPlusId(1);
        result.setStartTime(new Date());
        result.setEndTime(DateUtils.addDays(new Date(), 30));
        event.setPlusMemberCard(result);
        event.setProgramEntity(IPlusProgramQueryModel.getById(15144));
        memberPlusSendPlanApplication.generateMemberProfitsSendPlan(event);
    }

    @Test
    public void addPLusOrderPayConditionReachEventTest() {
        PlusOrderPayFinishEvent event = TestDataUtils.readJsonFromFile(
                "asserts/plusOrderPayConditionReachEvent.json", PlusOrderPayFinishEvent.class);
        memberPlusSendPlanApplication.addConditionReachEvent(event);
    }


    @Test
    public void conditionTest() {
        Integer userId = ((Long) System.currentTimeMillis()).intValue();
        String loanOrderSn = "loan" + System.currentTimeMillis();
        // 1. 创单
        String plusOrderSn = createOrder(userId, loanOrderSn);
        // 2. 支付成功回调
        paySuccess(userId, plusOrderSn);
        // 3.发放计划条件达成_会员单支付成功
        addPlusOrderPayFinishEvent(userId, plusOrderSn);
        // 4.发放计划条件达成_借款单结清
        addLoanFinishEvent(plusOrderSn);
    }

    private void addLoanFinishEvent(String plusOrderSn) {
        LoanOrderFinishEvent loanOrderFinishEvent = new LoanOrderFinishEvent();
        loanOrderFinishEvent.setLoanOrderSn(plusOrderSn);
        memberPlusSendPlanApplication.addConditionReachEvent(loanOrderFinishEvent);
    }

    private void addPlusOrderPayFinishEvent(Integer userId, String plusOrderSn) {
        PlusOrderPayFinishEvent event = TestDataUtils.readJsonFromFile(
                "asserts/plusOrderPayConditionReachEvent.json", PlusOrderPayFinishEvent.class);
        event.setPlusOrderSn(plusOrderSn);
        event.setUserId(userId);
        memberPlusSendPlanApplication.addConditionReachEvent(event);
    }


    private String createOrder(Integer userId, String loanOrderSn) {
        PlusOrderCreateReq req = new PlusOrderCreateReq();
        req.setChannelId(1);
        req.setUserId(userId);
        req.setPayType(PlusOrderPayTypeEnum.PAY_ALL.getValue());
        req.setProgramId(15144);
        CreateOrderContext createOrderContext = new CreateOrderContext();
        createOrderContext.setLoanOrderSn(loanOrderSn);
        createOrderContext.setRelationBusinessType(1);
        req.setCreateOrderContext(createOrderContext);
        PlusAbyssResult<PlusOrderCreateResp> order = plusOrderApi.createOrder(req);
        return order.getResult().getOrderSn();
    }

    private void paySuccess(Integer userId, String plusOrderSn) {

    }


    @Autowired
    private IMemberPlusCashbackRecordRepository memberPlusCashbackRecordRepository;

    @Test
    public void aa() {
      /*  MemberPlusCashbackRecordEntity byTransactionId = memberPlusCashbackRecordRepository.getByTransactionId(
                "1");
        byTransactionId.setTransactionId("2");
        Integer save = memberPlusCashbackRecordRepository.save(byTransactionId);*/
        memberPlusCashbackRecordRepository.updateRecordPayStatus(2, 2, "1fewfew1");
    }
}
