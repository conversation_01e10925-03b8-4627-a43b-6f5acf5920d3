package com.juzifenqi.plus.test.order;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.api.IPlusOrderApi;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.PlusOrderPayInfoResp;
import com.juzifenqi.plus.enums.RenewStateEnum;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.ao.PlusOrderPayInfoAo;
import com.juzifenqi.plus.module.order.model.MemberPlusRenewPlanQueryModel;
import com.juzifenqi.plus.module.order.model.contract.entity.MemberPlusRenewPlanEntity;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderRelationCreateEvent;
import com.juzifenqi.plus.test.AbstractTest;
import com.juzifenqi.plus.test.utils.TestDataUtils;
import java.util.List;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
public class PlusOrderApplicationTest extends AbstractTest {

    @Autowired
    private IPlusOrderApplication plusOrderApplication;

    @Autowired
    private MemberPlusRenewPlanQueryModel memberPlusRenewPlanQueryModel;
    @Autowired
    private IPlusOrderApi plusOrderApi;

    @Test
    public void buildOrderRelationTest_1() {
        // 首单的绑定
        PlusOrderRelationCreateEvent event = TestDataUtils
                .readJsonFromFile("order/plusOrderRelationCreateEvent_1.json",
                        PlusOrderRelationCreateEvent.class);
        plusOrderApplication.buildOrderRelation(event);
    }

    @Test
    public void buildOrderRelationTest_2() {
        // 续费单的绑定
        PlusOrderRelationCreateEvent event = TestDataUtils
                .readJsonFromFile("order/plusOrderRelationCreateEvent_2.json",
                        PlusOrderRelationCreateEvent.class);
        plusOrderApplication.buildOrderRelation(event);
    }

    @Test
    public void createPlusOrderByRenewPlanTest() {
        List<MemberPlusRenewPlanEntity> renewPlans = memberPlusRenewPlanQueryModel
                .listByRenewStatus(RenewStateEnum.TO_BE_RENEW.getCode(), 1, 111111);
        plusOrderApplication.createPlusOrderByRenewPlans(renewPlans);
    }

    @Test
    public void getOrderPayInfo() {
        PlusAbyssResult<PlusOrderPayInfoResp> result = plusOrderApi.getOrderPayInfo("992305161746205721");
        System.out.println(JSONObject.toJSONString(result));
//        PlusOrderPayInfoAo ao = plusOrderApplication.getOrderPayInfo("992305161746205721");
//        System.out.println(JSONObject.toJSONString(ao));
    }
}
