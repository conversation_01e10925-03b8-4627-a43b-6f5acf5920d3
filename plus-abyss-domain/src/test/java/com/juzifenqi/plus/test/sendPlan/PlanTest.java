package com.juzifenqi.plus.test.sendPlan;

import com.alibaba.fastjson.JSON;
import com.groot.utils.core.date.DateUtils;
import com.juzifenqi.coupon.api.common.CouponQueryApi;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusDetailApi;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.api.IPlusProfitSendPlanApi;
import com.juzifenqi.plus.dto.req.MemberPlusProfitSendPlanQueryReq;
import com.juzifenqi.plus.dto.req.ReceiveCouponReq;
import com.juzifenqi.plus.dto.req.detail.ProfitsMergeDetailReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.ReceiveCouponResultResp;
import com.juzifenqi.plus.dto.resp.detail.profit.ProfitMergeDetailResp;
import com.juzifenqi.plus.enums.CouponTypeEnum;
import com.juzifenqi.plus.enums.PlusSendPlanStatusEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusMemberCardEntity;
import com.juzifenqi.plus.module.asserts.model.event.MemberPlusSendPlanCreateEvent;
import com.juzifenqi.plus.module.asserts.model.event.QueryProfitUsedEvent;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleResetProfitEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.GwfxCancelEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.GwfxFinishEvent;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.DmdsTaskFinishEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo;
import com.juzifenqi.plus.module.order.repository.dao.IPlusCustomerGroupRecordMapper;
import com.juzifenqi.plus.module.order.repository.po.PlusCustomerGroupRecordPo;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlanTest {

    @Autowired
    private IPlusProgramQueryModel         plusProgramQueryModel;
    @Autowired
    private IMemberPlusSendPlanApplication profitsSendPlanApplication;
    @Autowired
    private IPlusProfitSendPlanApi         plusProfitSendPlanApi;
    @Autowired
    private IPlusCustomerGroupRecordMapper customerGroupRecordMapper;
    @Autowired
    private IPlusDetailApi plusDetailApi;

    @Test
    public void test() {
        ProfitsMergeDetailReq req = new ProfitsMergeDetailReq();
        req.setChannelId(1);
        req.setUserId(310115378);
        PlusAbyssResult<ProfitMergeDetailResp> profitMergeDetail = plusDetailApi.getProfitMergeDetail(
                req);

        System.out.println(JSON.toJSONString(profitMergeDetail));
    }

    @Autowired
    private CouponQueryApi couponQueryApi;

    @Test
    public void test2() {
        //couponQueryApi.getCouponById(1);
        MemberPlusSendPlanCreateEvent createEvent = new MemberPlusSendPlanCreateEvent();
        createEvent.setUserId(7);
        createEvent.setChannelId(1);
        createEvent.setProgramEntity(
                plusProgramQueryModel.getProgramAndProfitsPackage(15163, "992401311732289409"));
        PlusMemberCardEntity entity = new PlusMemberCardEntity();
        entity.setUserId(7);
        entity.setChannelId(1);
        entity.setMemberPlusId(1323);
        entity.setOrderSn("992401311732289409");
        entity.setConfigId(4);
        entity.setStartTime(DateUtils.getDateFromStr("2024-02-10 11:52:43"));
        entity.setEndTime(DateUtils.getDateFromStr("2024-05-10 11:52:43"));
        createEvent.setPlusMemberCard(entity);
        profitsSendPlanApplication.generateMemberProfitsSendPlan(createEvent);
    }

    @Test
    public void test3() {
        DmdsTaskFinishEvent event = new DmdsTaskFinishEvent();
        event.setChannelId(1);
        event.setUserId(7);
        event.setOrderChannelId(1);
        event.setOrderAmount(new BigDecimal("10000"));
        profitsSendPlanApplication.addConditionReachEvent(event);
    }

    @Test
    public void test4() {
        ReceiveCouponReq receiveCouponReq = new ReceiveCouponReq();
        receiveCouponReq.setSendPlanId(4311);
        receiveCouponReq.setUserId(310115378);
        receiveCouponReq.setChannelId(1);
        receiveCouponReq.setProgramId(15229);
        receiveCouponReq.setType(CouponTypeEnum.HKYH.getCode());
        PlusAbyssResult<ReceiveCouponResultResp> receiveCoupon = plusProfitSendPlanApi.receiveCoupon(
                receiveCouponReq);
        System.out.println(JSON.toJSONString(receiveCoupon));
    }

    @Autowired
    private ProfitHandlerContext profitHandlerContext;


    @Test
    public void test5() {
        HandleResetProfitEvent event = new HandleResetProfitEvent();
        event.setChannelId(1);
        event.setUserId(8);
        event.setModelId(11);
        List<MemberPlusInfoDetailExtPo> moveList = new ArrayList<>();
        MemberPlusInfoDetailExtPo po = new MemberPlusInfoDetailExtPo();
        po.setJxStatus(1);
        po.setChannelId(1);
        po.setUserId(8);
        po.setConfigId(11);
        po.setOrderSn("2020202020202020202056");
        po.setProgramId(15174);
        po.setJxStartTime(DateUtils.getDateFromStr("2024-02-14 17:14:50"));
        po.setJxEndTime(DateUtils.getDateFromStr("2024-03-15 17:14:50"));
        po.setId(1325);
        moveList.add(po);
        event.setList(moveList);
        profitHandlerContext.doRestProfits(event);
    }

    @Test
    public void test6() {
        QueryProfitUsedEvent event = new QueryProfitUsedEvent();
        event.setOrderSn("992401311732289409");
        event.setProgramId(15163);
        event.setChannel(1);
        event.setUserId(7);
        event.setConfigId(4);
        event.setModelId(13);
        Map<String, Object> dataMap = profitHandlerContext.getProfitUsedInfo(event);
        System.out.println(JSON.toJSONString(dataMap));
    }

    @Autowired
    private IPlusOrderJobApi jobApi;

    @Test
    public void test7() {
        jobApi.plusSendPlanTasks(13);
    }

    @Test
    public void test8() {
        jobApi.movePlusSendPlanTasks();
    }

    @Test
    public void test9() {
        MemberPlusProfitSendPlanQueryReq req = new MemberPlusProfitSendPlanQueryReq();
        req.setUserId(8);
        req.setModelId(11);
        req.setConfigId(1);
        req.setSendStatuses(Arrays.asList(PlusSendPlanStatusEnum.NOT_EFFECTIVE.getValue(),
                PlusSendPlanStatusEnum.CONDITIONAL.getValue(),
                PlusSendPlanStatusEnum.READY.getValue(), PlusSendPlanStatusEnum.DONE.getValue()));
        plusProfitSendPlanApi.querySendPlanList(req);
    }

    @Test
    public void test10() {
        jobApi.batchCreateRdzxOrder(0);
    }

    @Test
    public void test11() {
        GwfxFinishEvent event = new GwfxFinishEvent();
        event.setChannelId(1);
        event.setUserId(7);
        event.setProductId(8);
        event.setOrderAmount(new BigDecimal("80"));
        event.setIsFullPayment(0);
        event.setOrderChannelId(1);
        event.setOrderSn("1234567898");
        profitsSendPlanApplication.addConditionReachEvent(event);
    }

    @Test
    public void test12() {
        GwfxCancelEvent event = new GwfxCancelEvent();
        event.setChannelId(1);
        event.setUserId(7);
        event.setIsFullPayment(0);
        event.setOrderChannelId(1);
        event.setOrderSn("1234567897");
        profitsSendPlanApplication.addConditionReachEvent(event);
    }
}
