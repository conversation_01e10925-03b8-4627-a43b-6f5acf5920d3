package com.juzifenqi.plus.test;

import com.juzifenqi.core.ServiceResult;
import com.juzifenqi.order.dto.OrderSimpleInfoDTO;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.external.OrderExternal;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public abstract class AbstractTest {

    @MockBean
    protected OrderExternal orderExternal;

    //    @MockBean
    //    protected FmsRepositoryAcl userCardRepository;

    @Before
    public void init() {
        // mock 订单中心mock
        mockOrderExternal();

        //        // mock 查银行卡信息
        //        mockUserCardExternal();
    }

    private void mockOrderExternal() {
        // mock 创单
        Map<String, String> orderResult = new HashMap<>();
        orderResult.put("orderId", String.valueOf(System.currentTimeMillis()));
        orderResult.put("sign", "test");
        orderResult.put("moneyOrder", "58");
        ServiceResult<Map<String, String>> serviceResult = new ServiceResult<>();
        serviceResult.setResult(orderResult);
        Mockito.when(orderExternal.emberOrderCommit(Mockito.any())).thenReturn(serviceResult);

        // mock 获取订单信息
        OrderSimpleInfoDTO orderSimpleInfoDTO = new OrderSimpleInfoDTO();
        orderSimpleInfoDTO.setBankId(1);
        orderSimpleInfoDTO.setPeriodNum(12);
        orderSimpleInfoDTO.setMemberId(1);
        orderSimpleInfoDTO.setSmallMonthlyCard(true);
        ServiceResult<OrderSimpleInfoDTO> orderInfo = new ServiceResult<>();
        orderInfo.setResult(orderSimpleInfoDTO);
        Mockito.when(orderExternal.getCommonDataBySn(Mockito.any())).thenReturn(orderInfo);
    }

    //    /**
    //     * mock根据银行id获取银行卡信息
    //     */
    //    private void mockUserCardExternal() {
    //        UserCardNewEntity userCardNewEntity = new UserCardNewEntity();
    //        userCardNewEntity.setCardNo("天地银行");
    //        userCardNewEntity.setBankName("天地银行");
    //        userCardNewEntity.setCardNoDes("天地银行");
    //        userCardNewEntity.setCustomerName("测试用户");
    //        userCardNewEntity.setBankCardId("1");
    //        Mockito.when(userCardRepository
    //                .getBankById(Mockito.anyInt(), Mockito.anyInt(), Mockito.anyString()))
    //                .thenReturn(userCardNewEntity);
    //    }
}
