package com.juzifenqi.plus.test.order;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.dto.req.detail.ProfitsDetailReq;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.application.converter.IPlusMemberProfitsCardApplicationConverter;
import com.juzifenqi.plus.module.asserts.model.ProfitDetailModel;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusInfoDetailRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusMemberCardEntity;
import com.juzifenqi.plus.module.asserts.model.converter.IPlusMemberCardModelConverter;
import com.juzifenqi.plus.module.asserts.model.event.MemberPlusSendPlanCreateEvent;
import com.juzifenqi.plus.module.asserts.model.event.PlusProfitSendPlanEvent;
import com.juzifenqi.plus.module.order.application.converter.IPlusOrderApplicationConverter;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.event.UpdateProfitVirtualEvent;
import com.juzifenqi.plus.module.program.model.impl.PlusProfitModelImpl;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.impl.PlusProgramVirtualRepositoryImpl;
import com.juzifenqi.plus.test.AbstractTest;
import java.util.List;
import java.util.Objects;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @description
 * <AUTHOR>
 * @Date 2024/8/29
 */
public class PlusOrderSnapshotTest extends AbstractTest {
    @Autowired
    private PlusOrderSnapModel plusOrderSnapModel;
    @Autowired
    private IPlusProgramQueryModel         plusProgramQueryModel;
    @Autowired
    private IMemberPlusSendPlanApplication profitsSendPlanApplication;
    @Autowired
    private IMemberPlusInfoDetailRepository memberPlusInfoDetailRepository;
    @Autowired
    private ProfitDetailModel                profitDetailModel;
    @Autowired
    private PlusProgramVirtualRepositoryImpl plusProgramVirtualRepository;

    @Test
    public void createPlusOrderSnapshot(){
        PlusOrderEntity plusOrder = new PlusOrderEntity();
        plusOrder.setOrderSn("123456789");
        plusOrder.setConfigId(1);
        plusOrder.setProgramId(15346);
        plusOrder.setUserId(12333);
        plusOrderSnapModel.createPlusOrderSnapshot(plusOrder);
    }

    @Test
    public void generateMemberProfitsSendPlan(){

        PlusMemberCardEntity result = IPlusMemberCardModelConverter.instance.toPlusMemberCardEntity(memberPlusInfoDetailRepository.getByOrderSn("123456789"));
        // 获取权益包信息
        PlusProgramEntity program = plusProgramQueryModel.getProgramAndProfitsPackage(
                15346, "123456789");
        // 3. 生成发放计划和发放权益
        MemberPlusSendPlanCreateEvent createEvent = IPlusMemberProfitsCardApplicationConverter.instance.toProfitsSendPlanCreateEvent(
                result, program);
        profitsSendPlanApplication.generateMemberProfitsSendPlan(createEvent);
    }

    @Test
    public void refreshProfitSendPlan(){

        PlusProfitSendPlanEvent event = IPlusOrderApplicationConverter.instance.toPlusProfitSendPlanEvent(
                "992409051653252534", 15346,
                1, PlusConstant.MODEL_SEND_CONTROL_LIST);
        profitsSendPlanApplication.refreshProfitSendPlan(event);
    }

    @Test
    public void getProfitsDetail(){

        ProfitsDetailReq req = new ProfitsDetailReq();
        req.setConfigId(1);
        req.setChannelId(1);
        req.setUserId(310115378);
        profitDetailModel.getProfitsDetail(req);
    }

    @Test
    public void listBySkuAndConfig(){
        List<PlusProgramVirtualEntity> virtualList = plusProgramVirtualRepository.listBySkuAndConfig(
                PlusConstant.MERGE_CARD_LIST, PlusModelEnum.SHQY.getModelId(), "fFZ9cRT38A");
        System.err.println(JSONObject.toJSONString(virtualList));
    }
}
