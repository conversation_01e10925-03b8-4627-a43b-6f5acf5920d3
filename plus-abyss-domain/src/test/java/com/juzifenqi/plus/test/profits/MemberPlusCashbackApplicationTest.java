package com.juzifenqi.plus.test.profits;

import com.juzifenqi.plus.module.asserts.application.IMemberPlusCashbackApplication;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusSendPlanEntity;
import com.juzifenqi.plus.test.AbstractTest;
import com.juzifenqi.plus.test.utils.TestDataUtils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 */
public class MemberPlusCashbackApplicationTest extends AbstractTest {

    @Autowired
    private IMemberPlusCashbackApplication cashbackApplication;

    @Test
    public void sendGQFXTest() {
        MemberPlusSendPlanEntity data = TestDataUtils.readJsonFromFile("asserts/generateCashbackEvent.json", MemberPlusSendPlanEntity.class);
        cashbackApplication.sendJQFXCashback(data);
    }
}
