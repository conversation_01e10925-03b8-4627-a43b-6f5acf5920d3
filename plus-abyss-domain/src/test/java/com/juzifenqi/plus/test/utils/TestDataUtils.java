package com.juzifenqi.plus.test.utils;

import com.alibaba.fastjson.JSON;
import java.io.InputStream;
import java.io.InputStreamReader;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 */
@Slf4j
public class TestDataUtils {

    public static <T> T readJsonFromFile(String filePath, Class<T> clazz) {
        InputStream in = null;
        InputStreamReader reader = null;
        try {
            in = TestDataUtils.class.getClassLoader().getResourceAsStream(filePath);
            reader = new InputStreamReader(in, "utf-8");
            StringBuilder sb = new StringBuilder();
            int ch = 0;
            while ((ch = reader.read()) != -1) {
                sb.append((char) ch);
            }
            in.close();
            reader.close();
            return JSON.parseObject(sb.toString(), clazz);
        } catch (Exception e) {
            log.error("测试数据读取失败，文件名 = {}", filePath, e);
            return null;
        }
    }
}
