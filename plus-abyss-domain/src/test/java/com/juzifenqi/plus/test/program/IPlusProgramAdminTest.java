package com.juzifenqi.plus.test.program;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusLogApi;
import com.juzifenqi.plus.api.admin.IPlusProfitsAdminApi;
import com.juzifenqi.plus.api.admin.IPlusProgramAdminApi;
import com.juzifenqi.plus.api.admin.IPlusRepeatOrderApi;
import com.juzifenqi.plus.dto.req.admin.RepeatOrderCancelReq;
import com.juzifenqi.plus.dto.req.admin.program.SavePlusProgramReq;
import com.juzifenqi.plus.dto.req.profits.CreatePlusProModelReq;
import com.juzifenqi.plus.dto.req.profits.CreateProgramCouponReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramDetailResp;
import com.juzifenqi.plus.module.order.model.IPlusOrderBillModel;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * @description
 * <AUTHOR>
 * @Date 2024/8/27
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class IPlusProgramAdminTest {
    @Autowired
    private IPlusProgramAdminApi iPlusProgramAdminApi;
    @Autowired
    private IPlusProfitsAdminApi iPlusProfitsAdminApi;
    @Autowired
    private IPlusLogApi iPlusLogApi;
    @Autowired
    private IPlusOrderBillModel orderBillModel;
    @Autowired
    private IPlusRepeatOrderApi orderApi;

    @Test
    public void addProgram(){
        orderBillModel.deductIncomeRetry(100);
    }
    @Test
    public void editProgram(){
        RepeatOrderCancelReq req = new RepeatOrderCancelReq();
        req.setOrderSn("992410082020450501");
        req.setOptId(1);
        req.setOptName("哈哈");
        orderApi.cancelRepeatOrder(req);
    }

    @Test
    public void getProgramDetail(){
        PlusAbyssResult<ProgramDetailResp> resp = iPlusProgramAdminApi.getProgramDetail(15349);
        System.err.println(JSONObject.toJSONString(resp));
    }

    @Test
    public void editPlusProModel(){
        String json = "";
        CreatePlusProModelReq req = new CreatePlusProModelReq();
        req.setId(2251);
        req.setModelId(8);
        req.setProgramId(15349);
        req.setSort(1);
        req.setSendType(2);
        PlusAbyssResult resp = iPlusProfitsAdminApi.editPlusProModel(req);
        System.err.println(JSONObject.toJSONString(resp));
    }

    @Test
    public void editPlusProgramCoupon(){
//        String json = "{\"modelId\":4,\"programId\":\"15346\",\"couponName\":\"业务产品刘彬测试专用\",\"index\":240805256,\"couponId\":3112,\"couponCategory\":2,\"ordersNumber\":1,\"orderPrice\":1}";

        String json = "{\"modelId\":6,\"programId\":\"15346\",\"couponName\":\"业务产品刘彬测试专用\",\"index\":240805256,\"couponId\":3112,\"couponCategory\":2,\"type\":1}";
        CreateProgramCouponReq req = JSONObject.parseObject(json, CreateProgramCouponReq.class);

        req.setOptId(1);
        req.setOptName("test");
        req.setId(183);
//        req.setOrdersNumber(2);
//        req.setOrderPrice(new BigDecimal(3));
        req.setType(2);
        PlusAbyssResult resp = iPlusProfitsAdminApi.editPlusProgramCoupon(req);

        System.err.println(JSONObject.toJSONString(iPlusLogApi.getProfitLog(200,103)));
    }

    @Test
    public void saveProgramCouponIndex(){
        String json = "{\"modelId\":1,\"programId\":\"15346\",\"couponName\":\"业务产品刘彬测试专用\",\"index\":240805256,\"couponId\":3112,\"couponCategory\":2}";
        CreateProgramCouponReq req = JSONObject.parseObject(json, CreateProgramCouponReq.class);
        req.setOptId(1);
        req.setOptName("test");
        List<CreateProgramCouponReq> reqs = new ArrayList<>();
        reqs.add(req);
        PlusAbyssResult resp = iPlusProfitsAdminApi.saveProgramCouponIndex(reqs);
    }

    @Test
    public void deletePlusProgramCoupon(){
        CreateProgramCouponReq req = new CreateProgramCouponReq();
        req.setId(200);
        req.setModelId(1);
        PlusAbyssResult resp = iPlusProfitsAdminApi.deletePlusProgramCoupon(req);
        System.err.println(JSONObject.toJSONString(resp));
    }

}
