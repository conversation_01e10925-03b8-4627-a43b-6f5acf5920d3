package com.juzifenqi.plus.test.order;


import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusOrderApi;
import com.juzifenqi.plus.api.IPlusProfitSendPlanApi;
import com.juzifenqi.plus.api.IPlusRdzxApi;
import com.juzifenqi.plus.api.IPlusRefundRecordApi;
import com.juzifenqi.plus.api.admin.IPlusOrderAdminApi;
import com.juzifenqi.plus.dto.req.CreateRefundMqRecordReq;
import com.juzifenqi.plus.dto.req.admin.PlusOrderDetailQueryReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.admin.PlusOrderDetailResp;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.OrderReceiveProfitsEvent;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderRdzxApplication;
import com.juzifenqi.plus.module.order.application.IPlusOrderSmsApplication;
import com.juzifenqi.plus.module.order.application.IPlusRefundRecordApplication;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class SmsTest {

    @Autowired
    private IPlusOrderApi    orderApi;


    @Autowired
    private IPlusProfitSendPlanApi sendPlanApi;

    @Test
    public void test4() {

    }

    @Autowired
    private IPlusRefundRecordApi refundRecordApi;

    @Test
    public void test5() {
        CreateRefundMqRecordReq req = new CreateRefundMqRecordReq();
        req.setMqMsg("hahah111111");
        req.setOrderNode("QXDD");
        req.setOrderSn("ffw111fffff111e");
        req.setChannelId(1);
        req.setUserId(7);
        req.setOrderChannelId(777);
        req.setMsgId("msgid11111");
        req.setOrderStatus(502);
        refundRecordApi.createRefundMqRecord(req);
    }

    @Test
    public void test6() {

    }

    @Autowired
    private IPlusRefundRecordApplication refundRecordApplication;

    @Test
    public void test7() {
        refundRecordApplication.mqRecordExecute();
    }

    @Autowired
    private IMemberPlusSendPlanApplication sendPlanApplication;

    @Test
    public void test8() {
        OrderReceiveProfitsEvent event = new OrderReceiveProfitsEvent();
        event.setReceiveStatus("suc");
        event.setUserId(7);
        event.setOrderSn("22120309245790379146");
        sendPlanApplication.orderReceiveProfitsEvent(event);
    }

    @Autowired
    private IPlusOrderApplication orderApplication;

    @Test
    public void test9() {
        PlusDeductEvent event = new PlusDeductEvent();
        event.setUserId(310115378);
        event.setConfigId(10);
        event.setOrderSn("862210191804410066");
        event.setPlusOrderSn("992401171627050186");
        event.setDeductFlag(PlusPayTypeEnum.PAY_TYPE_5);
        event.setDeductNum(10);
        orderApplication.deduct(event);
    }

    @Autowired
    private IPlusOrderRdzxApplication rdzxApplication;
    @Autowired
    private IPlusRdzxApi              rdzxApi;
    @Test
    public void test10() {
       //rdzxApplication.batchDeduct(PlusPayTypeEnum.PAY_TYPE_6);
       /* CollectionRdzxDeductReq req= new CollectionRdzxDeductReq();
        req.setOrderSn("992401171627050186");
        PlusAbyssResult result = rdzxApi.collectionDeduct(req);
        System.out.println(JSON.toJSONString(result));*/
        orderApplication.delayBatchDeduct();
    }

    @Autowired
    private IPlusOrderSmsApplication smsApplication;

    @Test
    public void test11() {
        smsApplication.delayDeductSms();
    }

    @Autowired
    private IPlusOrderAdminApi orderAdminApi;

    @Test
    public void test91111() {
        PlusOrderDetailQueryReq req = new PlusOrderDetailQueryReq();
        req.setOrderSn("H0309250730115907165");
        PlusAbyssResult<PlusOrderDetailResp> plusOrderDetails = orderAdminApi.getPlusOrderDetails(
                req);
        System.out.println(JSON.toJSONString(plusOrderDetails));
    }
    // userId:310115378

    @Test
    public void mqRecordExecuteTest(){
        refundRecordApplication.mqRecordExecute();
    }
}
