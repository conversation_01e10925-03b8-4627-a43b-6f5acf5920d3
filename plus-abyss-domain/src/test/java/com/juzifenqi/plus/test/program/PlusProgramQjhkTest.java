package com.juzifenqi.plus.test.program;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.admin.IPlusProfitsAdminApi;
import com.juzifenqi.plus.dto.req.profits.CreateProgramQjhkReq;
import com.juzifenqi.plus.dto.req.profits.CreateQjhkCouponConfigReq;
import com.juzifenqi.plus.dto.req.profits.EditQjhkCouponConfigReq;
import com.juzifenqi.plus.dto.req.profits.EditQjhkCouponConfigStateReq;
import com.juzifenqi.plus.dto.req.profits.QueryProgramQjhkReq;
import com.juzifenqi.plus.dto.req.profits.QueryQjhkCouponConfigReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.profits.PlusProgramQjhkResp;
import com.juzifenqi.plus.dto.resp.profits.PlusQjhkCouponConfigResp;
import java.math.BigDecimal;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @description
 * @date 2024/4/3 11:04
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlusProgramQjhkTest {

    @Autowired
    private IPlusProfitsAdminApi service;

    @Test
    public void test1() {
        QueryProgramQjhkReq request = new QueryProgramQjhkReq();
        request.setProgramId(15175);
        PlusAbyssResult<PlusProgramQjhkResp> result = service.getPlusProgramQjhk(request);
        System.out.println("查询区间还款优惠折扣配置结果:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test2() {
        CreateProgramQjhkReq request = new CreateProgramQjhkReq();
        request.setProgramId(15175);
        request.setConfigId(10);
        request.setDiscountRate(new BigDecimal("0.85"));
        PlusAbyssResult result = service.editPlusProgramQjhk(request);
        System.out.println("新增区间还款优惠折扣配置结果:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test3() {
        CreateProgramQjhkReq request = new CreateProgramQjhkReq();
        request.setId(1);
        request.setDiscountRate(new BigDecimal("0.90"));
        PlusAbyssResult result = service.editPlusProgramQjhk(request);
        System.out.println("编辑区间还款优惠折扣配置结果:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test4() {
        CreateQjhkCouponConfigReq req = new CreateQjhkCouponConfigReq();
        req.setMinAmount(new BigDecimal("3"));
        req.setMaxAmount(new BigDecimal("3"));
        req.setPeriodNum(3);
        req.setCouponId(3);
        req.setOptId(1);
        req.setOptName("测试");
        service.saveQjhkCouponConfig(req);
    }

    @Test
    public void test5() {
        QueryQjhkCouponConfigReq req = new QueryQjhkCouponConfigReq();
        req.setPageNo(1);
        req.setPageSize(10);
        PlusAbyssResult<List<PlusQjhkCouponConfigResp>> result = service.getQjhkCouponConfigListPage(
                req);
        System.out.println("查询区间还款配置优惠券:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test6() {
        EditQjhkCouponConfigReq req = new EditQjhkCouponConfigReq();
        req.setId(642);
        req.setMinAmount(new BigDecimal("4"));
        req.setMaxAmount(new BigDecimal("4"));
        req.setPeriodNum(4);
        req.setCouponId(4);
        req.setOptId(1);
        req.setOptName("测试");
        service.editQjhkCouponConfig(req);
    }

    @Test
    public void test7() {
        EditQjhkCouponConfigStateReq req = new EditQjhkCouponConfigStateReq();
        req.setId(642);
        req.setState(2);
        req.setOptId(1);
        req.setOptName("测试");
        service.editQjhkCouponConfigState(req);
    }

}
