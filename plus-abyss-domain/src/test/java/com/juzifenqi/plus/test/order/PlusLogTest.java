package com.juzifenqi.plus.test.order;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.magic.api.PlusMarketApi;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusMarketApi;
import com.juzifenqi.plus.dto.req.market.PlusLoanMarketReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.market.PlusLoanMarketResp;
import com.juzifenqi.plus.enums.PlusSceneCodeEnum;
import com.juzifenqi.plus.module.market.model.IPlusMarketModel;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderContractEvent;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderShuntMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @description
 * @date 2024/2/4 10:11
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlusLogTest {

    @Autowired
    private IPlusMarketApi marketApi;


    @Test
    public void getPlusOrderSystemLogTest() {
        PlusLoanMarketReq req = new PlusLoanMarketReq();
        req.setChannelId(1);
        req.setUserId(310117274);
        req.setSceneCode(PlusSceneCodeEnum.LOAN.getCode());
        PlusAbyssResult<PlusLoanMarketResp> result = marketApi.loanMarket(req);
        System.out.println(JSON.toJSON(result));
    }

}
