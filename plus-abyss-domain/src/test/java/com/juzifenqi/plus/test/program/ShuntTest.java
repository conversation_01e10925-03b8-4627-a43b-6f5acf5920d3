package com.juzifenqi.plus.test.program;


import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.admin.IPlusOrderBillApi;
import com.juzifenqi.plus.api.admin.IPlusShuntAdminApi;
import com.juzifenqi.plus.dto.req.PageEntity;
import com.juzifenqi.plus.dto.req.admin.shunt.CreateSupplierContractReq;
import com.juzifenqi.plus.dto.req.admin.shunt.CreateSupplierPayReq;
import com.juzifenqi.plus.dto.req.admin.shunt.CreateSupplierReq;
import com.juzifenqi.plus.dto.req.admin.shunt.CreateSupplierRouteReq;
import com.juzifenqi.plus.dto.req.admin.shunt.CreateSupplierSeparateReq;
import com.juzifenqi.plus.dto.req.admin.shunt.EditSupplierPublicConfigReq;
import com.juzifenqi.plus.dto.req.admin.shunt.EnableSupplierReq;
import com.juzifenqi.plus.dto.req.admin.shunt.separate.CreateSeparateSupplierReq;
import com.juzifenqi.plus.dto.req.admin.shunt.separate.EnableSeparateSupplierReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.admin.shunt.PlusShuntSupplierResp;
import com.juzifenqi.plus.enums.EnableStateEnum;
import com.juzifenqi.plus.enums.supplier.SeparateTypeEnum;
import com.juzifenqi.plus.enums.supplier.SupplierSeparateEnableStateEnum;
import com.juzifenqi.plus.enums.supplier.SupplierSettleEnableStateEnum;
import com.juzifenqi.plus.enums.supplier.SupplierTypeEnum;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierMapper;
import com.juzifenqi.plus.module.order.model.IPlusOrderShuntModel;
import com.juzifenqi.plus.utils.RedisUtils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class ShuntTest {

    @Autowired
    private IPlusShuntRepository shuntRepository;
    @Autowired
    private IPlusShuntAdminApi   adminApi;

    @Test
    public void test() {
        /*PageResultEntity<PlusShuntSupplierEntity> supplierList = shuntRepository.getSupplierList(
                new PageEntity());
        System.out.println(JSON.toJSONString(supplierList));*/

        PlusShuntSupplierEntity supplierDetail = shuntRepository.getSupplierDetail(3);
        System.out.println(JSON.toJSONString(supplierDetail));
    }

    @Test
    public void test2() {
        CreateSupplierReq req = new CreateSupplierReq();
        req.setId(19);
        req.setSupplierName("分流主体-刘天奇测试-5");
        req.setSeparateEnableState(SupplierSeparateEnableStateEnum.YES.getCode());

        List<CreateSupplierSeparateReq> separateList = new ArrayList<>();
        CreateSupplierSeparateReq separateReq = new CreateSupplierSeparateReq();
        separateReq.setSeparateType(SeparateTypeEnum.RATE.getCode());
        separateReq.setSeparateSupplierId(14);
        separateReq.setSeparateRate(new BigDecimal("0.94"));
        separateList.add(separateReq);
        req.setSeparateList(separateList);

        CreateSupplierPayReq payReq = new CreateSupplierPayReq();
        payReq.setBusinessScene("刘天奇会员-5");
        payReq.setBankAccountNo("5555");
        payReq.setBankAccountName("5");
        payReq.setBankAccountType(1);
        payReq.setBankBranchNo("5");
        payReq.setBankName("5");
        payReq.setBankProvinceName("5");
        payReq.setBankCityName("山东");
        req.setPay(payReq);

        List<CreateSupplierContractReq> contractList = new ArrayList<>();
        CreateSupplierContractReq contractReq = new CreateSupplierContractReq();
        contractReq.setContractName("超级合同5");
        contractReq.setContractNo("xxxxx5");
        contractList.add(contractReq);
        req.setContractList(contractList);

        req.setSettleEnableState(SupplierSettleEnableStateEnum.YES.getCode());

        req.setOptUserId(5);
        req.setOptUserName("刘天奇-5");
        adminApi.editSupplier(req);
    }

    @Test
    public void test3() {
        EditSupplierPublicConfigReq req = new EditSupplierPublicConfigReq();
        req.setSupplierId(4);
        req.setSwitchState(1);
        req.setSupplierName("嘻嘻");
        req.setOptUserId(11);
        req.setOptUserName("河长制");
        adminApi.editPublicConfig(req);
    }

    @Test
    public void test4() {
        CreateSupplierRouteReq req = new CreateSupplierRouteReq();
        req.setId(3);
        req.setPriorityOrder(7);
        req.setSupplierId(5);
        req.setOptUserId(1);
        req.setOptUserName("fewfwefew");
        req.setSupplierName("我哦我我");
        adminApi.editRoute(req);
        /*EnableSupplierReq req = new EnableSupplierReq();
        req.setId(3);
        req.setEnableState(2);
        req.setOptUserName("哈哈");
        req.setOptUserId(1);
        adminApi.enableRoute(req);*/
        /*adminApi.getShuntDetail();*/

    }

    @Autowired
    private IPlusOrderShuntModel     shuntModel;
    @Autowired
    private IPlusShuntSupplierMapper shuntSupplierMapper;
    @Autowired
    private IPlusOrderBillApi orderBillApi;
    @Autowired
    private RedisUtils redisUtils;

    @Test
    public void test5() {
        orderBillApi.getShuntOccupyInfo();
    }

    @Test
    public void test6() {
        PlusAbyssResult<List<PlusShuntSupplierResp>> supplierList = adminApi.getSupplierList(1);
        System.out.println("查询结果：" + JSON.toJSONString(supplierList));
    }

    @Test
    public void test7() {
        CreateSupplierReq req = new CreateSupplierReq();
        req.setSupplierName("分流主体测试1");
        req.setSeparateEnableState(SupplierSeparateEnableStateEnum.YES.getCode());

        List<CreateSupplierSeparateReq> separateList = new ArrayList<>();
        CreateSupplierSeparateReq separateReq = new CreateSupplierSeparateReq();
        separateReq.setSeparateType(SeparateTypeEnum.RATE.getCode());
        separateReq.setSeparateSupplierId(6);
        separateReq.setSeparateRate(new BigDecimal("0.5"));
        separateList.add(separateReq);
        CreateSupplierSeparateReq separateReq2 = new CreateSupplierSeparateReq();
        separateReq2.setSeparateType(SeparateTypeEnum.RATE.getCode());
        separateReq2.setSeparateSupplierId(6);
        separateReq2.setSeparateRate(new BigDecimal("0.5"));
        separateList.add(separateReq2);
        req.setSeparateList(separateList);

        CreateSupplierPayReq payReq = new CreateSupplierPayReq();
        payReq.setBusinessScene("测试会员");
        payReq.setBankAccountNo("5555");
        payReq.setBankAccountName("5");
        payReq.setBankAccountType(1);
        payReq.setBankBranchNo("5");
        payReq.setBankName("5");
        payReq.setBankProvinceName("5");
        payReq.setBankProvinceCode("5");
        payReq.setBankCityName("5");
        req.setPay(payReq);

        List<CreateSupplierContractReq> contractList = new ArrayList<>();
        CreateSupplierContractReq contractReq = new CreateSupplierContractReq();
        contractReq.setContractName("测试合同");
        contractReq.setContractNo("xxxxx5");
        contractList.add(contractReq);
        req.setContractList(contractList);

        req.setSettleEnableState(SupplierSettleEnableStateEnum.YES.getCode());

        req.setOptUserId(1);
        req.setOptUserName("刘天奇");
        adminApi.addSupplier(req);
    }

    @Test
    public void test8() {
        EnableSupplierReq req = new EnableSupplierReq();
        req.setId(7);
        req.setEnableState(2);
        req.setOptUserId(1);
        req.setOptUserName("刘天奇");
        adminApi.enableSupplier(req);
    }

    @Test
    public void test9() {
        PageEntity entity = new PageEntity();
        entity.setPageNo(1);
        entity.setPageSize(10);
        PlusAbyssResult<List<PlusShuntSupplierResp>> result = adminApi.getSupplierList(
                SupplierTypeEnum.FL.getCode(), entity);
        System.out.println("分流主体查询结果：" + JSON.toJSONString(result));
    }

    @Test
    public void test10() {
        PlusAbyssResult<PlusShuntSupplierResp> supplierDetail = adminApi.getSupplierDetail(5);
        System.out.println("查询结果：" + JSON.toJSONString(supplierDetail));
    }

    @Test
    public void test11() {
        PageEntity entity = new PageEntity();
        entity.setPageNo(1);
        entity.setPageSize(10);
        PlusAbyssResult<List<PlusShuntSupplierResp>> result = adminApi.getSupplierList(
                SupplierTypeEnum.QF.getCode(), entity);
        System.out.println("查询结果：" + JSON.toJSONString(result));
    }

    @Test
    public void test12() {
        CreateSeparateSupplierReq req = new CreateSeparateSupplierReq();
        req.setSupplierName("清分主体一");
        req.setMerchantId("1");
        req.setBusinessScene("清分主体业务场景一");
        req.setOptUserId(1);
        req.setOptUserName("刘天奇1");
        adminApi.addSeparateSupplier(req);
    }

    @Test
    public void test13() {
        CreateSeparateSupplierReq req = new CreateSeparateSupplierReq();
        req.setId(12);
        req.setSupplierName("清分主体二");
        req.setMerchantId("2");
        req.setBusinessScene("清分主体业务场景二");
        req.setOptUserId(2);
        req.setOptUserName("刘天奇2");
        adminApi.editSeparateSupplier(req);
    }

    @Test
    public void test14() {
        EnableSeparateSupplierReq req = new EnableSeparateSupplierReq();
        req.setId(7);
        req.setEnableState(EnableStateEnum.ENABLE.getCode());
        req.setOptUserId(1);
        req.setOptUserName("刘天奇");
        adminApi.enableSeparateSupplier(req);
    }

}
