package com.juzifenqi.plus.test.order;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusOrderApi;
import com.juzifenqi.plus.dto.req.PlusDeductForActiveReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.PlusOrderDeductResp;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.model.IPlusOrderBillModel;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.PayCallbackEntity;
import com.juzifenqi.plus.module.order.repository.external.acl.RiskExternalRepositoryAcl;
import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
@Slf4j
public class PlusDeductApplicationTest {

    @Autowired
    private IPlusOrderApi plusOrderApi;

    /**
     * 任务划扣
     */
    @Test
    public void taskDeduct() {
        log.info("小额月卡任务划扣开始：plusOrderSn:{}", "992210201134384792");
        PlusDeductForActiveReq req = new PlusDeductForActiveReq();
        req.setUserId(7);
        req.setDeductFlag(PlusPayTypeEnum.PAY_TYPE_1);
        req.setOrderSn("862210191804410066");
        req.setConfigId(1);
        req.setPlusOrderSn("992210201134384792");
        req.setDeductNum(1);
        req.setOrderChannelId(3);
        try {
            req.setCreateOrderTime(
                    new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").parse("2024-08-01 00:00:00"));
        } catch (ParseException e) {
            throw new RuntimeException(e);
        }
        log.info("小额月卡测试任务划扣入参：{}", JSON.toJSONString(req));
        PlusAbyssResult<PlusOrderDeductResp> deduct = plusOrderApi.deduct(req);
        log.info("小额月卡测试任务划扣返回：{}", JSON.toJSONString(deduct));
    }


    /**
     * 放款划扣
     */
    @Test
    public void loanDeduct() {
        log.info("小额月卡测试放款划扣开始：plusOrderSn:{}", "992210201134384792");
        PlusDeductForActiveReq req = new PlusDeductForActiveReq();
        req.setUserId(7);
        req.setDeductFlag(PlusPayTypeEnum.PAY_TYPE_1);
        req.setOrderSn("862210191804410066");
        req.setConfigId(12);
        req.setOrderChannelId(3);
        req.setCreateOrderTime(new Date(1695023660000L));
        log.info("小额月卡测试任务划扣入参：{}", JSON.toJSONString(req));
        PlusAbyssResult<PlusOrderDeductResp> deduct = plusOrderApi.deduct(req);
        log.info("小额月卡测试放款划扣返回：{}", JSON.toJSONString(deduct));
    }

    @Autowired
    private IPlusOrderApplication orderApplication;

    @Test
    public void fewf() {
        PayCallbackEntity entity = new PayCallbackEntity();
        entity.setApplication("shangcheng");
        entity.setPayType("deduct");
        entity.setStatus("S");
        entity.setAmount(new BigDecimal("89900"));
        entity.setSerialNumber("wuwuwuwu");
        entity.setOrderId("992406201946171376");
        orderApplication.payResultCallback(entity);
    }

    @Autowired
    private IPlusOrderBillModel orderBillModel;

    @Test
    public void fgew() {
        PlusOrderBillEntity entity = orderBillModel.getByOrderSn("992406201946171376");
        orderBillModel.incomeExecute(entity, entity.getSerialNumber());
    }

    @Autowired
    private RiskExternalRepositoryAcl riskExternalRepositoryAcl;

    @Test
    public void fewfew() throws Exception {
        boolean resubmitUser = riskExternalRepositoryAcl.getResubmitUser(310113316);
        System.out.println(resubmitUser);
    }
}
