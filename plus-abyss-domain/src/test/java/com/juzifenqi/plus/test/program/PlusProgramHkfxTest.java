package com.juzifenqi.plus.test.program;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusOrderJobApi;
import com.juzifenqi.plus.api.IPlusRdzxApi;
import com.juzifenqi.plus.api.admin.IPlusProfitsAdminApi;
import com.juzifenqi.plus.dto.req.profits.CreateRepayCashBackReq;
import com.juzifenqi.plus.dto.req.profits.DeleteRepayCashBackReq;
import com.juzifenqi.plus.dto.req.profits.EditRepayCashBackReq;
import com.juzifenqi.plus.dto.req.profits.QueryRepayCashBackReq;
import com.juzifenqi.plus.dto.req.profits.RdzxEquityConfigInfoReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.profits.PlusProgramRepayCashBackResp;
import com.juzifenqi.plus.dto.resp.profits.RdzxEquityConfigInfoResp;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.module.asserts.application.IMemberPlusSendPlanApplication;
import com.juzifenqi.plus.module.asserts.model.event.plan.condition.LoanOrderRepayEvent;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderDeductPlanMapper;
import java.math.BigDecimal;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 还款返现测试类
 *
 * <AUTHOR>
 * @date 2024/6/13 下午3:46
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlusProgramHkfxTest {

    @Autowired
    private IPlusProfitsAdminApi           profitsAdminApi;
    @Autowired
    private IPlusRdzxApi                   rdzxApi;
    @Autowired
    private IMemberPlusSendPlanApplication profitsSendPlanApplication;
    @Autowired
    private IPlusOrderJobApi               plusOrderJobApi;

    @Test
    public void test() {
        CreateRepayCashBackReq req = new CreateRepayCashBackReq();
        req.setProgramId(15267);
        req.setConfigId(JuziPlusEnum.RDZX_CARD.getCode());
        req.setModelId(PlusModelEnum.HKFX.getModelId());
        req.setMinAmount(new BigDecimal("1000"));
        req.setMaxAmount(new BigDecimal("2000"));
        req.setPeriods(6);
        req.setCashbackAmount(new BigDecimal("10"));
        req.setFirstScale(new BigDecimal("0.4"));
        req.setOptId(257);
        req.setOptName("刘天奇");
        profitsAdminApi.saveRepayCashBack(req);
    }

    @Test
    public void test1() {
        QueryRepayCashBackReq req = new QueryRepayCashBackReq();
        req.setProgramId(15266);
        req.setPageNo(1);
        req.setPageSize(10);
        PlusAbyssResult<List<PlusProgramRepayCashBackResp>> result = profitsAdminApi.getRepayCashBackListPage(
                req);
        System.out.println("查询还款返现配置:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test2() {
        EditRepayCashBackReq req = new EditRepayCashBackReq();
        req.setId(115);
        req.setMinAmount(new BigDecimal("100"));
        req.setMaxAmount(new BigDecimal("300"));
        req.setPeriods(6);
        req.setCashbackAmount(new BigDecimal("60"));
        req.setFirstScale(new BigDecimal("0.4"));
        req.setOptId(1);
        req.setOptName("刘天奇");
        profitsAdminApi.editRepayCashBack(req);
    }

    @Test
    public void test3() {
        DeleteRepayCashBackReq req = new DeleteRepayCashBackReq();
        req.setId(116);
        req.setOptId(1);
        req.setOptName("刘天奇");
        profitsAdminApi.deleteRepayCashBack(req);
    }

    @Test
    public void test4() {
        RdzxEquityConfigInfoReq req = new RdzxEquityConfigInfoReq();
        req.setChannelId(1);
        req.setUserId(1);
        req.setServiceFee(new BigDecimal("100"));
        req.setLoanAmount(new BigDecimal("150"));
        req.setPeriods(6);
        PlusAbyssResult<RdzxEquityConfigInfoResp> result = rdzxApi.rdzxEquityConfigInfo(req);
        System.out.println("查询融担咨询卡权益配置信息:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test5() {
        LoanOrderRepayEvent event = new LoanOrderRepayEvent();
        event.setLoanOrderSn("24053116225246117286");
        event.setPeriods(1);
        profitsSendPlanApplication.addConditionReachEvent(event);
    }

    @Test
    public void test6() {
        plusOrderJobApi.hkfxMicroDefrayPayJob();
    }

    @Autowired
    private IPlusOrderDeductPlanMapper plusOrderDeductPlanMapper;

    @Test
    public void test7(){
        plusOrderDeductPlanMapper.getRepayDayDeductPlan(0);
        plusOrderDeductPlanMapper.getOverdueDeductPlan(1);
    }

    @Test
    public void test8() {
        String param = "{\"beforeMinutes\":30,\"warningNumber\":5}";
        plusOrderJobApi.deductError(param);
    }

}
