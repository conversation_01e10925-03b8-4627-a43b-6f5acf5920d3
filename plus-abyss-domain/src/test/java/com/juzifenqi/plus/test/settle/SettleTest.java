package com.juzifenqi.plus.test.settle;

import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.ISettleJobApi;
import com.juzifenqi.plus.api.admin.IPlusOrderBillApi;
import com.juzifenqi.plus.api.admin.ISettleBillAdminApi;
import com.juzifenqi.plus.dto.req.admin.settle.SettleBillQueryReq;
import com.juzifenqi.plus.dto.req.admin.settle.SettlePendingQueryReq;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.admin.PlusOrderSeparateItemAdminResp;
import com.juzifenqi.plus.dto.resp.admin.settle.PlusOrderSettleItemPendingAdminResp;
import com.juzifenqi.plus.dto.resp.admin.settle.SettleBillResp;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.OrderRefundNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.settle.application.ISettleApplication;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 分账结算相关
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/7 13:02
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class SettleTest {

    @Autowired
    private IPlusOrderApplication orderApplication;
    @Autowired
    private IPlusOrderBillApi     billApi;
    @Autowired
    private ISettleApplication    settleApplication;
    @Autowired
    private ISettleBillAdminApi   adminApi;
    @Autowired
    private ISettleJobApi         jobApi;

    @Test
    public void testDeduct() {
        PlusDeductEvent event = new PlusDeductEvent();
        event.setUserId(310118131);
        event.setConfigId(5);
        event.setDeductFlag(PlusPayTypeEnum.PAY_TYPE_1);
        event.setOrderChannelId(3);
        event.setOrderSn("992408011625288829");
        event.setCreateOrderTime(new Date());
        orderApplication.deduct(event);
    }

    @Test
    public void testDeductNotify() {

        NewPayResultCallbackEntity entity = new NewPayResultCallbackEntity();
        entity.setState("S");
        entity.setAmount(new BigDecimal("5.00"));
        entity.setOrderId("992112062015510083");
        entity.setPayProductCode("ZXZF");
        entity.setSerialNumber("901832982680497954883");
        entity.setThirdPayNum("HK9924081010310250721725852099612");
        orderApplication.newPayResultCallback(entity);
    }

    @Test
    public void testSeparateItemQuery() {
        PlusAbyssResult<List<PlusOrderSeparateItemAdminResp>> resp = billApi.getOrderSeparateItemList(
                "992408101031025072");
        System.out.println(resp);
    }

    @Test
    public void testRefundNotify() {
        OrderRefundNotifyEntity entity = new OrderRefundNotifyEntity();
        entity.setOrderId("992408101031025072");
        entity.setStatus("S");
        entity.setSuccessTime(new Date());
        entity.setRefundAmount(new BigDecimal("1.00"));
        settleApplication.refundSettlePending(entity);
    }

    @Test
    public void testPendingQuery() {
        SettlePendingQueryReq req = new SettlePendingQueryReq();
        req.setPageNo(1);
        req.setPageSize(10);
        PlusAbyssResult<List<PlusOrderSettleItemPendingAdminResp>> resp = adminApi.getPendingList(
                req);
        System.out.println(resp);
    }

    @Test
    public void testSettleSummary() {
        jobApi.createSettleBillJob("2024-09-09 00:00:00", "2024-09-09 23:59:59");
    }

    @Test
    public void testSettleDefray() {
        jobApi.defraySettleBillJob("2024-09-09");
    }

    @Test
    public void testSettleBillQuery() {
        SettleBillQueryReq req = new SettleBillQueryReq();
        req.setPageNo(1);
        req.setPageSize(10);
        PlusAbyssResult<List<SettleBillResp>> resp = adminApi.getSettleBillList(req);
        System.out.println(resp);
    }
}
