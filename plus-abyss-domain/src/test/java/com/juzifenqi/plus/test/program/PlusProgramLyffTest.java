package com.juzifenqi.plus.test.program;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.PlusAbyssApplication;
import com.juzifenqi.plus.api.IPlusProductApi;
import com.juzifenqi.plus.api.admin.IPlusProfitsAdminApi;
import com.juzifenqi.plus.dto.req.profits.CreateLyffVirtualGoodsReq;
import com.juzifenqi.plus.dto.req.profits.CreateLyffVirtualGoodsReq.LyffVirtualGoods;
import com.juzifenqi.plus.dto.req.profits.DeleteLyffVirtualGoodsReq;
import com.juzifenqi.plus.dto.req.profits.EditLyffVirtualGoodsReq;
import com.juzifenqi.plus.dto.req.profits.LyffVirtualDetailReq;
import com.juzifenqi.plus.dto.req.profits.LyffVirtualInfoReq;
import com.juzifenqi.plus.dto.req.profits.QueryLyffVirtualGoodsReq;
import com.juzifenqi.plus.dto.resp.LyffVirtualDetailResp;
import com.juzifenqi.plus.dto.resp.LyffVirtualInfoResp;
import com.juzifenqi.plus.dto.resp.PlusAbyssResult;
import com.juzifenqi.plus.dto.resp.profits.PlusProgramLyffVirtualResp;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * 权益0元发放测试类
 *
 * <AUTHOR>
 * @date 2024/5/28 上午11:45
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest(classes = PlusAbyssApplication.class)
public class PlusProgramLyffTest {

    @Autowired
    private IPlusProfitsAdminApi service;

    @Autowired
    private IPlusProductApi plusProductApi;

    @Test
    public void test4() {
        CreateLyffVirtualGoodsReq req = new CreateLyffVirtualGoodsReq();
        req.setProgramId(15243);
        req.setConfigId(10);
        req.setOptId(1);
        req.setOptName("刘天奇");
        List<LyffVirtualGoods> virtualGoodsList = new ArrayList<>();
        LyffVirtualGoods goods = new LyffVirtualGoods();
        goods.setSku("e0ZZ16bk55");
        goods.setProductId(111220);
        goods.setProductName("100119-知乎盐选会员月卡");
        goods.setVirtualGoodsId(111220);
        goods.setSellPrice(new BigDecimal("20"));
        goods.setRankNum(1);
        goods.setDiscountRate(new BigDecimal("0"));
        goods.setImgUrl("xxxxxx.com");
        goods.setMinAmount(new BigDecimal("0"));
        goods.setMaxAmount(new BigDecimal("1000"));
        goods.setStatus(1);
        virtualGoodsList.add(goods);
        req.setVirtualGoodsList(virtualGoodsList);
        PlusAbyssResult result = service.saveLyffVirtualGoodsBatch(req);
        System.out.println("新增权益0元发放配置结果:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test5() {
        QueryLyffVirtualGoodsReq req = new QueryLyffVirtualGoodsReq();
        req.setProgramId(15221);
        PlusAbyssResult<List<PlusProgramLyffVirtualResp>> result = service.getLyffVirtualGoodsListPage(
                req);
        System.out.println("查询权益0元发放配置列表结果:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test6() {
        EditLyffVirtualGoodsReq req = new EditLyffVirtualGoodsReq();
        req.setId(599);
        req.setImgUrl("xxxxx.2n");
        req.setMinAmount(new BigDecimal("3"));
        req.setMaxAmount(new BigDecimal("3"));
        req.setOptId(3);
        req.setOptName("李四");
        PlusAbyssResult result = service.editLyffVirtualGoods(req);
        System.out.println("编辑权益0元发放配置结果:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test7() {
        DeleteLyffVirtualGoodsReq req = new DeleteLyffVirtualGoodsReq();
        req.setId(599);
        req.setOptId(3);
        req.setOptName("李四");
        PlusAbyssResult result = service.deleteLyffVirtualGoods(req);
        System.out.println("删除权益0元发放配置结果:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test8() {
        LyffVirtualInfoReq req = new LyffVirtualInfoReq();
        req.setChannelId(1);
        req.setServiceFee(new BigDecimal("100"));
        PlusAbyssResult<LyffVirtualInfoResp> result = plusProductApi.getLyffVirtualInfo(req);
        System.out.println("查看权益0元发放虚拟商品信息:" + JSONObject.toJSONString(result));
    }

    @Test
    public void test9() {
        LyffVirtualDetailReq req = new LyffVirtualDetailReq();
        req.setUserId(310117450);
        req.setPlusOrderSn("992405311614483283");
        PlusAbyssResult<LyffVirtualDetailResp> result = plusProductApi.getLyffVirtualDetail(req);
        System.out.println("查看权益0元发放虚拟商品详情:" + JSONObject.toJSONString(result));
    }

}
