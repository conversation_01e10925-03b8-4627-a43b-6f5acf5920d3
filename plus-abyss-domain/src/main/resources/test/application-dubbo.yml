#dubbo
dubbo:
  application:
    name: plus-abyss-provider-test
  registry:
    protocol: zookeeper
    address: ************:2181
  # 启动服务时不检查提供者是否存在
  consumer:
    check: false
    # 远程调用默认超时时间
    timeout: 5000
  protocol:
    name: dubbo
    port: -1
  service:
    delay: 100
  reference:
    retries: 0
    timeout: 5000
  #以下为dubbo相关的业务配置
  group: test
  member:
    group: member-platform-test
  sms:
    group: sms_test
  auth:
    group: auth_test
  coupon:
    group: coupon-group-test
  product:
    group: product-group-test
    cache:
      group: productSearch-test
  credit:
    group: test
  order:
    group: super-order-group-test
  virtual:
    group: virtual-center-test
  alpha:
    group: plus-aplha-test
  acm:
    group: acm-test
  support:
    group: support-test
  contract:
    group: test
  onlineShop:
    group: online-core-test
  mallTrade:
    group: mall-trade-group-test
  act:
    group: activity-provider-test
  workOrder:
    group: work-order-test
  market:
    group: plus-magic-test
  omsTaker:
    group: oms-taker-test
  rms:
    group: test