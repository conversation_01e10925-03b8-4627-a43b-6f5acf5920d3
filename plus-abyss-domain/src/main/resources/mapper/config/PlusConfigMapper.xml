<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusConfigMapper">

    <resultMap id="PlusConfig" type="com.juzifenqi.plus.module.program.repository.po.PlusConfigPo">
    <result column="id" property="id" />
    <result column="name" property="name" />
    <result column="sign_config" property="signConfig" />
    <result column="status" property="status" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="bt_time" property="btTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `name`,
        `sign_config`,
        `status`,
        `update_time`,
        `create_time`,
        `bt_time`
    </sql>

    <insert id="saverPlusConfig" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusConfigPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_config (
            `name`,
            `sign_config`,
            `status`,
            `create_time`
        )
        VALUES(
                  #{name},
                  #{signConfig},
                  #{status},
                  now()
              )
    </insert>

    <update id="updatePlusConfig" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusConfigPo" >
        UPDATE plus_config
        SET
        <if test="name != null">`name`= #{name},</if>
        <if test="status != null">`status`= #{status},</if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="loadPlusConfig" resultMap="PlusConfig">
        SELECT <include refid="Base_Column_List" />
        FROM plus_config
        <where>
            <if test="sign_config != null">`sign_config`= #{signConfig},</if>
            <if test="id != null">`id`= #{id},</if>
            <if test="status != null">`status`= #{status},</if>
        </where>
        order by id desc limit 1;
    </select>

    <select id="checkSignConfig" resultType="java.lang.Integer">
        select count(*) from plus_config where sign_config=#{signConfig};
    </select>

    <select id="getPlusConfigList" resultMap="PlusConfig">
        SELECT id,name,sign_config
        FROM plus_config
        order by id desc;
    </select>


    <select id="getPlusConfigById" resultMap="PlusConfig">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_config
        where id = #{configId}
    </select>
</mapper>