<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusProModelMapper">

    <resultMap id="PlusProModel"
            type="com.juzifenqi.plus.module.program.repository.po.PlusProModelPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="model_id" property="modelId"/>
        <result column="remark" property="remark"/>
        <result column="short_name" property="shortName"/>
        <result column="guide_copy" property="guideCopy"/>
        <result column="saving_copy" property="savingCopy"/>
        <result column="sort" property="sort"/>
        <result column="rule_explain" property="ruleExplain"/>
        <result column="rule_image" property="ruleImage"/>
        <result column="alert_state" property="alertState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="send_type" property="sendType"/>
    </resultMap>

    <resultMap id="MemberPlusModel" type="com.juzifenqi.plus.module.program.repository.po.PlusProModelPo">
        <result column="name" property="name"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `model_id`,
        `remark`,
        `short_name`,
        `guide_copy`,
        `saving_copy`,
        `sort`,
        `rule_explain`,
        `rule_image`,
        `alert_state`,
        `create_time`,
        `update_time`,
        `send_type`
    </sql>

    <select id="countProModelByProgramId" parameterType="java.lang.Integer"
            resultType="java.lang.Integer">
        select count(1)
        from plus_pro_model
        where program_id = #{programId}
          and model_id = #{modelId}
    </select>

    <insert id="savePlusProModel"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProModelPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_pro_model (`program_id`,
        `model_id`,
        `remark`,
        `short_name`,
        `guide_copy`,
        `saving_copy`,
        `sort`,
        `rule_explain`,
        `rule_image`,
        `alert_state`,
        `create_time`,
        `update_time`,
        send_type)
        VALUES (#{programId},
        #{modelId},
        #{remark},
        #{shortName},
        #{guideCopy},
        #{savingCopy},
        #{sort},
        #{ruleExplain},
        #{ruleImage},
        #{alertState},
        NOW(),
        #{updateTime},
        #{sendType})
    </insert>

    <insert id="batchInsertPlusProModel">
        INSERT INTO plus_pro_model (`program_id`,
        `model_id`,
        `remark`,
        `short_name`,
        `guide_copy`,
        `saving_copy`,
        `sort`,
        `rule_explain`,
        `rule_image`,
        `alert_state`,
        `create_time`,
        `update_time`,
        send_type)
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.modelId},
            #{record.remark},
            #{record.shortName},
            #{record.guideCopy},
            #{record.savingCopy},
            #{record.sort},
            #{record.ruleExplain},
            #{record.ruleImage},
            #{record.alertState},
            NOW(),
            NOW(),
             #{record.sendType}
            )
        </foreach>
    </insert>

    <update id="batchUpdateProModelSort">

        <foreach collection="list" index="index" item="record" separator=";">
            update plus_pro_model
            set `sort` = #{record.sort},
            update_time = NOW()
            where id = #{record.id}
        </foreach>
    </update>

    <delete id="deletePlusProModel" parameterType="java.lang.Integer">
        DELETE
        FROM plus_pro_model
        WHERE `program_id` = #{programId}
        and `model_id` = #{modelId}
    </delete>

    <update id="updateMemberPlusProModel"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProModelPo">
        UPDATE plus_pro_model
        SET
        <if test="sort != null">
            `sort`= #{sort},
        </if>
        <if test="shortName != null">
            `short_name`= #{shortName},
        </if>
        <if test="guideCopy != null">
            `guide_copy`= #{guideCopy},
        </if>
        <if test="savingCopy != null">
            `saving_copy`= #{savingCopy},
        </if>
        <if test="ruleExplain != null">
            `rule_explain`= #{ruleExplain},
        </if>
        <if test="ruleImage == '-1'">
            `rule_image`= null,
        </if>
        <if test="ruleImage != null and ruleImage != ''and ruleImage != '-1'">
            `rule_image`= #{ruleImage},
        </if>
        <if test="alertState != null">
            `alert_state`= #{alertState},
        </if>
        <if test="sendType != null">
            `send_type`= #{sendType},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>


    <select id="getPlusProModelByProgramId" resultMap="PlusProModel">
        SELECT
        <include refid="Base_Column_List"/>
        from plus_pro_model p
        where p.program_id = #{id}
        order by sort asc, update_time desc
    </select>


    <sql id="whereConditions">
        <where>
            <if test="queryMap.q_programId != null and queryMap.q_programId != ''">
                and `program_id` = #{param1.q_programId}
            </if>
        </where>
    </sql>


    <select id="getCountNullByProgramId" parameterType="java.lang.Integer"
            resultType="java.lang.Integer">
        select count(1)
        from plus_pro_model
        where program_id = #{programId}
        and short_name is null
    </select>

    <select id="getPlusProModelByVo"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProModelPo"
            resultMap="PlusProModel">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_pro_model
        WHERE `program_id` = #{programId} and `model_id` = #{modelId}
    </select>

    <select id="getProModelByProgramIdOrderBySort" parameterType="java.lang.Integer"
            resultMap="PlusProModel">
        SELECT
        <include refid="Base_Column_List"/>
        from plus_pro_model p
        where p.program_id = #{programId}
        order by sort ASC
    </select>

    <select id="getQuotaModelByMember" parameterType="java.util.Map"
            resultMap="PlusProModel">
        SELECT
        pp.*
        FROM member_plus_info_detail mp
        INNER JOIN
        plus_pro_model pp
        ON mp.`program_id` = pp.`program_id`
        WHERE
        mp.user_id=#{userId}
        AND mp.channel_id=#{channelId}
        AND mp.config_id=#{configId}
        AND mp.jx_status=1
        AND pp.model_id = 8
    </select>
    <select id="getByModelId" resultType="com.juzifenqi.plus.module.program.repository.po.PlusProModelPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_pro_model
        WHERE `model_id` = #{modelId}
    </select>

    <delete id="batchDel">
        DELETE
        FROM plus_pro_model
        WHERE `program_id` = #{programId}
          and `model_id` in
        <foreach collection="modelIds" item="modelId" open="(" separator="," close=")">
            #{modelId}
        </foreach>
    </delete>
</mapper>
