<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusDefaultProgramPriceMapper">

    <resultMap id="PlusDefaultProgramPriceMap" type="com.juzifenqi.plus.module.program.repository.po.price.PlusDefaultProgramPricePo" >
        <result column="id" property="id" />
        <result column="price_id" property="priceId" />
        <result column="program_id" property="programId" />
        <result column="create_user" property="createUser" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `price_id`,
        `program_id`,
        `create_user`,
        `create_user_id`,
        `create_time`
    </sql>

    <insert id="insert" parameterType="com.juzifenqi.plus.module.program.repository.po.price.PlusDefaultProgramPricePo">
        INSERT INTO plus_default_program_price (
        `price_id`,
        `program_id`,
        `create_user`,
        `create_user_id`,
        `create_time`
        )
        VALUES
        (#{priceId},
        #{programId},
        #{createUser},
        #{createUserId},
        NOW())
    </insert>

    <select id="selectById" parameterType="java.lang.Integer"
            resultMap="PlusDefaultProgramPriceMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_default_program_price
        WHERE `id` = #{id}
    </select>

    <select id="selectByPriceId" resultMap="PlusDefaultProgramPriceMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_default_program_price
        where price_id = #{priceId}
        order by id desc
    </select>

    <delete id="deleteByPriceId">
        delete from plus_default_program_price where price_id = #{priceId}
    </delete>

</mapper>
