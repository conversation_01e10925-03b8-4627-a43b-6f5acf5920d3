<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusChannelFunctionMapper">

    <resultMap id="PlusChannelFunction" type="com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo" >
        <result column="id" property="id" />
        <result column="manage_id" property="manageId" />
        <result column="config_id" property="configId" />
        <result column="open_mode" property="openMode" />
        <result column="plus_profit" property="plusProfit" />
        <result column="pricing_mode" property="pricingMode" />
        <result column="pay_later_deduct" property="payLaterDeduct" />
        <result column="rapid_return_card" property="rapidReturnCard" />
        <result column="delay_return_card" property="delayReturnCard"/>
        <result column="delay_days" property="delayDays"/>
        <result column="note_reach" property="noteReach" />
        <result column="pay_success_url" property="paySuccessUrl"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `manage_id`,
        `config_id`,
        `open_mode`,
        `plus_profit`,
        `pricing_mode`,
        `pay_later_deduct`,
        `rapid_return_card`,
        `delay_return_card`,
        `delay_days`,
        `note_reach`,
        `pay_success_url`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusChannelFunction" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_channel_function (
            `manage_id`,
            `config_id`,
            `open_mode`,
            `plus_profit`,
            `pricing_mode`,
            `pay_later_deduct`,
            `rapid_return_card`,
            `delay_return_card`,
            `delay_days`,
            `note_reach`,
            `create_time`,
            `update_time`,
            `pay_success_url`
        )
        VALUES(
                  #{manageId},
                  #{configId},
                  #{openMode},
                  #{plusProfit},
                  #{pricingMode},
                  #{payLaterDeduct},
                  #{rapidReturnCard},
                  #{delayReturnCard},
                  #{delayDays},
                  #{noteReach},
                  NOW(),
                  #{updateTime},
                  #{paySuccessUrl}
              )
    </insert>


    <insert id="insertPlusChannelFunction" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo">
        INSERT INTO plus_channel_function (
            `manage_id`,
            `config_id`,
            `open_mode`,
            `plus_profit`,
            `pricing_mode`,
            `pay_later_deduct`,
            `rapid_return_card`,
            `delay_return_card`,
            `delay_days`,
            `note_reach`,
            `create_time`,
            `update_time`,
            `pay_success_url`)
        VALUES (#{plusChannelFunction.manageId},
                #{plusChannelFunction.configId},
                #{plusChannelFunction.openMode},
                #{plusChannelFunction.plusProfit},
                #{plusChannelFunction.pricingMode},
                #{plusChannelFunction.payLaterDeduct},
                #{plusChannelFunction.rapidReturnCard},
                #{plusChannelFunction.delayReturnCard},
                #{plusChannelFunction.delayDays},
                #{plusChannelFunction.noteReach},
                NOW(),
                #{plusChannelFunction.updateTime},
                #{plusChannelFunction.paySuccessUrl})
    </insert>


    <delete id="deletePlusChannelFunction" parameterType="java.lang.Integer">
        DELETE FROM plus_channel_function
        WHERE `id` = #{id}
    </delete>

    <delete id="deleteByManageId">
        DELETE FROM plus_channel_function
        WHERE `manage_id` = #{manageId}
    </delete>

    <update id="updatePlusChannelFunction" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo" >
        UPDATE plus_channel_function
        SET
        <if test="plusChannelFunction.manageId != null">`manage_id`= #{plusChannelFunction.manageId},</if>
        <if test="plusChannelFunction.configId != null">`config_id`= #{plusChannelFunction.configId},</if>
        <if test="plusChannelFunction.openMode != null">`open_mode`= #{plusChannelFunction.openMode},</if>
        <if test="plusChannelFunction.plusProfit != null">`plus_profit`= #{plusChannelFunction.plusProfit},</if>
        <if test="plusChannelFunction.pricingMode != null">`pricing_mode`= #{plusChannelFunction.pricingMode},</if>
        <if test="plusChannelFunction.payLaterDeduct != null">`pay_later_deduct`= #{plusChannelFunction.payLaterDeduct},</if>
        <if test="plusChannelFunction.rapidReturnCard != null">`rapid_return_card`= #{plusChannelFunction.rapidReturnCard},</if>
        <if test="plusChannelFunction.delayReturnCard != null">`delay_return_card`= #{plusChannelFunction.delayReturnCard},</if>
        <if test="plusChannelFunction.returnIntervalTime != null">`delay_days`= #{plusChannelFunction.delayDays},</if>
        <if test="plusChannelFunction.noteReach != null">`note_reach`= #{plusChannelFunction.noteReach},</if>
        update_time = now()
        WHERE `id` = #{plusChannelFunction.id}
    </update>


    <select id="loadPlusChannelFunction" parameterType="java.lang.Integer" resultMap="PlusChannelFunction">
        SELECT <include refid="Base_Column_List" />
        FROM plus_channel_function
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusChannelFunction">
        SELECT <include refid="Base_Column_List" />
        FROM plus_channel_function
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_channel_function
    </select>

    <select id="getByManageId"
            resultMap="PlusChannelFunction">
        SELECT <include refid="Base_Column_List" />
        FROM plus_channel_function
        where manage_id = #{manageId}
    </select>

    <select id="getByChannelId" resultMap="PlusChannelFunction">
        SELECT f.`id`,
               f.`manage_id`,
               f.`config_id`,
               f.`open_mode`,
               f.`plus_profit`,
               f.`pricing_mode`,
               f.`pay_later_deduct`,
               f.`rapid_return_card`,
               f.`delay_return_card`,
               f.`delay_days`,
               f.`note_reach`,
               f.`pay_success_url`,
               f.`create_time`,
               f.`update_time`
        FROM plus_channel_function f,plus_channel_manage m
        WHERE m.id = f.manage_id
          AND m.channel_id = #{channelId}
    </select>

</mapper>