<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusProgramMapper">

    <resultMap id="MemberPlusProgram" type="com.juzifenqi.plus.module.program.repository.po.PlusProgramPo">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="member_price" property="memberPrice"/>
        <result column="mall_mobile_price" property="mallMobilePrice"/>
        <result column="first_order_discount" property="firstOrderDiscount"/>
        <result column="first_order_price" property="firstOrderPrice"/>
        <result column="rule_desc_img" property="ruleDescImg"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="programme_status" property="programmeStatus"/>
        <result column="programme_days" property="programmeDays"/>
        <result column="recommended_markup" property="recommendedMarkup"/>
        <result column="backstage_name" property="backstageName"/>
        <result column="estimate_save_amount" property="estimateSaveAmount"/>
        <result column="be_set_recovery" property="beSetRecovery"/>
        <result column="recovery_img" property="recoveryImg"/>
        <result column="show_home_page" property="showHomePage"/>
        <result column="show_buy_record" property="showBuyRecord"/>
        <result column="member_grade" property="memberGrade"/>
        <result column="program_color" property="programColor"/>
        <result column="sign_program" property="signProgram"/>
        <result column="config_id" property="configId"/>
        <result column="channel" property="channel"/>
        <result column="market_content" property="marketContent"/>
        <result column="amount_content" property="amountContent"/>
        <result column="user_explain" property="userExplain"/>
        <result column="user_rules" property="userRules"/>
        <result column="show_time" property="showTime"/>
        <result column="after_pay_state" property="afterPayState"/>
        <result column="contract_id" property="contractId"/>
    </resultMap>


    <resultMap id="MemberPlusProgramList" type="com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="member_price" property="memberPrice"/>
        <result column="mall_mobile_price" property="mallMobilePrice"/>
        <result column="first_order_discount" property="firstOrderDiscount"/>
        <result column="first_order_price" property="firstOrderPrice"/>
        <result column="rule_desc_img" property="ruleDescImg"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="programme_status" property="programmeStatus"/>
        <result column="programme_days" property="programmeDays"/>
        <result column="recommended_markup" property="recommendedMarkup"/>
        <result column="backstage_name" property="backstageName"/>
        <result column="estimate_save_amount" property="estimateSaveAmount"/>
        <result column="be_set_recovery" property="beSetRecovery"/>
        <result column="recovery_img" property="recoveryImg"/>
        <result column="show_home_page" property="showHomePage"/>
        <result column="show_buy_record" property="showBuyRecord"/>
        <result column="member_grade" property="memberGrade"/>
        <result column="program_color" property="programColor"/>
        <result column="sign_program" property="signProgram"/>
        <result column="config_id" property="configId"/>
        <result column="channel" property="channel"/>
        <result column="market_content" property="marketContent"/>
        <result column="amount_content" property="amountContent"/>
        <result column="user_explain" property="userExplain"/>
        <result column="user_rules" property="userRules"/>
        <result column="is_renew" property="isRenew"/>
        <result column="renew_open_time" property="renewOpenTime"/>
        <result column="show_time" property="showTime"/>
        <result column="isRenewId" property="isRenewId"/>
        <result column="renewOpenTime" property="renewOpenTime"/>
        <result column="alternate_program" property="alternateProgram"/>
        <result column="show_interval" property="interval"/>
        <result column="frame_type" property="frameType"/>
        <result column="frame_image" property="frameImage"/>
        <result column="is_show_frame" property="isShowFrame"/>
        <result column="after_pay_state" property="afterPayState"/>
        <result column="contract_id" property="contractId"/>
    </resultMap>


    <resultMap id="plusProgramAdminVo"
            type="com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity">
        <result column="id" property="id"/>
        <result column="name" property="name"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="member_price" property="memberPrice"/>
        <result column="mall_mobile_price" property="mallMobilePrice"/>
        <result column="first_order_discount" property="firstOrderDiscount"/>
        <result column="first_order_price" property="firstOrderPrice"/>
        <result column="rule_desc_img" property="ruleDescImg"/>
        <result column="status" property="status"/>
        <result column="programme_status" property="programmeStatus"/>
        <result column="programme_days" property="programmeDays"/>
        <result column="recommended_markup" property="recommendedMarkup"/>
        <result column="backstage_name" property="backstageName"/>
        <result column="estimate_save_amount" property="estimateSaveAmount"/>
        <result column="be_set_recovery" property="beSetRecovery"/>
        <result column="recovery_img" property="recoveryImg"/>
        <result column="show_home_page" property="showHomePage"/>
        <result column="show_buy_record" property="showBuyRecord"/>
        <result column="member_grade" property="memberGrade"/>
        <result column="program_color" property="programColor"/>
        <result column="sign_program" property="signProgram"/>
        <result column="config_id" property="configId"/>
        <result column="channel" property="channel"/>
        <result column="market_content" property="marketContent"/>
        <result column="amount_content" property="amountContent"/>
        <result column="user_explain" property="userExplain"/>
        <result column="user_rules" property="userRules"/>
        <result column="is_renew" property="isRenew"/>
        <result column="renew_price" property="renewPrice"/>
        <result column="renew_open_time" property="renewOpenTime"/>
        <result column="show_time" property="showTime"/>
        <result column="isRenewId" property="isRenewId"/>
        <result column="renewOpenTime" property="renewOpenTime"/>
        <result column="alternate_program" property="alternateProgram"/>
        <result column="show_interval" property="interval"/>
        <result column="frame_type" property="frameType"/>
        <result column="frame_image" property="frameImage"/>
        <result column="is_show_frame" property="isShowFrame"/>
        <result column="after_pay_state" property="afterPayState"/>
        <result column="contract_id" property="contractId"/>
        <result column="rd_send_type" property="rdSendType"/>
        <result column="pop_up" property="popUp"/>
        <result column="two_pop_up" property="twoPopUp"/>
        <result column="rd_choose" property="rdChoose"/>
        <result column="send_node" property="sendNode"/>
        <result column="pay_types" property="payTypes"/>
        <result column="first_pay_amount" property="firstPayAmount"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `name`,
        `effective_time`,
        `member_price`,
        `mall_mobile_price`,
        `first_order_discount`,
        `first_order_price`,
        `rule_desc_img`,
        `status`,
        `create_time`,
        `create_user_id`,
        `create_user_name`,
        `update_time`,
        `update_user_id`,
        `update_user_name`,
        `programme_status`,
        `programme_days`,
        `recommended_markup`,
        `backstage_name`,
        `estimate_save_amount`,
        `be_set_recovery`,
        `recovery_img`,
        `show_home_page`,
        `show_buy_record`,
        `program_color`,
        `config_id`,
        `sign_program`,
        `member_grade`,
        `channel`,
        `market_content`,
        `amount_content`,
        `user_explain`,
        `user_rules`,
        `show_time`,
        `after_pay_state`,
        `contract_id`
    </sql>

    <select id="loadMemberPlusProgram" parameterType="java.lang.Integer"
            resultMap="MemberPlusProgram">
        select
        <include refid="Base_Column_List"/>
        from plus_program
        where `id` = #{id}
    </select>


    <select id="loadPlusProgramByIdAndStatus" parameterType="java.lang.Integer"
            resultMap="MemberPlusProgram">
        select
        <include refid="Base_Column_List"/>
        from plus_program
        where `id` in
        <foreach collection="idList" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND status = 1
        and `effective_time` &lt;= NOW()
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusProgram">
        select
        p.`id`,
        p.`name`,
        p.`effective_time`,
        p.`member_price`,
        p.`mall_mobile_price`,
        p.`first_order_discount`,
        p.`first_order_price`,
        p.`rule_desc_img`,
        p.`status`,
        p.`create_time`,
        p.`create_user_id`,
        p.`create_user_name`,
        p.`update_time`,
        p.`update_user_id`,
        p.`update_user_name`,
        p.`programme_status`,
        p.`programme_days`,
        p.`recommended_markup`,
        p.`backstage_name`,
        p.`estimate_save_amount`,
        p.`be_set_recovery`,
        p.`recovery_img`,
        p.`show_home_page`,
        p.`show_buy_record`,
        p.`program_color`,
        p.`config_id`,
        p.`sign_program`,
        p.`member_grade`,
        p.`channel`,
        p.`market_content`,
        p.`amount_content`,
        p.`user_explain`,
        p.`user_rules`,
        a.is_renew,
        a.renew_open_time,
        p.show_time,
        p.`after_pay_state`,
        p.`contract_id`
        from `plus_program` p
        LEFT JOIN plus_renew_relevance a ON p.id = a.program_id
        <include refid="getCondition"/>
        order by `id` desc
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="pageListNew" resultMap="MemberPlusProgramList">
        select
        p.`id`,
        p.`name`,
        p.`effective_time`,
        p.`member_price`,
        p.`mall_mobile_price`,
        p.`first_order_discount`,
        p.`first_order_price`,
        p.`rule_desc_img`,
        p.`status`,
        p.`create_time`,
        p.`create_user_id`,
        p.`create_user_name`,
        p.`update_time`,
        p.`update_user_id`,
        p.`update_user_name`,
        p.`programme_status`,
        p.`programme_days`,
        p.`recommended_markup`,
        p.`backstage_name`,
        p.`estimate_save_amount`,
        p.`be_set_recovery`,
        p.`recovery_img`,
        p.`show_home_page`,
        p.`show_buy_record`,
        p.`program_color`,
        p.`config_id`,
        p.`sign_program`,
        p.`member_grade`,
        p.`channel`,
        p.`market_content`,
        p.`amount_content`,
        p.`user_explain`,
        p.`user_rules`,
        p.`after_pay_state`,
        p.`contract_id`,
        a.is_renew,
        a.renew_open_time,
        p.show_time,
        a.alternate_program,
        a.show_interval,
        a.frame_type,
        a.frame_image,
        a.is_show_frame
        from `plus_program` p
        LEFT JOIN `plus_renew_relevance` a ON p.id = a.program_id
        <include refid="getConditionNew"/>
        order by `id` desc
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="getPlusProgramDetailById" parameterType="java.lang.Integer"
            resultMap="plusProgramAdminVo">
        select
            p.`id`,
            p.`name`,
            p.`effective_time`,
            p.`member_price`,
            p.`mall_mobile_price`,
            p.`first_order_discount`,
            p.`first_order_price`,
            p.`rule_desc_img`,
            p.`status`,
            p.`create_time`,
            p.`create_user_id`,
            p.`create_user_name`,
            p.`update_time`,
            p.`update_user_id`,
            p.`update_user_name`,
            p.`programme_status`,
            p.`programme_days`,
            p.`recommended_markup`,
            p.`backstage_name`,
            p.`estimate_save_amount`,
            p.`be_set_recovery`,
            p.`recovery_img`,
            p.`show_home_page`,
            p.`show_buy_record`,
            p.`program_color`,
            p.`config_id`,
            p.`sign_program`,
            p.`member_grade`,
            p.`channel`,
            p.`market_content`,
            p.`amount_content`,
            p.`user_explain`,
            p.`user_rules`,
            p.`after_pay_state`,
            p.`contract_id`,
            p.`pay_types`,
            p.`first_pay_amount`,
            a.is_renew,
            a.id as isRenewId,
            a.renew_open_time,
            p.show_time,
            a.alternate_program,
            a.show_interval,
            a.frame_type,
            a.frame_image,
            a.is_show_frame,
            a.renew_price,
            e.rd_send_type,
            e.pop_up,
            e.two_pop_up,
            e.rd_choose,
            e.send_node
        from `plus_program` p
                 LEFT JOIN plus_renew_relevance a ON p.id = a.program_id
                 LEFT JOIN plus_program_extend e on p.id = e.program_id
        where p.id =#{id}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from plus_program p
        <include refid="getCondition"/>
    </select>


    <select id="pageListCountNew"
            parameterType="com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq"
            resultType="java.lang.Integer">
        select count(1)
        from plus_program p
        <include refid="getConditionNew"/>
    </select>

    <select id="getMemberPlusProgramListed"
            resultType="com.juzifenqi.plus.module.program.repository.po.PlusProgramPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_program
        WHERE programme_status = 1
        AND status = 1
        and `effective_time` &lt;= NOW()
    </select>

    <select id="getMemberPlusProgramAll"
            resultType="com.juzifenqi.plus.module.program.repository.po.PlusProgramPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_program
        WHERE programme_status = 1
        AND status = 1
        and `effective_time` &lt;= NOW()
        and show_home_page = 1
        <if test="configId != null and  configId>0">
            and config_id=#{configId}
        </if>
        order by id desc
    </select>

    <select id="getValidMemberPlusProgram"
            resultType="com.juzifenqi.plus.module.program.repository.po.PlusProgramPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_program
        WHERE programme_status = 1
        and `effective_time` &lt;= NOW()
    </select>

    <sql id="getCondition">
        <where>
            <if test="queryMap.q_startTime != null and '' != queryMap.q_startTime">
                and p.effective_time &gt;= #{queryMap.q_startTime}
            </if>
            <if test="queryMap.q_endTime != null and '' != queryMap.q_endTime">
                and p.effective_time &lt;= #{queryMap.q_endTime}
            </if>
            <if test="queryMap.q_updateStartTime != null and '' != queryMap.q_updateStartTime">
                and p.update_time &gt;= #{queryMap.q_updateStartTime}
            </if>
            <if test="queryMap.q_updateEndTime != null and '' != queryMap.q_updateEndTime">
                and p.update_time &lt;= #{queryMap.q_updateEndTime}
            </if>
            <if test="queryMap.q_name != null and queryMap.q_name != ''">
                and p.name like CONCAT('%', #{queryMap.q_name}, '%')
            </if>
            <if test="queryMap.q_programmeStatus != null and queryMap.q_programmeStatus != ''">
                and p.`programme_status` = #{queryMap.q_programmeStatus}
            </if>
            <if test="queryMap.q_status != null and queryMap.q_status != ''">
                and p.`status` = #{queryMap.q_status}
            </if>
            <if test="queryMap.q_signProgram != null and queryMap.q_signProgram != ''">
                and p.`sign_program` = #{queryMap.q_signProgram}
            </if>
            <if test="queryMap.q_configId != null and queryMap.q_configId != ''">
                and p.`config_id` = #{queryMap.q_configId}
            </if>
            <if test="queryMap.q_channelId != null and queryMap.q_channelId != ''">
                and p.`channel` = #{queryMap.q_channelId}
            </if>
        </where>
    </sql>


    <sql id="getConditionNew">
        <where>
            <if test="queryParam.startTime != null and '' != queryParam.startTime">
                and p.effective_time &gt;= #{queryParam.startTime}
            </if>
            <if test="queryParam.endTime != null and '' !=queryParam.endTime">
                and p.effective_time &lt;= #{queryParam.endTime}
            </if>

            <if test="queryParam.name != null and queryParam.name != ''">
                and p.name like CONCAT('%', #{queryParam.name}, '%')
            </if>
            <if test="queryParam.programmeStatus != null ">
                and p.`programme_status` = #{queryParam.programmeStatus}
            </if>
            <if test="queryParam.status != null ">
                and p.`status` = #{queryParam.status}
            </if>
            <if test="queryParam.configId != null ">
                and p.`config_id` = #{queryParam.configId }
            </if>
            <if test="queryParam.pragramId != null ">
                and p.`id` = #{queryParam.pragramId}
            </if>
            <if test="queryParam.channelId != null ">
                and p.`channel` = #{queryParam.channelId}
            </if>
        </where>
    </sql>

    <insert id="saveMemberPlusProgram"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProgramPo"
            useGeneratedKeys="true" keyProperty="id">
        insert into plus_program (
        `name`,
        `effective_time`,
        `member_price`,
        `mall_mobile_price`,
        `first_order_discount`,
        `first_order_price`,
        `rule_desc_img`,
        `status`,
        `create_time`,
        `create_user_id`,
        `create_user_name`,
        `update_time`,
        `update_user_id`,
        `update_user_name`,
        `programme_status`,
        `programme_days`,
        `recommended_markup`,
        `backstage_name`,
        `estimate_save_amount`,
        `be_set_recovery`,
        `recovery_img`,
        `show_home_page`,
        `show_buy_record`,
        `program_color`,
        `market_content`,
        `amount_content`,
        `user_explain`,
        `user_rules`,
        `config_id`,
        `sign_program`,
        `member_grade`,
        <if test="channel != null">`channel`,</if>
        `show_time`,
        `after_pay_state`,
        `contract_id`,
        `pay_types`,
        `first_pay_amount`
        )
        values(
            #{name},
            #{effectiveTime},
            #{memberPrice},
            #{mallMobilePrice},
            #{firstOrderDiscount},
            #{firstOrderPrice},
            #{ruleDescImg},
            #{status},
            now(),
            #{createUserId},
            #{createUserName},
            NOW(),
            #{updateUserId},
            #{updateUserName},
            #{programmeStatus},
            #{programmeDays},
            #{recommendedMarkup},
            #{backstageName},
            #{estimateSaveAmount},
            #{beSetRecovery},
            #{recoveryImg},
            #{showHomePage},
            #{showBuyRecord},
            #{programColor},
            #{marketContent},
            #{amountContent},
            #{userExplain},
            #{userRules},
            #{configId},
            #{signProgram},
            #{memberGrade},
            <if test="channel != null">#{channel},</if>
            #{showTime},
            #{afterPayState},
            #{contractId},
            #{payTypes},
            #{firstPayAmount}
        )
    </insert>


    <delete id="deleteMemberPlusProgram" parameterType="java.lang.Integer">
        delete from plus_program
        where `id` = #{id}
    </delete>

    <update id="updatePlusProgram"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProgramPo"
            useGeneratedKeys="true" keyProperty="id">
        update plus_program
        set
        <if test="name != null">`name`= #{name},</if>
        <if test="effectiveTime != null">`effective_time`=
            #{effectiveTime},
        </if>
        <if test="mallMobilePrice != null">`mall_mobile_price`=
            #{mallMobilePrice},
        </if>
        <if test="firstOrderDiscount != null">`first_order_discount`=
            #{firstOrderDiscount},
        </if>
        `first_order_price`= #{firstOrderPrice},
        <if test="ruleDescImg != null">`rule_desc_img`=
            #{ruleDescImg},
        </if>
        <if test="createUserId != null">`create_user_id`=
            #{createUserId},
        </if>
        <if test="createUserName != null">`create_user_name`=
            #{createUserName},
        </if>
        <if test="updateUserId != null">`update_user_id`=
            #{updateUserId},
        </if>
        <if test="updateUserName != null">`update_user_name`=
            #{updateUserName},
        </if>
        <if test="programmeStatus != null">`programme_status`=
            #{programmeStatus},
        </if>
        <if test="programmeDays != null">`programme_days`=
            #{programmeDays},
        </if>
        <if test="amountContent != null">`amount_content`=
            #{amountContent},
        </if>
        <if test="marketContent != null">`market_content`=
            #{marketContent},
        </if>
        <if test="userExplain != null">`user_explain`=
            #{userExplain},
        </if>
        <if test="userRules != null">`user_rules`=
            #{userRules},
        </if>
        <if test="recommendedMarkup != null">`recommended_markup`=
            #{recommendedMarkup},
        </if>
        <if test="backstageName != null">`backstage_name`=
            #{backstageName},
        </if>
        <if test="configId != null">`config_id`=
            #{configId},
        </if>
        <if test="signProgram != null">`sign_program`=
            #{signProgram},
        </if>
        <if test="showTime != null">`show_time`=
            #{showTime},
        </if>
        <if test="recoveryImg != null">`recovery_img`=
            #{recoveryImg},
        </if>
        <if test="payTypes != null">`pay_types`=
            #{payTypes},
        </if>
        <if test="firstPayAmount != null">`first_pay_amount`=
            #{firstPayAmount},
        </if>
        member_price = #{memberPrice},
        estimate_save_amount = #{estimateSaveAmount},
        be_set_recovery = #{beSetRecovery},
        show_home_page = #{showHomePage},
        show_buy_record = #{showBuyRecord},
        member_grade = #{memberGrade},
        program_color = #{programColor},
        update_time = now(),
        <!--after_pay_state=#{afterPayState},-->
        contract_id=#{contractId}
        where `id` = #{id}
    </update>

    <update id="upAct">
        update plus_program set status = 1 , update_time = now() where id=#{id}
    </update>

    <update id="downAct">
        update plus_program set status = 2 , update_time = now() where id=#{id}
    </update>

    <update id="updateProgrammeEffective">
        update plus_program set programme_status = 1 , update_time = now() where id=#{id}
    </update>

    <select id="getBackstageNameCount" resultType="java.lang.Integer">
        SELECT count(*) from plus_program where backstage_name = #{backstageName}
        <if test="id !=null and id != ''">
            and `id` != #{id}
        </if>
        <if test="channel != null">
            and channel = #{channel}
        </if>
    </select>

    <select id="getProgramIsRecord" parameterType="java.lang.Integer"
            resultType="java.lang.Integer">
        select
        show_buy_record
        from plus_program
        where `id` = #{id}
    </select>
    <select id="checkSignProgram" resultType="java.lang.Integer">
        select count(*) from plus_program where sign_program=#{sign}
    </select>
    <select id="getMemberPlusProgramList"
            resultType="com.juzifenqi.plus.module.program.repository.po.PlusProgramPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_program
        WHERE programme_status = 1
        AND status = 1
        and `effective_time` &lt;= NOW()
        <if test="configId != null and  configId>0">
            and config_id=#{configId}
        </if>
        <if test="programId != null">
            and id != #{programId}
        </if>
        <if test="channelId != null">
            and channel = #{channelId}
        </if>
        order by id desc
    </select>

    <update id="updateMallMobilePrice">
        UPDATE plus_program
        SET mall_mobile_price = #{price}, update_time = now()
        where id = #{programId}
    </update>
</mapper>
