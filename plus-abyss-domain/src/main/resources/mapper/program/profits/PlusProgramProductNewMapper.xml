<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductNewMapper">

    <resultMap id="PlusProgramProductNew"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        <result column="id" property="id"/>
        <result column="product_id" property="productId"/>
        <result column="program_id" property="programId"/>
        <result column="config_id" property="configId"/>
        <result column="sort" property="sort"/>
        <result column="model_id" property="modelId"/>
        <result column="price_type" property="priceType"/>
        <result column="product_sku" property="productSku"/>
        <result column="product_image" property="productSku"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="type_id" property="typeId"/>
        <result column="sale_state" property="saleState"/>
        <result column="opt_user" property="optUser"/>
        <result column="opt_id" property="optId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `product_id`,
        `program_id`,
        `config_id`,
        `sort`,
        `model_id`,
        `opt_user`,
        `opt_id`,
        `create_time`,
        `update_time`,
        `price_type`,
        `product_sku`,
        `product_image`,
        `discount_rate`,
        `type_id`,
        `sale_state`,
        `bi_time`
    </sql>

    <insert id="savePlusProgramProductNew"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_product_new (`product_id`,
                                              `program_id`,
                                              `config_id`,
                                              `sort`,
                                              `model_id`,
                                              `opt_user`,
                                              `opt_id`,
                                              `create_time`,
                                              `update_time`,
                                              `bi_time`)
        VALUES (#{productId},
                #{programId},
                #{configId},
                #{sort},
                #{modelId},
                #{optUser},
                #{optId},
                NOW(),
                #{updateTime},
                #{biTime})
    </insert>


    <delete id="deletePlusProgramProductNew" parameterType="java.lang.Integer">
        DELETE
        FROM plus_program_product_new
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusProgramProductNew"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        UPDATE plus_program_product_new
        SET
        <if test="plusProgramProductNew.productId != null">`product_id`=
            #{plusProgramProductNew.productId},
        </if>
        <if test="plusProgramProductNew.programId != null">`program_id`=
            #{plusProgramProductNew.programId},
        </if>
        <if test="plusProgramProductNew.configId != null">`config_id`=
            #{plusProgramProductNew.configId},
        </if>
        <if test="plusProgramProductNew.sort != null">`sort`= #{plusProgramProductNew.sort},</if>
        <if test="plusProgramProductNew.modelId != null">`model_id`=
            #{plusProgramProductNew.modelId},
        </if>
        <if test="plusProgramProductNew.optUser != null">`opt_user`=
            #{plusProgramProductNew.optUser},
        </if>
        <if test="plusProgramProductNew.optId != null">`opt_id`= #{plusProgramProductNew.optId},
        </if>
        <if test="plusProgramProductNew.biTime != null">`bi_time`=
            #{plusProgramProductNew.biTime},
        </if>
        update_time = now()
        WHERE `id` = #{plusProgramProductNew.id}
    </update>
    <update id="updateStateByModelAndProduct">
        update
            plus_program_product_new
        set
            sale_state = #{onSaleState}
        where
            product_sku = #{productSku}
        and
            model_id = #{modelId}
    </update>


    <select id="loadPlusProgramProductNew" parameterType="java.lang.Integer"
            resultMap="PlusProgramProductNew">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_product_new
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusProgramProductNew">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_product_new
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_program_product_new
    </select>

    <select id="getCountByProgramId" resultType="java.lang.Integer">
        select count(*)
        from plus_program_product_new
        where program_id = #{programId}
          and model_id = #{modelId}
    </select>


    <select id="yygProductIdList" resultType="java.lang.Integer">
        select product_id
        from plus_program_product_new
        where program_id = #{programId}
          and model_id = #{modelId}
        order by id desc
    </select>

    <select id="yygProductList"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        select
        <include refid="Base_Column_List"/>
        from plus_program_product_new
        where
            program_id = #{programId}
          and
            model_id = #{modelId}
          <if test="typeId != null">
              and type_id = #{typeId}
          </if>
        order by id desc
    </select>

    <insert id="batchSaveYygProduct" parameterType="java.util.List" useGeneratedKeys="true"
            keyProperty="id">
        insert into plus_program_product_new
        (product_id,program_id,config_id,model_id,opt_user,opt_id,create_time,product_sku,product_image,discount_rate,type_id,sort)
        values
        <foreach collection="list" item="item" separator=","
                index="index">
            ( #{item.productId},
            #{item.programId},
            #{item.configId},
            #{item.modelId},
            #{item.optUser},
            #{item.optId},
            NOW(),
            #{item.productSku},
            #{item.productImage},
            #{item.discountRate},
            #{item.typeId},
            #{item.sort}
            )
        </foreach>
    </insert>

    <insert id="batchSaveProductNew">
        insert into
        plus_program_product_new(
        `product_id`,
        `program_id`,
        `config_id`,
        `sort`,
        `model_id`,
        `product_sku`,
        `product_image`,
        `discount_rate`,
        `type_id`,
        `sale_state`,
        `opt_user`,
        `opt_id`)
        values
        <foreach collection="list" item="item" separator="," index="index">
            (
            #{item.productId},
            #{item.programId},
            #{item.configId},
            #{item.sort},
            #{item.modelId},
            #{item.productSku},
            #{item.productImage},
            #{item.discountRate},
            #{item.typeId},
            #{item.saleState},
            #{item.optUser},
            #{item.optId}
            )
        </foreach>
    </insert>

    <select id="getByProductIdAndProgramId"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        select
        <include refid="Base_Column_List"/>
        from plus_program_product_new
        where program_id = #{programId} and product_id = #{productId} and model_id = #{modelId}
        order by id desc limit 1
    </select>


    <delete id="batchDelYygProduct" parameterType="java.util.List">
        delete from plus_program_product_new
        where `id` in
        (
        <foreach collection="list" item="item" separator="," index="index">
            #{item.id}
        </foreach>
        )
    </delete>
    <delete id="delByProgramAndModelAndType">
        delete from
            plus_program_product_new
        where
            program_id = #{programId}
        and
            model_id = #{modelId}
        and
            type_id = #{typeId}
    </delete>
    <delete id="delByProductSkuAndModelId">
        delete from
            plus_program_product_new
        where
            product_sku = #{productSku}
        and
            model_id = #{modelId}
    </delete>

    <select id="getAllProductId" resultType="java.lang.Integer">
        select product_id
        from plus_program_product_new where model_id = #{modelId}
        order by id desc
    </select>
    <select id="selectByTypeId" resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        select
        <include refid="Base_Column_List"/>
        from
            plus_program_product_new
        where
            type_id = #{typeId}
        and model_id = #{modelId}
        and
            sale_state = 1
    </select>
    <select id="selectByProgramId" resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        select
        <include refid="Base_Column_List"/>
        from
            plus_program_product_new
        where
            program_id = #{programId} and model_id = #{modelId}
    </select>

    <select id="getByProductSkuAndProgramId" resultMap="PlusProgramProductNew">
        select
        <include refid="Base_Column_List"/>
        from plus_program_product_new
        where program_id = #{programId} and product_sku = #{productSku} and model_id = #{modelId}
        order by id desc limit 1
    </select>

    <select id="getProductNewList"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        select
        <include refid="Base_Column_List"/>
        from plus_program_product_new
        where
        program_id = #{programId}
        and
        model_id = #{modelId}
        <if test="typeId != null">
            and type_id = #{typeId}
        </if>
        order by id desc
    </select>

    <update id="updateProductNew">
        UPDATE plus_program_product_new
        SET
        <if test="plusProgramProductNew.sort != null">`sort`= #{plusProgramProductNew.sort},</if>
        <if test="plusProgramProductNew.discountRate != null">`discount_rate`= #{plusProgramProductNew.discountRate},</if>
        <if test="plusProgramProductNew.productImage != null">`product_image`= #{plusProgramProductNew.productImage},</if>
        <if test="plusProgramProductNew.optUser != null">`opt_user`=#{plusProgramProductNew.optUser},</if>
        <if test="plusProgramProductNew.optId != null">`opt_id`= #{plusProgramProductNew.optId},</if>
        `bi_time`= now(),
        update_time = now()
        WHERE `id` = #{plusProgramProductNew.id}
    </update>

    <select id="getByProgramAndModel"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
        select
        <include refid="Base_Column_List"/>
        from
        plus_program_product_new
        where
        program_id = #{programId}
        and
        model_id = #{modelId}
        and
        sale_state = 1
    </select>
</mapper>
