<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramLmkVirtualMapper">

    <resultMap id="PlusProgramLmkVirtual"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo">
        <result column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="virtual_goods_id" property="virtualGoodsId"/>
        <result column="sku" property="sku"/>
        <result column="virtual_status" property="virtualStatus"/>
        <result column="img_url" property="imgUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `config_id`,
        `program_id`,
        `product_id`,
        `product_name`,
        `virtual_goods_id`,
        `sku`,
        `virtual_status`,
        `img_url`,
        `create_time`,
        `update_time`,
        `create_user_id`,
        `create_user_name`,
        `update_user_id`,
        `update_user_name`
    </sql>

    <insert id="insertBatch"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_lmk_virtual (`config_id`,
        `program_id`,
        `product_id`,
        `product_name`,
        `virtual_goods_id`,
        `sku`,
        `virtual_status`,
        `img_url`,
        `create_time`,
        `update_time`,
        `create_user_id`,
        `create_user_name`,
        `update_user_id`,
        `update_user_name`)
        VALUES
        <foreach collection="list" item="record" separator=",">
            (#{record.configId},
            #{record.programId},
            #{record.productId},
            #{record.productName},
            #{record.virtualGoodsId},
            #{record.sku},
            #{record.virtualStatus},
            #{record.imgUrl},
            NOW(),
            #{record.updateTime},
            #{record.createUserId},
            #{record.createUserName},
            #{record.updateUserId},
            #{record.updateUserName})
        </foreach>
    </insert>


    <delete id="delete" parameterType="java.lang.Integer">
        DELETE
        FROM plus_program_lmk_virtual
        WHERE `id` = #{id}
    </delete>

    <update id="update"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo">
        UPDATE plus_program_lmk_virtual
        SET
        <if test="plusProgramLmkVirtual.imgUrl != null">`img_url`=
            #{plusProgramLmkVirtual.imgUrl},
        </if>
        <if test="plusProgramLmkVirtual.updateUserId != null">`update_user_id`=
            #{plusProgramLmkVirtual.updateUserId},
        </if>
        <if test="plusProgramLmkVirtual.updateUserName != null">`update_user_name`=
            #{plusProgramLmkVirtual.updateUserName},
        </if>
        update_time = now()
        WHERE `id` = #{plusProgramLmkVirtual.id}
    </update>


    <select id="pageList" resultMap="PlusProgramLmkVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_lmk_virtual
        WHERE program_id = #{vo.programId}
        LIMIT #{vo.startPage}, #{vo.pageSize}
    </select>

    <select id="count" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_program_lmk_virtual
        WHERE program_id = #{vo.programId}
    </select>

    <select id="selectBySkuAndProgramId" resultMap="PlusProgramLmkVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_lmk_virtual
        where program_id = #{programId}
        and sku = #{sku}
        order by id desc limit 1
    </select>

    <select id="selectByProgramId" resultMap="PlusProgramLmkVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_lmk_virtual
        where program_id = #{programId} and virtual_status = 1 order by id
    </select>


    <select id="countByProgramId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_program_lmk_virtual
        where program_id = #{programId}
    </select>

    <select id="selectById" resultMap="PlusProgramLmkVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_lmk_virtual
        where id = #{id}
    </select>

    <select id="selectBySku" resultMap="PlusProgramLmkVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_lmk_virtual
        where sku = #{sku}
    </select>

    <update id="updateStateBySku"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo">
        UPDATE plus_program_lmk_virtual
        SET `virtual_status`= #{virtualStatus},
            `update_user_id`=#{updateUserId},
            `update_user_name`=#{updateUserName},
            `update_time` = now()
        WHERE `sku` = #{sku}
    </update>
</mapper>
