<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramHalfPriceProductMapper">

  <resultMap id="PlusProgramHalfPriceProduct"
    type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramHalfPriceProductPo">
    <result column="id" property="id"/>
    <result column="product_id" property="productId"/>
    <result column="sort" property="sort"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="program_id" property="programId"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`,
        `product_id`,
        `sort`,
        `program_id`,
        `create_time`,
        `update_time`
  </sql>
  <insert id="savePlusProgramHalfPriceProduct" parameterType="java.util.List">
    insert into plus_program_halfprice_product (product_id,create_time,program_id)
    values
    <foreach collection="list" item="item" separator=","
      index="index">
      ( #{item.productId},
      NOW(),
      #{item.programId}
      )
    </foreach>
  </insert>
  <update id="updatePlusProgramHalfPriceProduct">
    update plus_program_halfprice_product
    set
    <if test="product.sort != null">`sort`=
      #{product.sort},
    </if>
    update_time = now()
    where `id` = #{product.id}
  </update>
  <delete id="deletePlusProgramHalfPriceProduct">
    delete from plus_program_halfprice_product
    where `product_id` = #{id}
  </delete>
  <select id="getHalfPriceProduct"
          resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramHalfPriceProductPo">
    select
    <include refid="Base_Column_List"/>
    FROM plus_program_halfprice_product
    order by `sort` asc ,`update_time` desc
    <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
  </select>
  <select id="getHalfPriceProductCount" resultType="java.lang.Integer">
    SELECT count(1)
    FROM plus_program_halfprice_product
  </select>
  <select id="getByProductCodeById" resultType="java.lang.Integer">
    select count(1)
    from plus_program_halfprice_product
    where product_id = #{id}
      and program_id=#{programId}
  </select>
  <select id="getByProductSkuAndProgramId"
          resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
    select
    <include refid="Base_Column_List"/>
    from plus_program_product_new
    where program_id = #{programId}
    and product_sku = #{productSku}
    and model_id = #{modelId}
    order by id desc
  </select>
  <select id="getByProductIdAndProgramId"
          resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo">
    select
    <include refid="Base_Column_List"/>
    from plus_program_product_new
    where program_id = #{programId}
      and product_id = #{productId}
      and model_id = #{modelId}
    order by id desc
  </select>
  <select id="halfPriceProductIdList" resultType="java.lang.Integer">
    select product_id
    from plus_program_halfprice_product
    <where>
      <if test="program_id != null">`program_id`= #{programId} </if>
    </where>
    order by `sort` asc,`update_time` desc
  </select>
</mapper>