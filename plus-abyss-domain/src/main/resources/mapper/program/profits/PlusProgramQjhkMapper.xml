<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramQjhkMapper">

    <resultMap id="PlusProgramQjhk"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramQjhkPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="config_id" property="configId"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `config_id`,
        `discount_rate`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramQjhkPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_qjhk (
        `program_id`,
        `config_id`,
        `discount_rate`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{programId},
        #{configId},
        #{discountRate},
        NOW(),
        #{updateTime}
        )
    </insert>

    <update id="update"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramQjhkPo">
        UPDATE plus_program_qjhk
        SET
        <if test="plusProgramQjhk.discountRate != null">`discount_rate`=
            #{plusProgramQjhk.discountRate},
        </if>
        update_time = now()
        WHERE `id` = #{plusProgramQjhk.id}
    </update>

    <select id="getByProgramId" resultMap="PlusProgramQjhk">
        select
        <include refid="Base_Column_List"/>
        from plus_program_qjhk
        where program_id = #{programId} order by id desc limit 1
    </select>

    <select id="getById" resultMap="PlusProgramQjhk">
        select
        <include refid="Base_Column_List"/>
        from plus_program_qjhk
        where id = #{id}
    </select>

</mapper>
