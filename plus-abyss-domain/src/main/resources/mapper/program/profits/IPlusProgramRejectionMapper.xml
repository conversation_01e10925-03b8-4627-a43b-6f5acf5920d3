<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramRejectionMapper">

    <resultMap id="PlusProgramRejection"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_name" property="couponName"/>
        <result column="coupon_id" property="couponId"/>
        <result column="type" property="type"/>
        <result column="index" property="index"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `coupon_name`,
        `coupon_id`,
        `type`,
        `index`
    </sql>

    <select id="loadPlusProgramRejection" parameterType="java.lang.Integer"
            resultMap="PlusProgramRejection">
        select
        <include refid="Base_Column_List"/>
        from plus_program_rejection
        where `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusProgramRejection">
        select
        <include refid="Base_Column_List"/>
        from plus_program_rejection
        <include refid="whereConditions"/>
        order by `id` desc
        <if test="pageSize != null and pageSize &gt; 0">limit #{startPage},#{pageSize}</if>
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        select count(1)
        from plus_program_rejection
        <include refid="whereConditions"/>
    </select>

    <select id="getRejectionCouponByProgramId" resultMap="PlusProgramRejection">
        select
        <include refid="Base_Column_List"/>
        from plus_program_rejection
        where `program_id` = #{programId}
    </select>

    <select id="getByRejectionIds" parameterType="java.lang.Integer"
            resultMap="PlusProgramRejection">
        select
        <include refid="Base_Column_List"/>
        from plus_program_rejection
        where `id` in (
        <foreach collection="ids" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )

    </select>

    <select id="checkRejectTypeZero" resultType="java.lang.Integer">
        select count(1) from plus_program_rejection
        where program_id = #{programId}
        and type = #{type}
        <if test="id != null ">
            and id != #{id}
        </if>
    </select>

    <sql id="whereConditions">
        <where>
            <if test="programId != null">
                and `program_id` = #{programId}
            </if>
        </where>
    </sql>

    <insert id="savePlusProgramRejection"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo"
            useGeneratedKeys="true" keyProperty="id">
        insert into plus_program_rejection (
        `program_id`,
        `coupon_name`,
        `coupon_id`,
        `type`,
        `index`
        )
        values(
        #{programId},
        #{couponName},
        #{couponId},
        #{type},
        #{index}
        )
    </insert>

    <insert id="batchInsertPlusRejection" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into plus_program_rejection (
        `program_id`,
        `coupon_name`,
        `coupon_id`,
        `type`,
        `index`
        )
        values
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.couponName},
            #{record.couponId},
            #{record.type},
            #{record.index}
            )
        </foreach>
    </insert>

    <delete id="deletePlusProgramRejection">
        delete from plus_program_rejection
        where `id` = #{id}
    </delete>

    <update id="updatePlusProgramRejection"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo">
        update plus_program_rejection
        set
        `type`= #{type,jdbcType=INTEGER}
        where `id` = #{id,jdbcType=INTEGER}
    </update>

</mapper>
