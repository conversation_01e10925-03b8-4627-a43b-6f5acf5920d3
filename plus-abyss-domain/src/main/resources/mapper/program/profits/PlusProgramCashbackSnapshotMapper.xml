<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramCashbackSnapshotMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackSnapshotPo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="model_id" property="modelId"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="order_num" property="orderNum"/>
        <result column="cashback_type" property="cashbackType"/>
        <result column="cashback_amount" property="cashbackAmount"/>
        <result column="goods_url" property="goodsUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, channel_id, config_id, model_id, program_id, plus_order_sn, order_num, cashback_type, cashback_amount, goods_url, create_time, update_time
    </sql>
    <insert id="insertBatch"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackSnapshotPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_cashback_snapshot (user_id, config_id, program_id, model_id, plus_order_sn,
        order_num, cashback_type, cashback_amount,
        goods_url,channel_id, create_time, update_time)
        VALUES
        <foreach collection="snapshots" item="value" separator=",">
            (#{value.userId},
            #{value.configId},
            #{value.programId},
            #{value.modelId},
            #{value.plusOrderSn},
            #{value.orderNum},
            #{value.cashbackType},
            #{value.cashbackAmount},
            #{value.goodsUrl},
            #{value.channelId},
            NOW(),
            NOW())
        </foreach>
    </insert>
    <select id="queryAuthSnapshots"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackSnapshotPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `plus_program_cashback_snapshot`
        WHERE
        plus_order_sn = #{plusOrderSn}
        AND program_id = #{programId}
        and model_id in
        <foreach collection="modelIds" item="modelId" index="index" open="(" separator="," close=")">
            #{modelId}
        </foreach>
        ORDER BY order_num DESC
    </select>

    <select id="querySnapshots"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackSnapshotPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `plus_program_cashback_snapshot`
        WHERE
        plus_order_sn = #{plusOrderSn}
        AND program_id = #{programId} and model_id = #{modelId} ORDER BY order_num DESC
    </select>

    <select id="getSnapshots"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackSnapshotPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `plus_program_cashback_snapshot`
        WHERE
        plus_order_sn in (
        <foreach collection="plusOrderSns" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>
</mapper>
