<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramZskfMapper">

  <resultMap id="PlusProgramZskf" type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramZskfPo">
    <result column="id" property="id"/>
    <result column="config_id" property="configId"/>
    <result column="program_id" property="programId"/>
    <result column="return_url" property="returnUrl"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="create_user_name" property="createUserName"/>
    <result column="update_user_id" property="updateUserId"/>
    <result column="update_user_name" property="updateUserName"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
        ,
        `config_id`,
        `program_id`,
        `return_url`,
        `create_time`,
        `update_time`,
        `create_user_id`,
        `create_user_name`,
        `update_user_id`,
        `update_user_name`
  </sql>

  <insert id="insert" parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramZskfPo"
    useGeneratedKeys="true" keyProperty="id">
    INSERT INTO plus_program_zskf (`config_id`,
                                   `program_id`,
                                   `return_url`,
                                   `create_time`,
                                   `update_time`,
                                   `create_user_id`,
                                   `create_user_name`,
                                   `update_user_id`,
                                   `update_user_name`)
    VALUES (#{configId},
            #{programId},
            #{returnUrl},
            NOW(),
            #{updateTime},
            #{createUserId},
            #{createUserName},
            #{updateUserId},
            #{updateUserName})
  </insert>

  <select id="selectByProgramId"
    resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramZskfPo">
    select
    <include refid="Base_Column_List"/>
    from plus_program_zskf
    where program_id = #{programId} order by id desc limit 1
  </select>

  <select id="countByProgramId" resultType="java.lang.Integer">
    select count(*)
    from plus_program_zskf
    where program_id = #{programId}
  </select>

  <update id="update" parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramZskfPo">
    update plus_program_zskf
    set return_url       = #{returnUrl},
        update_time      = now(),
        update_user_id   = #{updateUserId},
        update_user_name = #{updateUserName}
    where id = #{id}
  </update>

</mapper>