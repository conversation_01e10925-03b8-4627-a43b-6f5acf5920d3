<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramVirtualMapper">

    <resultMap id="PlusProgramVirtual"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo">
        <result column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="virtual_goods_id" property="virtualGoodsId"/>
        <result column="sku" property="sku"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="sell_price" property="sellPrice"/>
        <result column="plus_price" property="plusPrice"/>
        <result column="status" property="status"/>
        <result column="rank_num" property="rankNum"/>
        <result column="img_url" property="imgUrl"/>
        <result column="profit_type_id" property="profitTypeId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_nm" property="updateUserNm"/>
        <result column="model_id" property="modelId"/>
        <result column="min_amount" property="minAmount"/>
        <result column="max_amount" property="maxAmount"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `config_id`,
        `program_id`,
        `product_id`,
        `product_name`,
        `virtual_goods_id`,
        `sku`,
        `discount_rate`,
        `sell_price`,
        CAST((sell_price * discount_rate) AS DECIMAL(10,2)) plus_price,
        `status`,
        `rank_num`,
        `img_url`,
        `profit_type_id`,
        `create_time`,
        `update_time`,
        `update_user_id`,
        `update_user_nm`,
        `model_id`,
        `min_amount`,
        `max_amount`
    </sql>

    <sql id="Base_Column_List2">
        a.`id`,
        a.`config_id`,
        a.`program_id`,
        a.`product_id`,
        a.`product_name`,
        a.`virtual_goods_id`,
        a.`sku`,
        a.`discount_rate`,
        a.`sell_price`,
        CAST((a.sell_price * a.discount_rate) AS DECIMAL(10,2)) plus_price,
        a.`status`,
        a.`rank_num`,
        a.`img_url`,
        a.`profit_type_id`,
        a.`create_time`,
        a.`update_time`,
        a.`update_user_id`,
        a.`update_user_nm`,
        a.`model_id`,
        a.`min_amount`,
        a.`max_amount`
    </sql>

    <sql id="whereConditions">
        <where>
            `program_id` = #{queryMap.q_programId}
            and `model_id` = #{queryMap.q_modelId}
            <if test="queryMap.q_productId != null and queryMap.q_productId != ''">
                and `product_id` = #{queryMap.q_productId}
            </if>
            <if test="queryMap.q_productName != null and queryMap.q_productName != ''">
                and `product_name` like CONCAT('%', #{queryMap.q_productName}, '%')
            </if>
            <if test="queryMap.q_sku != null and queryMap.q_sku != ''">
                and `sku` = #{queryMap.q_sku}
            </if>
        </where>
    </sql>


    <insert id="saveBatch"
            parameterType="java.util.List"
            useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO plus_program_virtual (
        `config_id`,
        `program_id`,
        `product_id`,
        `product_name`,
        `virtual_goods_id`,
        `sku`,
        `discount_rate`,
        `sell_price`,
        `status`,
        `rank_num`,
        `img_url`,
        `profit_type_id`,
        `create_time`,
        `update_time`,
        `update_user_id`,
        `update_user_nm`,
        `model_id`,
        `min_amount`,
        `max_amount`
        )
        VALUES
        <foreach collection="list" item="record" separator=",">
            (
            #{record.configId},
            #{record.programId},
            #{record.productId},
            #{record.productName},
            #{record.virtualGoodsId},
            #{record.sku},
            #{record.discountRate},
            #{record.sellPrice},
            #{record.status},
            #{record.rankNum},
            #{record.imgUrl},
            #{record.profitTypeId},
            NOW(),
            NOW(),
            #{record.updateUserId},
            #{record.updateUserNm},
            #{record.modelId},
            #{record.minAmount},
            #{record.maxAmount}
            )
        </foreach>
    </insert>

    <update id="updateById"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo">
        UPDATE plus_program_virtual
        SET
        <if test="plusProgramVirtual.discountRate != null">`discount_rate`=
            #{plusProgramVirtual.discountRate},
        </if>
        <if test="plusProgramVirtual.rankNum != null">`rank_num`= #{plusProgramVirtual.rankNum},
        </if>
        <if test="plusProgramVirtual.imgUrl != null">`img_url`= #{plusProgramVirtual.imgUrl},</if>
        <if test="plusProgramVirtual.minAmount != null">
            `min_amount`= #{plusProgramVirtual.minAmount},
        </if>
        <if test="plusProgramVirtual.maxAmount != null">
            `max_amount`= #{plusProgramVirtual.maxAmount},
        </if>
        <if test="plusProgramVirtual.updateUserId != null">`update_user_id`=
            #{plusProgramVirtual.updateUserId},
        </if>
        <if test="plusProgramVirtual.updateUserNm != null">`update_user_nm`=
            #{plusProgramVirtual.updateUserNm},
        </if>
        update_time = now()
        WHERE `id` = #{plusProgramVirtual.id}
    </update>

    <update id="syncStatusBySku"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo">
        UPDATE plus_program_virtual
        SET
        <if test="plusProgramVirtual.status != null">`status`= #{plusProgramVirtual.status},</if>
        <if test="plusProgramVirtual.updateUserId != null">`update_user_id`=
            #{plusProgramVirtual.updateUserId},
        </if>
        <if test="plusProgramVirtual.updateUserNm != null">`update_user_nm`=
            #{plusProgramVirtual.updateUserNm},
        </if>
        update_time = now()
        WHERE `sku`= #{plusProgramVirtual.sku}
    </update>

    <delete id="deleteById" parameterType="java.lang.Integer">
        DELETE FROM plus_program_virtual
        WHERE `id` = #{id}
    </delete>

    <select id="selectById" parameterType="java.lang.Integer"
            resultMap="PlusProgramVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusProgramVirtual">
        select
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        <include refid="whereConditions"/>
        order by `rank_num`,`create_time` desc
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>
    <select id="pageListByQuery" parameterType="java.util.Map" resultMap="PlusProgramVirtual">
        select
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where `profit_type_id` = #{param.profitTypeId}
        order by `rank_num` asc ,`update_time` desc
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_program_virtual
        <include refid="whereConditions"/>
    </select>


    <select id="pageListCountByQuery" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_program_virtual
         where profit_type_id = #{param.profitTypeId}
    </select>


    <select id="getByProgramAndSku" parameterType="java.util.Map"
            resultMap="PlusProgramVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where program_id = #{programId}
          and model_id = #{modelId}
        and sku = #{productSku}
        limit 1
    </select>

    <select id="getBySku" parameterType="java.lang.String"
            resultMap="PlusProgramVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where sku = #{productSku}
    </select>


    <select id="getByProgram" parameterType="map"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where program_id = #{programId}
        and model_id = #{modelId}
        and status = 1
        order by id
    </select>

    <select id="getByProgramAndType" parameterType="java.util.Map"
            resultMap="PlusProgramVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where program_id = #{programId}
        and profit_type_id = #{profitTypeId}
        limit 200;
    </select>

    <!-- 批量删除 -->
    <delete id="batchDelete" parameterType="java.util.Map">
        DELETE FROM plus_program_virtual
        where program_id = #{programId}
        and profit_type_id = #{profitTypeId}
    </delete>

    <select id="getVirtualByType" parameterType="java.lang.Integer"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where profit_type_id = #{profitTypeId}
        and status = 1
        order by `rank_num` asc,`update_time` desc
    </select>


    <select id="getVirtualEntityByType" parameterType="java.lang.Integer"
            resultMap="PlusProgramVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where profit_type_id = #{profitTypeId}
    </select>
    <select id="getVirtualById"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        WHERE `id` = #{id}
    </select>

    <select id="getPageVirtualList" resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where program_id = #{programId}
        and model_id = #{modelId}
        and status = 1
        order by id
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="countList" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_program_virtual
        where program_id = #{programId}
        and model_id = #{modelId}
        and status = 1
        order by id
    </select>

    <select id="getByProgramIdAndModelId" parameterType="java.util.Map"
            resultMap="PlusProgramVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual
        where program_id = #{programId}
        and model_id = #{modelId}
        and status = 1
        order by id
    </select>

    <select id="listBySkuAndConfig" resultMap="PlusProgramVirtual">
        SELECT
        <include refid="Base_Column_List2"/>
        FROM plus_program_virtual a inner join plus_program b on a.program_id = b.id
        where a.sku = #{productSku}
        and a.`model_id` = #{modelId}
        and b.config_id in
        <foreach collection="configIdList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <update id="updateImgUrlBatch">
        UPDATE plus_program_virtual
        SET
        <if test="imgUrl != null and imgUrl != ''">
            `img_url`= #{imgUrl},
        </if>
        update_time = now()
        WHERE id in
        <foreach collection="idList" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </update>
</mapper>