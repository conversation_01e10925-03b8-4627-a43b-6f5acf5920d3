<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProductInfoMapper">

  <resultMap id="PlusProductInfo" type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProductInfoPo" >
    <result column="id" property="id" />
    <result column="product_id" property="productId" />
    <result column="product_name" property="productName" />
    <result column="product_sku" property="productSku" />
    <result column="sale_state" property="saleState" />
    <result column="pur_price" property="purPrice" />
    <result column="prot_price" property="protPrice" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_user_name" property="createUserName" />
    <result column="create_time" property="createTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_user_name" property="updateUserName" />
    <result column="update_time" property="updateTime" />
  </resultMap>

  <sql id="Base_Column_List">
    `id`,
        `product_id`,
        `product_name`,
        `product_sku`,
        `sale_state`,
        `pur_price`,
        `prot_price`,
        `create_user_id`,
        `create_user_name`,
        `create_time`,
        `update_user_id`,
        `update_user_name`,
        `update_time`
  </sql>
  <delete id="delById">
    delete from
      plus_product_info
    where
        id = #{id}
  </delete>

  <select id="selectProductBySku" resultMap="PlusProductInfo">
    select
        <include refid="Base_Column_List"/>
    from
        plus_product_info
    where
    product_sku = #{sku}
  </select>

    <select id="selectProductBySkus" resultMap="PlusProductInfo">
        select
        <include refid="Base_Column_List"/>
        from
        plus_product_info
        where
        product_sku in
        <foreach collection="skus" item="sku" open="(" close=")"
                separator=",">
            #{sku}
        </foreach>
    </select>

  <select id="selectProductById" resultMap="PlusProductInfo">
    select
    <include refid="Base_Column_List"/>
    from
    plus_product_info
    where
    id = #{id}
  </select>
  <select id="selectPlusProductList"
          resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProductInfoPo">
    select
    <include refid="Base_Column_List"/>
    from
    plus_product_info
    where 1=1
    <if test="productName != ''and productName != null">
      and product_name like
      concat('%',#{productName},'%')
    </if>
    <if test="productId != ''and productId != null">
      and product_id = #{productId}
    </if>
    <if test="productSku != ''and productSku != null">
      and product_sku = #{productSku}
    </if>
    <if test="onSaleState != ''and onSaleState != null">
      and sale_state = #{onSaleState}
    </if>
    order by create_time desc
    limit #{startPage}, #{pageSize}
  </select>
  <select id="selectPlusProductCount" resultType="java.lang.Integer">
    select
    count(1)
    from
    plus_product_info
    where 1=1
    <if test="productName != ''and productName != null">
      and product_name like
      concat('%',#{productName},'%')
    </if>
    <if test="productId != ''and productId != null">
      and product_id = #{productId}
    </if>
    <if test="productSku != ''and productSku != null">
      and product_sku = #{productSku}
    </if>
    <if test="onSaleState != ''and onSaleState != null">
      and sale_state = #{onSaleState}
    </if>
  </select>


  <insert id="savePlusProduct">
    insert into
      plus_product_info(
      `product_id`,
      `product_name`,
      `product_sku`,
      `pur_price`,
      `prot_price`,
      `create_user_id`,
      `create_user_name`,
      `create_time`,
      `update_user_id`,
      `update_user_name`
    )
    values (
        #{productId},
        #{productName},
        #{productSku},
        #{purPrice},
        #{protPrice},
        #{createUserId},
        #{createUserName},
        now(),
        #{updateUserId},
        #{updateUserName}
           )
  </insert>

  <update id="updatePlusProduct">
    update plus_product_info
    <trim prefix="set" suffixOverrides=",">
      <if test="purPrice!=null and purPrice!=''"> pur_price = #{purPrice},</if>
      <if test="protPrice!=null and protPrice!=''"> prot_price = #{protPrice},</if>
      <if test="updateUserId!=null and updateUserId!=''"> update_user_id = #{updateUserId},</if>
      <if test="updateUserName!=null and updateUserName!=''"> update_user_name = #{updateUserName},</if>
      update_time = now()
    </trim>
    WHERE id = #{id}
  </update>

  <update id="updatePlusProductState">
    update plus_product_info
    <trim prefix="set" suffixOverrides=",">
      <if test="saleState!=null"> sale_state = #{saleState},</if>
      <if test="updateUserId!=null"> update_user_id = #{updateUserId},</if>
      <if test="updateUserName!=null and updateUserName!=''"> update_user_name = #{updateUserName},</if>
      update_time = now()
    </trim>
    WHERE id = #{id}
  </update>

</mapper>
