<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramXFZKMapper">

    <resultMap id="MemberPlusInDisCoupon"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_name" property="couponName"/>
        <result column="index" property="index"/>
        <result column="coupon_id" property="couponId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>


    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `coupon_name`,
        `index`,
        `coupon_id`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="batchInsertPlusProgramXFZK">
        INSERT INTO plus_program_xfzk (
        `program_id`,
        `coupon_name`,
        `index`,
        `coupon_id`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.couponName},
            #{record.index},
            #{record.couponId},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <insert id="batchInsertXFZKCoupon">
        INSERT INTO plus_program_xfzk (
        `program_id`,
        `coupon_name`,
        `index`,
        `coupon_id`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.couponName},
            #{record.index},
            #{record.couponId},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <delete id="deleteMemberPlusInDisCoupon" parameterType="java.lang.Integer">
        DELETE FROM plus_program_xfzk
        WHERE `id` = #{id}
    </delete>

    <select id="getListByProgramId" parameterType="java.lang.Integer"
            resultMap="MemberPlusInDisCoupon">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_xfzk
        where program_id = #{programId}
    </select>

    <select id="getListByProgramIdAndModelId" parameterType="java.lang.Integer"
            resultMap="MemberPlusInDisCoupon">
        SELECT
        i.*
        FROM plus_program_xfzk i
        left join plus_pro_model pm
        on pm.program_id = i.program_id
        where i.program_id = #{programId}
        and pm.model_id = #{modelId}
    </select>
    <select id="getInDisCouponById" parameterType="java.lang.Integer"
            resultMap="MemberPlusInDisCoupon">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_xfzk
        where id = #{id}
    </select>

    <update id="updateMemberPlusInDisCoupon"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo">
        UPDATE plus_program_xfzk SET
        <if test="programId != null and programId !=''">
            `program_id`= #{programId},
        </if>
        <if test="couponName != null and couponName!=''">
            `coupon_name`= #{couponName},
        </if>
        <if test="index != null and index != ''">
            `index`= #{index},
        </if>
        <if test="couponId != null and couponId != ''">
            `coupon_id`= #{couponId},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="pageList"
            resultMap="MemberPlusInDisCoupon">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_xfzk
        where `program_id`= #{programId}
    </select>

    <select id="countXFZK" resultType="integer">
        SELECT
       count(1)
        FROM plus_program_xfzk
        where `program_id`= #{programId}
    </select>


    <select id="getByCouponIds" resultType="java.lang.Integer">
        SELECT coupon_id
        FROM plus_program_xfzk
        where program_id = #{programId} and `coupon_id` in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </select>
</mapper>
