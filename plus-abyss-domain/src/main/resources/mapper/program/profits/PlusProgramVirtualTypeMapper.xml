<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramVirtualTypeMapper">

    <resultMap id="PlusProgramVirtualType" type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo" >
        <result column="id" property="id" />
        <result column="config_id" property="configId" />
        <result column="program_id" property="programId" />
        <result column="type_name" property="typeName" />
        <result column="num_limit" property="numLimit" />
        <result column="type_limit" property="typeLimit" />
        <result column="type_limit_num" property="typeLimitNum" />
        <result column="rank_num" property="rankNum" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user_nm" property="updateUserNm" />
        <result column="parent_id" property="parentId" />
        <result column="type_level" property="typeLevel" />
        <result column="model_id" property="modelId" />
        <result column="market_content" property="marketContent" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `config_id`,
        `program_id`,
        `type_name`,
        `num_limit`,
        `type_limit`,
        `type_limit_num`,
        `rank_num`,
        `create_time`,
        `update_time`,
        `update_user_id`,
        `update_user_nm`,
        `parent_id`,
        `type_level`,
        `model_id`,
        `market_content`
    </sql>

    <insert id="savePlusProgramVirtualType" parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_virtual_type (
            `config_id`,
            `program_id`,
            `type_name`,
            `num_limit`,
            `type_limit`,
            `type_limit_num`,
            `rank_num`,
            `create_time`,
            `parent_id`,
            `type_level`,
            `model_id`,
            `market_content`
        )
        VALUES(
                  #{configId},
                  #{programId},
                  #{typeName},
                  #{numLimit},
                  #{typeLimit},
                  #{typeLimitNum},
                  #{rankNum},
                  NOW(),
                  #{parentId},
                  #{typeLevel},
                  #{modelId},
                  #{marketContent}
              )
    </insert>


    <delete id="deletePlusProgramVirtualType" parameterType="java.lang.Integer">
        DELETE FROM plus_program_virtual_type
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusProgramVirtualType" parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo" >
        UPDATE plus_program_virtual_type
        SET
        <if test="plusProgramVirtualType.typeName != null">`type_name`= #{plusProgramVirtualType.typeName},</if>
        <if test="plusProgramVirtualType.numLimit != null">`num_limit`= #{plusProgramVirtualType.numLimit},</if>
        <if test="plusProgramVirtualType.typeLimit != null">`type_limit`= #{plusProgramVirtualType.typeLimit},</if>
        <if test="plusProgramVirtualType.typeLimitNum != null">`type_limit_num`= #{plusProgramVirtualType.typeLimitNum},</if>
        <if test="plusProgramVirtualType.rankNum != null">`rank_num`= #{plusProgramVirtualType.rankNum},</if>
        <if test="plusProgramVirtualType.updateUserId != null">`update_user_id`= #{plusProgramVirtualType.updateUserId},</if>
        <if test="plusProgramVirtualType.updateUserNm != null">`update_user_nm`= #{plusProgramVirtualType.updateUserNm},</if>
        <if test="plusProgramVirtualType.marketContent != null">`market_content`= #{plusProgramVirtualType.marketContent},</if>
        update_time = now()
        WHERE `id` = #{plusProgramVirtualType.id}
    </update>


    <select id="loadPlusProgramVirtualType" parameterType="java.lang.Integer" resultMap="PlusProgramVirtualType">
        SELECT <include refid="Base_Column_List" />
        FROM plus_program_virtual_type
        WHERE `id` = #{id}
    </select>

    <select id="getVirtualTypeByProgram" parameterType="map" resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo">
        select
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual_type
        where `program_id` = #{programId}
          and `model_id` = #{modelId}
        order by `rank_num` asc ,`update_time` desc
    </select>

    <select id="getTypeByProgram" parameterType="map" resultMap="PlusProgramVirtualType">
        select
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual_type
        where `program_id` = #{programId}
          and `model_id` = #{modelId}
          and `parent_id` is null
    </select>
    <select id="getVirtualTypeByLevel"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo">
        select
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual_type
        where `program_id` = #{programId}
        and `model_id` = #{modelId}
        <if test="typeLevel != null and typeLevel > 0">
            and `type_level` = #{typeLevel}
        </if>
        order by `rank_num` asc ,`update_time` desc
    </select>
    <select id="checkVirtualTypeRankNumExists" resultType="java.lang.Integer">
        select COUNT(1)
        FROM plus_program_virtual_type
        where `program_id` = #{plusProgramVirtualType.programId}
        and `model_id` = #{plusProgramVirtualType.modelId}
        and `rank_num` = #{plusProgramVirtualType.rankNum}
        and `type_level` = #{plusProgramVirtualType.typeLevel}
        <if test="plusProgramVirtualType.parentId != null and plusProgramVirtualType.parentId > 0">
            and `parent_id` = #{plusProgramVirtualType.parentId}
        </if>
        <if test="plusProgramVirtualType.id != null and plusProgramVirtualType.id > 0">
            and `id` != #{plusProgramVirtualType.id}
        </if>
    </select>
    <select id="getTypeByParentId"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo">
        select
        <include refid="Base_Column_List"/>
        FROM plus_program_virtual_type
        where `parent_id` = #{parentId}
        order by `rank_num` asc ,`update_time` desc
    </select>
</mapper>
