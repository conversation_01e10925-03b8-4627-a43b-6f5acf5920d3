<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusLiftAmountMapper">

    <resultMap id="PlusLiftAmount"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="grade" property="grade"/>
        <result column="periods" property="periods"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_user_name" property="updateUserName"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `grade`,
        `periods`,
        `create_user_id`,
        `create_user_name`,
        `create_time`,
        `update_user_id`,
        `update_user_name`,
        `update_time`
    </sql>

    <insert id="savePlusLiftAmount"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_lift_amount (
            `program_id`,
            `grade`,
            `periods`,
            `create_user_id`,
            `create_user_name`,
            `create_time`,
            `update_user_id`,
            `update_user_name`,
            `update_time`
        )
        VALUES(
                  #{programId},
                  #{grade},
                  #{periods},
                  #{createUserId},
                  #{createUserName},
                  NOW(),
                  #{updateUserId},
                  #{updateUserName},
                  NOW()
              )
    </insert>

    <update id="updatePlusLiftAmount"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo">
        UPDATE plus_lift_amount
        SET
        <if test="programId != null">`program_id`=
            #{programId},
        </if>
        <if test="grade != null">`grade`= #{grade},</if>
        <if test="periods != null">`periods`= #{periods},</if>
        <if test="createUserId != null">`create_user_id`=
            #{createUserId},
        </if>
        <if test="createUserName != null">`create_user_name`=
            #{createUserName},
        </if>
        <if test="updateUserId != null">`update_user_id`=
            #{updateUserId},
        </if>
        <if test="updateUserName != null">`update_user_name`=
            #{updateUserName},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="getPlusLiftAmountByProgramId" resultMap="PlusLiftAmount">
        SELECT
            a.*
        FROM plus_lift_amount a
                 left join plus_pro_model m on a.program_id = m.program_id
        where a.program_id = #{programId}
          and m.model_id = 8
    </select>
    <select id="getPlusLiftAmountByProgramIdAndProgramUp"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo">
        SELECT
            a.*
        FROM plus_lift_amount a
                 left join plus_program m on a.program_id = m.id
        where a.program_id = #{programId}
          and m.status = 1

    </select>

    <delete id="deletePlusLiftAmountByProgramId" parameterType="integer">
        delete from plus_lift_amount  where  program_id = #{programId}
    </delete>

</mapper>
