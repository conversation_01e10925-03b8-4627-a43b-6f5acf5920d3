<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusCouponIndexMapper">

    <resultMap id="PlusCouponIndex"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="type" property="type"/>
        <result column="coupon_id" property="couponId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="PlusCouponIndexVo"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="type" property="type"/>
        <result column="coupon_id" property="couponId"/>
    </resultMap>


    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `type`,
        `coupon_id`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusCouponIndex"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_coupon_index (
        `program_id`,
        `type`,
        `coupon_id`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{programId},
        #{type},
        #{couponId},
        NOW(),
        #{updateTime}
        )
    </insert>

    <insert id="insertPlusCouponIndex"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo">
        INSERT INTO plus_coupon_index (
        `program_id`,
        `type`,
        `coupon_id`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{PlusCouponIndex.programId},
        #{PlusCouponIndex.type},
        #{PlusCouponIndex.couponId},
        NOW(),
        #{PlusCouponIndex.updateTime}
        )
    </insert>

    <insert id="batchInsertPlusCouponIndex" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        INSERT INTO plus_coupon_index (
        `program_id`,
        `type`,
        `coupon_id`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.type},
            #{record.couponId},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <delete id="deletePlusCouponIndex" parameterType="java.lang.Integer">
        DELETE FROM plus_coupon_index
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusCouponIndex"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo">
        UPDATE plus_coupon_index
        SET
        <if test="programId != null">`program_id`=
            #{programId},
        </if>
        <if test="type != null">`type`= #{type},</if>
        <if test="couponId != null">`coupon_id`=
            #{couponId},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="loadPlusCouponIndex" parameterType="java.lang.Integer"
            resultMap="PlusCouponIndex">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_coupon_index
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusCouponIndex">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_coupon_index
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_coupon_index
    </select>

    <select id="getCouponByProgramIdAndType" resultMap="PlusCouponIndex">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_coupon_index
        where program_id = #{programId}
        and type = #{type}
    </select>

    <select id="getPlusCouponIndexByProgramId" resultMap="PlusCouponIndex">
        SELECT
        i.*
        FROM plus_coupon_index i
        left join plus_pro_model pm
        on i.program_id = pm.program_id
        where i.program_id = #{programId}
        and i.type = #{type}
        and pm.model_id = #{modelId}
    </select>

    <select id="getCouponByProgramIdAndCouponType" resultMap="PlusCouponIndex">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_coupon_index
        where program_id = #{programId}
        and type in
        <foreach collection="couponTypeList" item="couponType" index="index" open="(" separator=","
                close=")">
            #{couponType}
        </foreach>
    </select>

    <select id="getCouponByProgramId" resultMap="PlusCouponIndex">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_coupon_index
        where program_id = #{programId}
    </select>

    <select id="getPlusCouponIndexPage" resultMap="PlusCouponIndexVo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_coupon_index
        where program_id = #{programId} and `type` = #{type}
        order by id desc
    </select>

    <select id="pageListCountByType" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_coupon_index
        where  program_id = #{programId} and `type` = #{type}
    </select>

    <select id="getByCouponIds" resultType="java.lang.Integer">
        SELECT coupon_id
        FROM plus_coupon_index
        where program_id = #{programId} and `type` = #{type} and `coupon_id` in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )

    </select>


    <insert id="batchInsertPlusCouponIndexVo">
        INSERT INTO plus_coupon_index (
        `program_id`,
        `type`,
        `coupon_id`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.type},
            #{record.couponId},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>
</mapper>
