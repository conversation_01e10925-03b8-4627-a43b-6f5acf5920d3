<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductTypeMapper">

  <resultMap id="PlusProgramProductType" type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo" >
    <result column="id" property="id" />
    <result column="parent_id" property="parentId" />
    <result column="config_id" property="configId" />
    <result column="program_id" property="programId" />
    <result column="type_name" property="typeName" />
    <result column="type_level" property="typeLevel" />
    <result column="model_id" property="modelId" />
    <result column="num_limit" property="numLimit" />
    <result column="type_limit" property="typeLimit" />
    <result column="type_limit_num" property="typeLimitNum" />
    <result column="rank_num" property="rankNum" />
    <result column="market_content" property="marketContent" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_user_nm" property="updateUserNm" />
  </resultMap>

  <sql id="Base_Column_List">
    `id`,
        `parent_id`,
        `config_id`,
        `program_id`,
        `type_name`,
        `type_level`,
        `model_id`,
        `num_limit`,
        `type_limit`,
        `type_limit_num`,
        `rank_num`,
        `market_content`,
        `create_time`,
        `update_time`,
        `update_user_id`,
        `update_user_nm`
  </sql>

  <insert id="saveProductType" keyProperty="id" useGeneratedKeys="true">
    insert into
      plus_program_product_type(
      `config_id`,
      `program_id`,
      `type_name`,
      `type_level`,
      `model_id`,
      `num_limit`,
      `type_limit`,
      `type_limit_num`,
      `rank_num`,
      `market_content`,
      `update_user_id`,
      `update_user_nm`
    )values (
              #{configId},
              #{programId},
              #{typeName},
              #{typeLevel},
              #{modelId},
              #{numLimit},
              #{typeLimit},
              #{typeLimitNum},
              #{rankNum},
              #{marketContent},
              #{updateUserId},
              #{updateUserNm}
            )
  </insert>
  <delete id="deleteProductTypeById">
    delete from
      plus_program_product_type
    where
      id = #{id}
  </delete>
  <update id="updateProductType">
    update plus_program_product_type
    <trim prefix="set" suffixOverrides=",">
      <if test="typeName!=null and typeName!=''"> type_name = #{typeName},</if>
      <if test="numLimit!=null and numLimit!=''"> num_limit = #{numLimit},</if>
      <if test="typeLimit!=null and typeLimit!=''"> type_limit = #{typeLimit},</if>
      <if test="typeLimitNum!=null and typeLimitNum != ''"> type_limit_num = #{typeLimitNum},</if>
      <if test="rankNum!=null and rankNum != ''"> rank_num = #{rankNum},</if>
      <if test="updateUserId!=null and updateUserId != ''"> update_user_id = #{updateUserId},</if>
      <if test="updateUserNm!=null and updateUserNm != ''"> update_user_nm = #{updateUserNm},</if>
      update_time=now()
    </trim>
    WHERE id = #{id}
  </update>
  <select id="getByProgramAndModel"
          resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo">
    select
    <include refid="Base_Column_List"/>
    from
    plus_program_product_type
    where
    program_id = #{programId}
    and  model_id = #{modelId}
    order by
    rank_num
    asc
  </select>
  <select id="selectRankNumList" resultType="java.lang.Integer">
    select
      rank_num
    from
      plus_program_product_type
    where
      program_id = #{programId}
      and
      model_id = #{modelId}
  </select>
  <select id="getProductTypeById"
          resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo">
    select
    <include refid="Base_Column_List"/>
    from
    plus_program_product_type
    where
    id = #{id}
  </select>
  <select id="selectTypeListByProgram"
          resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo">
    select
    <include refid="Base_Column_List"/>
    from
    plus_program_product_type
    where
    program_id = #{programId}
    and  model_id = #{modelId}
    order by
    rank_num
    asc
  </select>

    <select id="selectTypeById" resultMap="PlusProgramProductType">
        select
        <include refid="Base_Column_List"/>
        from
        plus_program_product_type
        where
        id = #{id}
    </select>

    <select id="selectTypeListByProgramId" resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo">
        select
        <include refid="Base_Column_List"/>
        from
        plus_program_product_type
        where
        program_id = #{programId}
    </select>

    <insert id="batchInsertType">
        INSERT INTO plus_program_product_type (
        `config_id`,
        `program_id`,
        `type_name`,
        `type_level`,
        `model_id`,
        `num_limit`,
        `type_limit`,
        `type_limit_num`,
        `rank_num`,
        `market_content`,
        `update_user_id`,
        `update_user_nm`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.configId},
            #{record.programId},
            #{record.typeName},
            #{record.typeLevel},
            #{record.modelId},
            #{record.numLimit},
            #{record.typeLimit},
            #{record.typeLimitNum},
            #{record.rankNum},
            #{record.marketContent},
            #{record.updateUserId},
            #{record.updateUserNm}
            )
        </foreach>
    </insert>
</mapper>