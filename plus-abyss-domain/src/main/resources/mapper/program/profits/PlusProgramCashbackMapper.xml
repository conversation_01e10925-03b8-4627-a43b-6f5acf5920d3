<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramCashbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo">
        <id column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="order_num" property="orderNum"/>
        <result column="cashback_type" property="cashbackType"/>
        <result column="cashback_amount" property="cashbackAmount"/>
        <result column="buy_url" property="buyUrl"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_user_name" property="createUserName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="min_amount" property="minAmount"/>
        <result column="max_amount" property="maxAmount"/>
        <result column="periods" property="periods"/>
        <result column="first_scale" property="firstScale"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , config_id, program_id, order_num, cashback_type, cashback_amount, buy_url, create_user_id,create_user_name,
             create_time, update_time,min_amount,max_amount,periods,first_scale
    </sql>
    <delete id="deleteByProgramIdAndConfigId">
        DELETE
        FROM plus_program_cashback
        WHERE program_id = #{programId}
          and config_id = #{configId} and model_id = #{modelId}
    </delete>

    <select id="queryByProgramIdAndConfigId"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo">
        select
        <include refid="Base_Column_List"/>
        from plus_program_cashback where config_id=#{configId} and program_id=#{programId} and model_id = #{modelId}
    </select>

    <select id="queryByProgramId"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo">
        select
        <include refid="Base_Column_List"/>
        from plus_program_cashback where program_id=#{programId} and model_id = #{modelId}
    </select>

    <select id="countByProgramId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_program_cashback
        where program_id = #{programId} and model_id = #{modelId}
    </select>

    <select id="countList" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_program_cashback
        where config_id=#{configId} and program_id = #{programId} and model_id = #{modelId}
    </select>

    <insert id="insertBatch"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_cashback (config_id, program_id, model_id, order_num,
        cashback_type,
        cashback_amount, buy_url, create_user_id,create_user_name, create_time, update_time,
        min_amount,max_amount, periods, first_scale)
        VALUES
        <foreach collection="cashbacks" item="value" separator=",">
            (#{value.configId},
            #{value.programId},
            #{value.modelId},
            #{value.orderNum},
            #{value.cashbackType},
            #{value.cashbackAmount},
            #{value.buyUrl},
            #{value.createUserId},
            #{value.createUserName},
            NOW(),
            NOW(),
            #{value.minAmount},
            #{value.maxAmount},
            #{value.periods},
            #{value.firstScale})
        </foreach>
    </insert>

    <insert id="insert"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_cashback (config_id, program_id, model_id, order_num,
                                           cashback_type,
                                           cashback_amount, buy_url, create_user_id,
                                           create_user_name, create_time, update_time, min_amount,
                                           max_amount, periods, first_scale)
        VALUES (#{configId},
                #{programId},
                #{modelId},
                #{orderNum},
                #{cashbackType},
                #{cashbackAmount},
                #{buyUrl},
                #{createUserId},
                #{createUserName},
                NOW(),
                NOW(),
                #{minAmount},
                #{maxAmount},
                #{periods},
                #{firstScale})
    </insert>

    <select id="getPageList"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo">
        select
        <include refid="Base_Column_List"/>
        from plus_program_cashback where config_id=#{configId} and program_id=#{programId} and model_id = #{modelId}
        order by id
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="getById"
            resultType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo">
        select
        <include refid="Base_Column_List"/>
        from plus_program_cashback where id = #{id}
    </select>

    <update id="updateById"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo">
        update plus_program_cashback set
        <if test="cashback.cashbackAmount != null">`cashback_amount`=
            #{cashback.cashbackAmount},
        </if>
        <if test="cashback.minAmount != null">`min_amount`=
            #{cashback.minAmount},
        </if>
        <if test="cashback.maxAmount != null">`max_amount`=
            #{cashback.maxAmount},
        </if>
        <if test="cashback.periods != null">`periods`=
            #{cashback.periods},
        </if>
        <if test="cashback.firstScale != null">`first_scale`=
            #{cashback.firstScale},
        </if>
        update_time = NOW() where id = #{cashback.id}
    </update>

    <delete id="deleteById">
        DELETE
        FROM plus_program_cashback
        WHERE id = #{id}
    </delete>

</mapper>
