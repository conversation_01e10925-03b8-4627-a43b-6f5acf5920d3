<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramSrlMapper">

  <resultMap id="PlusProgramSrl" type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramSrlPo" >
    <result column="id" property="id" />
    <result column="config_id" property="configId" />
    <result column="program_id" property="programId" />
    <result column="img_url" property="imgUrl" />
    <result column="create_time" property="createTime" />
    <result column="update_time" property="updateTime" />
    <result column="create_user_id" property="createUserId" />
    <result column="create_user_name" property="createUserName" />
    <result column="update_user_id" property="updateUserId" />
    <result column="update_user_name" property="updateUserName" />
  </resultMap>

  <sql id="Base_Column_List">
    `id`,
        `config_id`,
        `program_id`,
        `img_url`,
        `create_time`,
        `update_time`,
        `create_user_id`,
        `create_user_name`,
        `update_user_id`,
        `update_user_name`
  </sql>

  <insert id="insert" parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramSrlPo" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO plus_program_srl (
      `config_id`,
      `program_id`,
      `img_url`,
      `create_time`,
      `update_time`,
      `create_user_id`,
      `create_user_name`,
      `update_user_id`,
      `update_user_name`
    )
    VALUES(
            #{configId},
            #{programId},
            #{imgUrl},
            NOW(),
            #{updateTime},
            #{createUserId},
            #{createUserName},
            #{updateUserId},
            #{updateUserName}
          )
  </insert>


  <update id="update" parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramSrlPo" >
    UPDATE plus_program_srl
    SET
    <if test="imgUrl != null">`img_url`= #{imgUrl},</if>
    <if test="updateUserId != null">`update_user_id`= #{updateUserId},</if>
    <if test="updateUserName != null">`update_user_name`= #{updateUserName},</if>
    update_time = now()
    WHERE `id` = #{id}
  </update>


  <select id="selectByProgramId" parameterType="java.lang.Integer" resultMap="PlusProgramSrl">
    SELECT <include refid="Base_Column_List" />
    FROM plus_program_srl
    WHERE program_id = #{programId} order by id desc limit 1
  </select>

  <select id="countByProgramId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    SELECT count(*)
    FROM plus_program_srl
    WHERE program_id = #{programId}
  </select>

</mapper>