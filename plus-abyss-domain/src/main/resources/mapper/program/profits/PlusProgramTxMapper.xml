<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramTxMapper">

  <resultMap id="PlusProgramTx" type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTxPo">
    <result column="id" property="id"/>
    <result column="config_id" property="configId"/>
    <result column="program_id" property="programId"/>
    <result column="img_url" property="imgUrl"/>
    <result column="return_url" property="returnUrl"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
    <result column="create_user_id" property="createUserId"/>
    <result column="create_user_name" property="createUserName"/>
    <result column="update_user_id" property="updateUserId"/>
    <result column="update_user_name" property="updateUserName"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
        ,
        `config_id`,
        `program_id`,
        `img_url`,
        `return_url`,
        `create_time`,
        `update_time`,
        `create_user_id`,
        `create_user_name`,
        `update_user_id`,
        `update_user_name`
  </sql>

  <insert id="insertBatch" parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTxPo"
    useGeneratedKeys="true" keyProperty="id">
    INSERT INTO plus_program_tx (
    `config_id`,
    `program_id`,
    `img_url`,
    `return_url`,
    `create_time`,
    `update_time`,
    `create_user_id`,
    `create_user_name`,
    `update_user_id`,
    `update_user_name`
    )
    VALUES
    <foreach collection="list" item="record" separator=",">
      (
      #{record.configId},
      #{record.programId},
      #{record.imgUrl},
      #{record.returnUrl},
      NOW(),
      #{record.updateTime},
      #{record.createUserId},
      #{record.createUserName},
      #{record.updateUserId},
      #{record.updateUserName}
      )
    </foreach>
  </insert>

  <delete id="deleteById" parameterType="java.lang.Integer">
    DELETE
    FROM plus_program_tx
    WHERE id = #{id}
  </delete>


  <select id="selectByProgramId" parameterType="java.lang.Integer" resultMap="PlusProgramTx">
    SELECT
    <include refid="Base_Column_List"/>
    FROM plus_program_tx
    WHERE program_id = #{programId}
  </select>

  <select id="countByProgramId" parameterType="java.lang.Integer" resultType="java.lang.Integer">
    SELECT count(*)
    FROM plus_program_tx
    WHERE program_id = #{programId}
  </select>

  <update id="update">
    update plus_program_tx
    set img_url=#{imgUrl},
        return_url=#{returnUrl},
        update_user_id=#{updateUserId},
        update_user_name = #{updateUserName},
        update_time=now()
    where id = #{id}
  </update>

</mapper>