<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramTaskMapper">

    <resultMap id="PlusProgramTask"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="orders_number" property="ordersNumber"/>
        <result column="coupon_name" property="couponName"/>
        <result column="coupon_id" property="couponId"/>
        <result column="index" property="index"/>
        <result column="order_price" property="orderPrice"/>
    </resultMap>

    <resultMap id="PlusCouponIndex"
            type="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="orders_number" property="ordersNumber"/>
        <result column="order_price" property="orderPrice"/>
        <result column="index" property="index"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `orders_number`,
        `coupon_name`,
        `coupon_id`,
        `index`,
        `order_price`
    </sql>

    <select id="loadPlusProgramTask" parameterType="java.lang.Integer"
            resultMap="PlusProgramTask">
        select
        <include refid="Base_Column_List"/>
        from plus_program_task
        where `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusProgramTask">
        select
        <include refid="Base_Column_List"/>
        from plus_program_task
        where `program_id` =#{programId}
        limit #{start},#{size}
    </select>

    <sql id="whereConditions">
        <where>
            <if test="param1.q_index != null and param1.q_index != ''">
                and `index` = #{param1.q_index}
            </if>
            <if test="param1.q_programId != null and param1.q_programId != ''">
                and `program_id` = #{param1.q_programId}
            </if>
        </where>
    </sql>

    <select id="getTaskCouponByProgramId" resultMap="PlusProgramTask">
        select
        <include refid="Base_Column_List"/>
        from plus_program_task
        where `program_id` = #{programId}
        order by orders_number
    </select>

    <select id="getmMaxOrdersNumberDate" resultType="java.lang.Integer">
        select max(orders_number)
        from plus_program_task
        where program_id = #{programId}
    </select>
    <select id="getCountByProgramId" resultType="java.lang.Integer">
        select count(*) from plus_program_task where program_id = #{programId}
    </select>


    <insert id="savePlusProgramTask"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo"
            useGeneratedKeys="true" keyProperty="id">
        insert into plus_program_task (
        `program_id`,
        `orders_number`,
        `order_price`,
        `coupon_name`,
        `coupon_id`,
        `index`
        )
        values(
        #{programId},
        #{ordersNumber},
        #{orderPrice},
        #{couponName},
        #{couponId},
        #{index}
        )
    </insert>

    <insert id="insertPlusProgramTask"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo">
        insert into plus_program_task (
        `program_id`,
        `orders_number`,
        `order_price`,
        `coupon_name`,
        `coupon_id`,
        `index`
        )
        values(
        #{programId},
        #{ordersNumber},
        #{orderPrice},
        #{couponName},
        #{couponId},
        #{index}
        )
    </insert>

    <insert id="batchInsertPlusProgramTask" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into plus_program_task (
        `program_id`,
        `orders_number`,
        `order_price`,
        `coupon_name`,
        `coupon_id`,
        `index`
        )
        values
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.ordersNumber},
            #{record.orderPrice},
            #{record.couponName},
            #{record.couponId},
            #{record.index}
            )
        </foreach>
    </insert>

    <delete id="deletePlusProgramTask">
        delete from plus_program_task
        where `id` = #{id}
    </delete>

    <update id="updatePlusProgramTask"
            parameterType="com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo">
        update plus_program_task
        set
        `order_price`= #{PlusProgramTask.orderPrice},
        `orders_number`= #{PlusProgramTask.ordersNumber}
        where `id` = #{PlusProgramTask.id}
    </update>
</mapper>
