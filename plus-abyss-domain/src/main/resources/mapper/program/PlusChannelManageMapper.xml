<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusChannelManageMapper">

    <resultMap id="PlusChannelManage" type="com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo" >
        <result column="id" property="id" />
        <result column="channel_id" property="channelId" />
        <result column="channel_name" property="channelName" />
        <result column="state" property="state" />
        <result column="plus_config_ids" property="plusConfigIds" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="public_key" property="publicKey" />
        <result column="private_key" property="privateKey" />
    </resultMap>

    <resultMap id="PlusChannelManageMap" type="com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelManagerEntity" >
        <result column="id" property="id" />
        <result column="channel_id" property="channelId" />
        <result column="channel_name" property="channelName" />
        <result column="state" property="state" />
        <result column="plus_config_ids" property="plusConfigIds" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="public_key" property="publicKey" />
        <result column="private_key" property="privateKey" />
        <collection property="plusChannelFunctions" ofType="com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelFunctionEntity">
            <result column="fid" property="id" />
            <result column="manage_id" property="manageId" />
            <result column="config_id" property="configId" />
            <result column="open_mode" property="openMode" />
            <result column="plus_profit" property="plusProfit" />
            <result column="pricing_mode" property="pricingMode" />
            <result column="pay_later_deduct" property="payLaterDeduct" />
            <result column="rapid_return_card" property="rapidReturnCard" />
            <result column="note_reach" property="noteReach" />
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `channel_id`,
        `channel_name`,
        `state`,
        `plus_config_ids`,
        `public_key`,
        `private_key`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="Base_Column_List_M">
        m.`id`,
        m.`channel_id`,
        m.`channel_name`,
        m.`state`,
        m.`plus_config_ids`,
        m.`public_key`,
        m.`private_key`,
        m.`create_time`,
        m.`update_time`
    </sql>

    <sql id="Base_Column_List_Function">
        f.`id` fid,
        f.`manage_id`,
        f.`config_id`,
        f.`open_mode`,
        f.`plus_profit`,
        f.`pricing_mode`,
        f.`pay_later_deduct`,
        f.`rapid_return_card`,
        f.`note_reach`
    </sql>

    <insert id="savePlusChannelManage" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_channel_manage (
            `channel_id`,
            `channel_name`,
            `state`,
            `plus_config_ids`,
            `public_key`,
            `private_key`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{channelId},
                  #{channelName},
                  #{state},
                  #{plusConfigIds},
                  #{publicKey},
                  #{privateKey},
                  NOW(),
                  #{updateTime}
              )
    </insert>


    <insert id="insertPlusChannelManage" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo">
        INSERT INTO plus_channel_manage (
            `channel_id`,
            `channel_name`,
            `state`,
            `plus_config_ids`,
            `public_key`,
            `private_key`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{plusChannelManage.channelId},
                  #{plusChannelManage.channelName},
                  #{plusChannelManage.state},
                  #{plusChannelManage.plusConfigIds},
                  #{plusChannelManage.publicKey},
                  #{plusChannelManage.privateKey},
                  NOW(),
                  #{plusChannelManage.updateTime}
              )
    </insert>



    <delete id="deletePlusChannelManage" parameterType="java.lang.Integer">
        DELETE FROM plus_channel_manage
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusChannelManage" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo">
        UPDATE plus_channel_manage
        SET
        <if test="plusChannelManage.state != null">`state`= #{plusChannelManage.state},</if>
        <if test="plusChannelManage.plusConfigIds != null">`plus_config_ids`= #{plusChannelManage.plusConfigIds},</if>
        update_time = now()
        WHERE `id` = #{plusChannelManage.id}
    </update>


    <select id="loadPlusChannelManage" parameterType="java.lang.Integer" resultMap="PlusChannelManage">
        SELECT <include refid="Base_Column_List" />
        FROM plus_channel_manage
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusChannelManage">
        SELECT <include refid="Base_Column_List" />
        FROM plus_channel_manage
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_channel_manage
    </select>

    <select id="selectList"
            resultMap="PlusChannelManage">
        select
        <include refid="Base_Column_List"/>
        from plus_channel_manage
        <where>
            <if test="channelManageDTO.channelId != null">
                channel_id = #{channelManageDTO.channelId}
            </if>
            <if test="channelManageDTO.state != null  ">
                and `state` = #{channelManageDTO.state}
            </if>
        </where>
    </select>

    <select id="coutList" resultType="java.lang.Integer">
        select count(id) from plus_channel_manage
        <where>
            <if test="channelManageDTO.channelId != null">
                channel_id = #{channelManageDTO.channelId}
            </if>
            <if test="channelManageDTO.state != null  ">
                and `state` = #{channelManageDTO.state}
            </if>
        </where>
    </select>

    <select id="getByChannelId"
            resultMap="PlusChannelManage">
        select
        <include refid="Base_Column_List"/>
        from plus_channel_manage where channel_id = #{channelId}
    </select>

    <select id="getByConfig" resultMap="PlusChannelManage">
        select <include refid="Base_Column_List_M"/> from plus_channel_manage m left join plus_channel_function f on m.id = f.manage_id
        where m.state =3
        <if test="configType == '1'">
            and pricing_mode like concat('%','2','%')
        </if>
    </select>

    <select id="getChannelList" resultMap="PlusChannelManageMap">
        select <include refid="Base_Column_List_M"/>,<include refid="Base_Column_List_Function"/>
        from plus_channel_manage m left join plus_channel_function f on m.id = f.manage_id
        <where>
            <if test="state != null  ">
                and state = #{state}
            </if>
        </where>
    </select>

</mapper>
