<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusQjhkCouponConfigMapper">

    <resultMap id="PlusQjhkCouponConfig" type="com.juzifenqi.plus.module.program.repository.po.PlusQjhkCouponConfigPo" >
        <result column="id" property="id" />
        <result column="min_amount" property="minAmount" />
        <result column="max_amount" property="maxAmount" />
        <result column="period_num" property="periodNum" />
        <result column="coupon_id" property="couponId" />
        <result column="state" property="state" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `min_amount`,
        `max_amount`,
        `period_num`,
        `coupon_id`,
        `state`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusQjhkCouponConfigPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_qjhk_coupon_config (
        `min_amount`,
        `max_amount`,
        `period_num`,
        `coupon_id`,
        `state`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{minAmount},
        #{maxAmount},
        #{periodNum},
        #{couponId},
        1,
        NOW(),
        NOW()
        )
    </insert>

    <select id="selectEffectiveList" resultMap="PlusQjhkCouponConfig">
        select <include refid="Base_Column_List"/> from plus_qjhk_coupon_config
        where state = 1 order by id
    </select>

    <select id="getPageList" resultMap="PlusQjhkCouponConfig">
        select
        <include refid="Base_Column_List"/>
        from plus_qjhk_coupon_config
        where 1=1
        <if test="periodNum != null">
            and period_num = #{periodNum}
        </if>
        order by id
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="countList" resultType="java.lang.Integer">
        select
        count(*)
        from plus_qjhk_coupon_config
        where 1=1
        <if test="periodNum != null">
            and period_num = #{periodNum}
        </if>
    </select>

    <select id="getById" resultMap="PlusQjhkCouponConfig">
        select
        <include refid="Base_Column_List"/>
        from plus_qjhk_coupon_config
        where id = #{id}
    </select>

    <update id="updateById">
        update plus_qjhk_coupon_config set
        <if test="config.minAmount != null">
            min_amount = #{config.minAmount},
        </if>
        <if test="config.maxAmount != null">
            max_amount = #{config.maxAmount},
        </if>
        <if test="config.periodNum != null">
            period_num = #{config.periodNum},
        </if>
        <if test="config.couponId != null">
            coupon_id = #{config.couponId},
        </if>
        update_time = NOW()
        where id = #{config.id}
    </update>

    <update id="updateStateById">
        update plus_qjhk_coupon_config set
        state = #{state},
        update_time = NOW()
        where id = #{id}
    </update>

</mapper>
