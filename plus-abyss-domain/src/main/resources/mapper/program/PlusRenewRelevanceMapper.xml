<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusRenewRelevanceMapper">

    <resultMap id="PlusRenewRelevance" type="com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo" >
        <result column="id" property="id" />
        <result column="program_id" property="programId" />
        <result column="is_renew" property="isRenew" />
        <result column="renew_price" property="renewPrice" />
        <result column="renew_open_time" property="renewOpenTime" />
        <result column="is_show_frame" property="isShowFrame" />
        <result column="frame_image" property="frameImage" />
        <result column="frame_type" property="frameType" />
        <result column="show_interval" property="interval" />
        <result column="alternate_program" property="alternateProgram" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="bi_time" property="biTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `is_renew`,
        `renew_price`,
        `renew_open_time`,
        `is_show_frame`,
        `frame_image`,
        `frame_type`,
        `show_interval`,
        `alternate_program`,
        `create_time`,
        `update_time`,
        `bi_time`
    </sql>

    <insert id="savePlusRenewRelevance" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_renew_relevance (
            `program_id`,
            `is_renew`,
            `renew_price`,
            `renew_open_time`,
            `is_show_frame`,
            `frame_image`,
            `frame_type`,
            `show_interval`,
            `alternate_program`,
            `create_time`,
            `update_time`,
            `bi_time`
        )
        VALUES(
                  #{programId},
                  #{isRenew},
                  #{renewPrice},
                  #{renewOpenTime},
                  #{isShowFrame},
                  #{frameImage},
                  #{frameType},
                  #{interval},
                  #{alternateProgram},
                  NOW(),
                  NOW(),
                  NOW()
              )
    </insert>

    <update id="updatePlusRenewRelevance" parameterType="com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo" >
        UPDATE plus_renew_relevance
        SET
        <if test="plusRenewRelevance.programId != ''">`program_id`= #{plusRenewRelevance.programId},</if>
        <if test="plusRenewRelevance.renewOpenTime != null">`renew_open_time`= #{plusRenewRelevance.renewOpenTime},</if>
        <if test="plusRenewRelevance.interval != ''">`show_interval`= #{plusRenewRelevance.interval},</if>
        <if test="plusRenewRelevance.frameImage != ''">`frame_image`= #{plusRenewRelevance.frameImage},</if>
        `alternate_program`= #{plusRenewRelevance.alternateProgram},
        `frame_type`= #{plusRenewRelevance.frameType},
        `is_renew`= #{plusRenewRelevance.isRenew},
        `renew_price`= #{plusRenewRelevance.renewPrice},
        `is_show_frame`= #{plusRenewRelevance.isShowFrame},
        update_time = now()
        WHERE `id` = #{plusRenewRelevance.id}
    </update>


    <select id="loadPlusRenewRelevance" parameterType="java.lang.Integer" resultMap="PlusRenewRelevance">
        SELECT <include refid="Base_Column_List" />
        FROM plus_renew_relevance
        WHERE `id` = #{id}
    </select>

    <select id="getRenewInfoByProgramId" parameterType="java.lang.Integer" resultMap="PlusRenewRelevance">
        SELECT <include refid="Base_Column_List" />
        FROM plus_renew_relevance
        WHERE `program_id` = #{programId}
    </select>
</mapper>
