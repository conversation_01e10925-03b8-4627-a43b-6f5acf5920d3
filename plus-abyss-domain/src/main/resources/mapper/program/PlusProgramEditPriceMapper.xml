<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusProgramEditPriceMapper">

    <resultMap id="MemberPlusEditPrice"
            type="com.juzifenqi.plus.module.program.repository.po.PlusProgramEditPricePo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="price" property="price"/>
        <result column="old_price" property="oldPrice"/>
        <result column="line_price" property="linePrice"/>
        <result column="old_line_price" property="oldLinePrice"/>
        <result column="result" property="result"/>
        <result column="execute_time" property="executeTime"/>
        <result column="opt_id" property="optId"/>
        <result column="opt_name" property="optName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `price`,
        `old_price`,
        `line_price`,
        `old_line_price`,
        `result`,
        `execute_time`,
        `opt_id`,
        `opt_name`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusEditPrice"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProgramEditPricePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_edit_price (
            `program_id`,
            `price`,
            `old_price`,
            `line_price`,
            `old_line_price`,
            `result`,
            `execute_time`,
            `opt_id`,
            `opt_name`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{programId},
                  #{price},
                  #{oldPrice},
                  #{linePrice},
                  #{oldLinePrice},
                  #{result},
                  #{executeTime},
                  #{optId},
                  #{optName},
                  NOW(),
                  NOW()
              )
    </insert>
    <update id="updateResultById">
        update member_plus_edit_price set result = #{result}, update_time = now()
        where id = #{id}
    </update>
    <update id="updateResultByTimeAndProgramIdAndIgnoreId">
        update member_plus_edit_price set result = #{nowResult}, update_time = now()
        where  program_id = #{programId} and id != #{id} and result = #{result}
    </update>

    <select id="getMemberPlusEditPriceByProgram" parameterType="java.lang.Integer"
            resultMap="MemberPlusEditPrice">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_edit_price
        WHERE `program_id` = #{programId} order by  create_time desc
    </select>
    <select id="getMemberPlusEditPriceByProgramPage"
            resultMap="MemberPlusEditPrice">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_edit_price
        WHERE `program_id` = #{programId} order by create_time desc limit
        #{start},#{size}
    </select>
    <select id="countMemberPlusEditPriceByProgram" parameterType="integer"
            resultType="integer">
        SELECT
            count(1)
        FROM member_plus_edit_price
        WHERE `program_id` = #{programId}
    </select>

    <select id="getLastDataByResultAndTime" resultMap="MemberPlusEditPrice">
        SELECT t.*
        FROM
            (
                SELECT
                    program_id,
                    max(create_time) as create_time
                FROM
                    member_plus_edit_price
                GROUP BY program_id
            ) a
                LEFT JOIN
            member_plus_edit_price t
            ON t.program_id=a.program_id and t.create_time = a.create_time
        where t.execute_time &lt;= now() and t.result = #{result}
    </select>

</mapper>
