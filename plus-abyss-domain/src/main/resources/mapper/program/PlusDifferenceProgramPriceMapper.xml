<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusDifferenceProgramMapper">

    <resultMap id="PlusDifferenceProgramPriceMap"
            type="com.juzifenqi.plus.module.program.repository.po.price.PlusDifferenceProgramPricePo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="price_id" property="priceId"/>
        <result column="grade" property="grade"/>
        <result column="available_max_price" property="availableMaxPrice"/>
        <result column="available_min_price" property="availableMinPrice"/>
        <result column="promote_max_price" property="promoteMaxPrice"/>
        <result column="promote_min_price" property="promoteMinPrice"/>
        <result column="promote_available_max_price" property="promoteAvailableMaxPrice"/>
        <result column="promote_available_min_price" property="promoteAvailableMinPrice"/>
        <result column="reality_quota_min_price" property="realityQuotaMinPrice"/>
        <result column="reality_quota_max_price" property="realityQuotaMaxPrice"/>
        <result column="loan_max_price" property="loanMaxPrice"/>
        <result column="loan_min_price" property="loanMinPrice"/>
        <result column="periods" property="periods"/>
        <result column="user_level" property="userLevel"/>
        <result column="resubmit_user" property="resubmitUser"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="loan_rfm_min_score" property="loanRfmMinScore"/>
        <result column="loan_rfm_max_score" property="loanRfmMaxScore"/>
        <result column="plus_rfm_min_score" property="plusRfmMinScore"/>
        <result column="plus_rfm_max_score" property="plusRfmMaxScore"/>
        <result column="market_reach_min_score" property="marketReachMinScore"/>
        <result column="market_reach_max_score" property="marketReachMaxScore"/>
        <result column="user_last_min_number" property="userLastMinNumber"/>
        <result column="user_last_max_number" property="userLastMaxNumber"/>
        <result column="member_user" property="memberUser"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `grade`,
        `program_id`,
        `price_id`,
        `available_max_price`,
        `available_min_price`,
        `promote_max_price`,
        `promote_min_price`,
        `promote_available_max_price`,
        `promote_available_min_price`,
        `loan_max_price`,
        `loan_min_price`,
        `periods`,
        `user_level`,
        `create_user`,
        `create_user_id`,
        `create_time`,
        `reality_quota_min_price`,
        `reality_quota_max_price`,
        `resubmit_user`,
        `loan_rfm_min_score`,
        `loan_rfm_max_score`,
        `plus_rfm_min_score`,
        `plus_rfm_max_score`,
        `market_reach_min_score`,
        `market_reach_max_score`,
        `user_last_min_number`,
        `user_last_max_number`,
        `member_user`
    </sql>

    <insert id="batchInsert" parameterType="com.juzifenqi.plus.module.program.repository.po.price.PlusDifferenceProgramPricePo">
        INSERT INTO plus_difference_program_price (
        `program_id`,
        `grade`,
        `price_id`,
        `available_max_price`,
        `available_min_price`,
        `promote_max_price`,
        `promote_min_price`,
        `promote_available_max_price`,
        `promote_available_min_price`,
        `loan_max_price`,
        `loan_min_price`,
        `periods`,
        `user_level`,
        `reality_quota_min_price`,
        `reality_quota_max_price`,
        `resubmit_user`,
        `create_user`,
        `create_user_id`,
        `create_time`,
        `loan_rfm_min_score`,
        `loan_rfm_max_score`,
        `plus_rfm_min_score`,
        `plus_rfm_max_score`,
        `market_reach_min_score`,
        `market_reach_max_score`,
        `user_last_min_number`,
        `user_last_max_number`,
        `member_user`
        )
        VALUES
        <foreach collection="list" item="li" index="index" separator=",">
            (
            #{li.programId},
            #{li.grade},
            #{li.priceId},
            #{li.availableMaxPrice},
            #{li.availableMinPrice},
            #{li.promoteMaxPrice},
            #{li.promoteMinPrice},
            #{li.promoteAvailableMaxPrice},
            #{li.promoteAvailableMinPrice},
            #{li.loanMaxPrice},
            #{li.loanMinPrice},
            #{li.periods},
            #{li.userLevel},
            #{li.realityQuotaMinPrice},
            #{li.realityQuotaMaxPrice},
            #{li.resubmitUser},
            #{li.createUser},
            #{li.createUserId},
            NOW(),
            #{li.loanRfmMinScore},
            #{li.loanRfmMaxScore},
            #{li.plusRfmMinScore},
            #{li.plusRfmMaxScore},
            #{li.marketReachMinScore},
            #{li.marketReachMaxScore},
            #{li.userLastMinNumber},
            #{li.userLastMaxNumber},
            #{li.memberUser}
            )
        </foreach>
    </insert>


    <select id="selectById" parameterType="java.lang.Integer"
            resultMap="PlusDifferenceProgramPriceMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_difference_program_price
        WHERE `id` = #{id}
    </select>

    <select id="selectByPriceId" resultMap="PlusDifferenceProgramPriceMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_difference_program_price
        where price_id = #{priceId}
        order by id desc
    </select>

    <delete id="deleteByPriceId">
        delete from plus_difference_program_price where price_id = #{priceId}
    </delete>
</mapper>
