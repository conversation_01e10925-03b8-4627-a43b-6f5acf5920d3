<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusProgramPriceMapper">

    <resultMap id="PlusProgramPriceMap"
            type="com.juzifenqi.plus.module.program.repository.po.price.PlusProgramPricePo">
        <result column="id" property="id"/>
        <result column="channel_id" property="channelId"/>
        <result column="channel_name" property="channelName"/>
        <result column="config_id" property="configId"/>
        <result column="price_type" property="priceType"/>
        <result column="grade" property="grade"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_user" property="updateUser"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="update_time" property="updateTime"/>
        <result column="biz_source" property="bizSource"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `channel_id`,
        `channel_name`,
        `config_id`,
        `price_type`,
        `grade`,
        `create_user`,
        `create_user_id`,
        `create_time`,
        `update_user`,
        `update_user_id`,
        `update_time`,
        `biz_source`
    </sql>

    <insert id="insert"
            parameterType="com.juzifenqi.plus.module.program.repository.po.price.PlusProgramPricePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_price (`channel_id`,
                                        `channel_name`,
                                        `config_id`,
                                        `price_type`,
                                        `grade`,
                                        `create_user`,
                                        `create_user_id`,
                                        `create_time`,
                                        `update_user`,
                                        `update_user_id`,
                                        `biz_source`,
                                        `update_time`)
        VALUES (#{channelId},
                #{channelName},
                #{configId},
                #{priceType},
                #{grade},
                #{createUser},
                #{createUserId},
                NOW(),
                #{updateUser},
                #{updateUserId},
                #{bizSource},
                #{updateTime})
    </insert>

    <update id="update"
            parameterType="com.juzifenqi.plus.module.program.repository.po.price.PlusProgramPricePo">
        UPDATE plus_program_price
        <set>
            `grade`= #{grade},
            <if test="updateUser != null">`update_user`= #{updateUser},</if>
            <if test="updateUserId != null">`update_user_id`= #{updateUserId},</if>
            update_time = now()
        </set>
        WHERE `id` = #{id}
    </update>


    <select id="selectDiffList" parameterType="java.util.Map"
            resultType="com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_price
        where price_type = 2
        <if test="channelId != null">
            and channel_id = #{channelId}
        </if>
        <if test="configId != null">
            and config_id = #{configId}
        </if>
        order by id desc
        LIMIT #{startPage}, #{pageSize}
    </select>


    <select id="selectDefaultList" parameterType="java.util.Map"
            resultType="com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity">
        SELECT p.`id`,
        p.`channel_id`,
        p.`channel_name`,
        p.`config_id`,
        p.`price_type`,
        p.`grade`,
        p.`create_user`,
        p.`create_user_id`,
        p.`create_time`,
        p.`update_user`,
        p.`update_user_id`,
        p.`update_time`,
        p.`biz_source`,
        dp.`program_id` as defaultProgramId
        from plus_program_price p left join plus_default_program_price dp
        on p.id = dp.price_id
        where p.price_type = 1
        <if test="channelId != null">
            and p.channel_id = #{channelId}
        </if>
        <if test="configId != null">
            and p.config_id = #{configId}
        </if>
        order by p.id desc
        LIMIT #{startPage}, #{pageSize}
    </select>

    <select id="selectByChannelAndConfigId" resultMap="PlusProgramPriceMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_price where channel_id = #{channelId} and config_id = #{configId} and
        biz_source = #{bizSource} and
        price_type = #{priceType} order by
        id desc limit 1
    </select>

    <select id="selectById" resultMap="PlusProgramPriceMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_price where id = #{id}
    </select>

    <select id="countList" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_program_price
        where price_type = #{priceType}
        <if test="channelId != null">
            and channel_id = #{channelId}
        </if>
        <if test="configId != null">
            and config_id = #{configId}
        </if>
    </select>
</mapper>
