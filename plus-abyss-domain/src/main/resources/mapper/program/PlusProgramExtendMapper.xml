<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.program.repository.dao.IPlusProgramExtendMapper">

    <resultMap id="PlusProgramExtend"
            type="com.juzifenqi.plus.module.program.repository.po.PlusProgramExtendPo">
        <result column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_name" property="configName"/>
        <result column="second_config_id" property="secondConfigId"/>
        <result column="second_config_type" property="secondConfigType"/>
        <result column="second_config_name" property="secondConfigName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="update_user_id" property="updateUserId"/>
        <result column="rd_send_type" property="rdSendType"/>
        <result column="pop_up" property="popUp"/>
        <result column="two_pop_up" property="twoPopUp"/>
        <result column="rd_choose" property="rdChoose"/>
        <result column="send_node" property="sendNode"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `config_id`,
        `program_id`,
        `channel_id`,
        `config_name`,
        `second_config_id`,
        `second_config_type`,
        `second_config_name`,
        `create_time`,
        `update_time`,
        `update_user_id`,
        `rd_send_type`,
        `pop_up`,
        `two_pop_up`,
        `rd_choose`,
        send_node
    </sql>

    <insert id="savePlusProgramExtend"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProgramExtendPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_extend (
            `config_id`,
            `program_id`,
            `channel_id`,
            `config_name`,
            `second_config_id`,
            `second_config_type`,
            `second_config_name`,
            `create_time`,
            `update_time`,
            `update_user_id`,
            `rd_send_type`,
            `pop_up`,
            `two_pop_up`,
            `rd_choose`,send_node
        )
        VALUES(
                  #{configId},
                  #{programId},
                  #{channelId},
                  #{configName},
                  #{secondConfigId},
                  #{secondConfigType},
                  #{secondConfigName},
                  NOW(),
                  #{updateTime},
                  #{updateUserId},
                  #{rdSendType},
                  #{popUp},
                  #{twoPopUp},
                  #{rdChoose},#{sendNode}
              )
    </insert>


    <delete id="deletePlusProgramExtend" parameterType="java.lang.Integer">
        DELETE FROM plus_program_extend
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusProgramExtend"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProgramExtendPo">
        UPDATE plus_program_extend
        SET
        <if test="plusProgramExtend.configName != null">`config_name`=
            #{plusProgramExtend.configName},
        </if>
        <if test="plusProgramExtend.secondConfigId != null">`second_config_id`=
            #{plusProgramExtend.secondConfigId},
        </if>
        <if test="plusProgramExtend.secondConfigType != null">`second_config_type`=
            #{plusProgramExtend.secondConfigType},
        </if>
        <if test="plusProgramExtend.secondConfigName != null">`second_config_name`=
            #{plusProgramExtend.secondConfigName},
        </if>
        <if test="plusProgramExtend.updateUserId != null">`update_user_id`=
            #{plusProgramExtend.updateUserId},
        </if>
        <if test="plusProgramExtend.updateUserId != null">`update_user_id`=
            #{plusProgramExtend.updateUserId},
        </if>
        <if test="plusProgramExtend.rdSendType != null">`rd_send_type`=
            #{plusProgramExtend.rdSendType},
        </if>
        <if test="plusProgramExtend.popUp != null">`pop_up`=
            #{plusProgramExtend.popUp},
        </if>
        <if test="plusProgramExtend.twoPopUp != null">`two_pop_up`=
            #{plusProgramExtend.twoPopUp},
        </if>
        <if test="plusProgramExtend.rdChoose != null">`rd_choose`=
            #{plusProgramExtend.rdChoose},
        </if>
        <if test="plusProgramExtend.sendNode != null">
            send_node = #{plusProgramExtend.sendNode},
        </if>
        update_time = now()
        WHERE `id` = #{plusProgramExtend.id}
    </update>


    <select id="loadPlusProgramExtend" parameterType="java.lang.Integer"
            resultMap="PlusProgramExtend">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_extend
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusProgramExtend">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_extend
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_program_extend
    </select>

    <select id="getProgramExtendByPid" parameterType="java.lang.Integer"
            resultType="com.juzifenqi.plus.module.program.repository.po.PlusProgramExtendPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_program_extend
        WHERE `program_id` = #{programId}
    </select>

</mapper>
