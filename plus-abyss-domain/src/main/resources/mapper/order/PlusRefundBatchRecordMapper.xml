<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRefundBatchRecordMapper">

    <resultMap id="PlusRefundBatchRecord"
            type="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchRecordPo">
        <result column="id" property="id"/>
        <result column="batch_no" property="batchNo"/>
        <result column="opt_user_id" property="optUserId"/>
        <result column="opt_user_name" property="optUserName"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `batch_no`,
        `opt_user_id`,
        `opt_user_name`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusRefundBatchRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchRecordPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_refund_batch_record (
              `batch_no`,
              `opt_user_id`,
              `opt_user_name`,
              `remark`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{batchNo},
                #{optUserId},
                #{optUserName},
                #{remark},
                NOW(),
                NOW()
      )
    </insert>

    <select id="getPlusRefundBatchRecord"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchRecordPo">
        SELECT
        <include refid="Base_Column_List"/>
          FROM plus_refund_batch_record
         WHERE `batch_no` = #{batchNo}
    </select>
</mapper>
