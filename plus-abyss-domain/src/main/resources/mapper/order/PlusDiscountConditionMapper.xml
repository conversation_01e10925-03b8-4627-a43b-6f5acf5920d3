<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusDiscountConditionMapper">

    <resultMap id="PlusDiscountCondition"
            type="com.juzifenqi.plus.module.order.repository.po.PlusDiscountConditionPo">
        <result column="id" property="id"/>
        <result column="conf_code" property="confCode"/>
        <result column="condition_field" property="conditionField"/>
        <result column="condition_key" property="conditionKey"/>
        <result column="condition_val" property="conditionVal"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `conf_code`,
        `condition_field`,
        `condition_key`,
        `condition_val`,
        `create_time`
    </sql>

    <sql id="Base_Column_List_B">
        c.`id`,
        c.`conf_code`,
        c.`condition_field`,
        c.`condition_key`,
        c.`condition_val`,
        c.`create_time`
    </sql>

    <insert id="batchInsert"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusDiscountConditionPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_discount_condition (
        `conf_code`,
        `condition_field`,
        `condition_key`,
        `condition_val`,
        `create_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.confCode},
            #{record.conditionField},
            #{record.conditionKey},
            #{record.conditionVal},
            NOW()
            )
        </foreach>
    </insert>


    <delete id="delete">
        DELETE FROM plus_discount_condition
        WHERE conf_code = #{confCode}
    </delete>

    <select id="selectByConfCode" resultMap="PlusDiscountCondition">
        select
        <include refid="Base_Column_List"/>
        from plus_discount_condition where conf_code = #{confCode} order by id
    </select>

    <select id="selectByNotConfCode" resultMap="PlusDiscountCondition">
        select
        <include refid="Base_Column_List_B"/>
        from plus_discount_condition c left join plus_discount_conf f on c.conf_code = f.conf_code
        where f.conf_state = 1 and f.conf_type = 2 and f.config_id = #{configId}
        and f.conf_tag = #{confTag}
        <if test="list != null and list.size() > 0">
           and c.conf_code not in
            (
            <foreach collection="list" item="confCode" index="index" separator=",">
                #{confCode}
            </foreach>
            )
        </if>
        order by c.id
    </select>

</mapper>
