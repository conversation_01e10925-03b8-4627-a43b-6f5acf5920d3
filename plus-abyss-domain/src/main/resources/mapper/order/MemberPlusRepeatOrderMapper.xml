<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IMemberPlusRepeatOrderMapper">

    <resultMap id="MemberPlusRepeatOrder"
            type="com.juzifenqi.plus.module.order.repository.po.MemberPlusRepeatOrderPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="user_name" property="userName"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="order_time" property="orderTime"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="program_name" property="programName"/>
        <result column="pay_type" property="payType"/>
        <result column="opt_state" property="optState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
        <result column="biz_source" property="bizSource"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `user_name`,
        `channel_id`,
        `order_sn`,
        `order_amount`,
        `order_time`,
        `config_id`,
        `program_id`,
        `program_name`,
        `pay_type`,
        `opt_state`,
        `create_time`,
        `update_time`,
        `bi_time`,
        `biz_source`
    </sql>

    <insert id="saveRepeatOrder"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusRepeatOrderPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_repeat_order (
        `user_id`,
        `user_name`,
        `channel_id`,
        `order_sn`,
        `order_amount`,
        `order_time`,
        `config_id`,
        `program_id`,
        `pay_type`,
        `program_name`,
        `create_time`,
        `biz_source`
        )
        VALUES(
        #{userId},
        #{userName},
        #{channelId},
        #{orderSn},
        #{orderAmount},
        #{orderTime},
        #{configId},
        #{programId},
        #{payType},
        #{programName},
        NOW(),
        #{bizSource}
        )
    </insert>

    <select id="getByOrderSnAndState" parameterType="java.lang.String"
            resultMap="MemberPlusRepeatOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_repeat_order
        WHERE `order_sn` = #{orderSn} and opt_state = 0
    </select>

    <select id="getByOrderSn" parameterType="java.lang.String"
            resultMap="MemberPlusRepeatOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_repeat_order
        WHERE `order_sn` = #{orderSn} limit 1
    </select>

    <select id="pageList"
            parameterType="com.juzifenqi.plus.dto.req.admin.RepeatOrderQueryReq"
            resultMap="MemberPlusRepeatOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_repeat_order
        <include refid="whereConditions"/>
        order by id desc
        LIMIT #{queryParam.startPage}, #{queryParam.pageSize}
    </select>

    <select id="pageListCount"
            parameterType="com.juzifenqi.plus.dto.req.admin.RepeatOrderQueryReq"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_repeat_order
        <include refid="whereConditions"/>
    </select>

    <sql id="whereConditions">
        <where>
            <if test="queryParam.orderSn != null and queryParam.orderSn != ''">
                and `order_sn` = #{queryParam.orderSn}
            </if>
            <if test="queryParam.userId != null">
                and `user_id` = #{queryParam.userId}
            </if>
            <if test="queryParam.configId != null">
                and `config_id` = #{queryParam.configId}
            </if>
            <if test="queryParam.programId != null">
                and `program_id` = #{queryParam.programId}
            </if>
            <if test="queryParam.channelId != null">
                and `channel_id` = #{queryParam.channelId}
            </if>
            <if test="queryParam.optState != null">
                and `opt_state` = #{queryParam.optState}
            </if>
            <if test="queryParam.startTime != null">
                and `order_time` &gt; #{queryParam.startTime}
            </if>
            <if test="queryParam.endTime != null">
                and `order_time` &lt; #{queryParam.endTime}
            </if>
        </where>
    </sql>

    <update id="setInfoHandledById"
            parameterType="java.lang.Integer">
        UPDATE member_plus_repeat_order
        SET
        `opt_state`= 1,
        update_time = now()
        WHERE `id` = #{id}
    </update>
</mapper>
