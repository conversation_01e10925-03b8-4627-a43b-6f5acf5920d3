<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderInfoMapper">
    <resultMap id="PlusOrderInfo"
            type="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_type" property="orderType"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="program_name" property="programName"/>
        <result column="order_state" property="orderState"/>
        <result column="cancel_type" property="cancelType"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="program_price" property="programPrice"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="call_time" property="callTime"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_type" property="payType"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="remark" property="remark"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="biz_source" property="bizSource"/>
        <result column="first_pay_amount" property="firstPayAmount"/>
    </resultMap>

    <resultMap id="PlusOrderEntity"
            type="com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity">
        <result column="id" property="orderId"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_type" property="orderType"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="program_name" property="programName"/>
        <result column="order_state" property="orderState"/>
        <result column="cancel_type" property="cancelType"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="program_price" property="programPrice"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="pay_time" property="payTime"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="pay_type" property="payType"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="remark" property="remark"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="order_flag" property="orderFlag"/>
        <result column="jx_status" property="plusState"/>
        <result column="biz_source" property="bizSource"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `order_type`,
        `config_id`,
        `program_id`,
        `program_name`,
        `order_state`,
        `cancel_type`,
        `cancel_reason`,
        `order_amount`,
        `program_price`,
        `discount_rate`,
        `call_time`,
        `pay_time`,
        `pay_amount`,
        `pay_type`,
        `first_pay_amount`,
        `refund_amount`,
        `remark`,
        `start_time`,
        `end_time`,
        `create_time`,
        `update_time`,
        `biz_source`
    </sql>

    <sql id="Base_Column_List_B">
        o.`id`,
        o.`user_id`,
        o.`channel_id`,
        o.`order_sn`,
        o.`order_type`,
        o.`config_id`,
        o.`program_id`,
        o.`program_name`,
        o.`order_state`,
        o.`cancel_type`,
        o.`cancel_reason`,
        o.`order_amount`,
        o.`program_price`,
        o.`discount_rate`,
        o.`call_time`,
        o.`pay_time`,
        o.`pay_amount`,
        o.`pay_type`,
        o.`first_pay_amount`,
        o.`refund_amount`,
        o.`remark`,
        o.`start_time`,
        o.`end_time`,
        o.`create_time`,
        o.`update_time`,
        o.`biz_source`,
        i.`jx_status`,
        ext.`order_flag`
    </sql>

    <sql id="Base_Column_List_Ext">
        o.`id`,
        o.`user_id`,
        o.`channel_id`,
        o.`order_sn`,
        o.`order_type`,
        o.`config_id`,
        o.`program_id`,
        o.`program_name`,
        o.`order_state`,
        o.`cancel_type`,
        o.`cancel_reason`,
        o.`order_amount`,
        o.`program_price`,
        o.`discount_rate`,
        o.`call_time`,
        o.`pay_time`,
        o.`pay_amount`,
        o.`pay_type`,
        o.`first_pay_amount`,
        o.`refund_amount`,
        o.`remark`,
        o.`start_time`,
        o.`end_time`,
        o.`create_time`,
        o.`update_time`,
        o.`biz_source`,
        ext.`order_flag`
    </sql>

    <insert id="insertPlusOrderInfo"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        INSERT INTO plus_order_info (`user_id`,
                                     `channel_id`,
                                     `order_sn`,
                                     `order_type`,
                                     `config_id`,
                                     `program_id`,
                                     `program_name`,
                                     `order_state`,
                                     `cancel_type`,
                                     `cancel_reason`,
                                     `order_amount`,
                                     `program_price`,
                                     `discount_rate`,
                                     `call_time`,
                                     `pay_time`,
                                     `pay_amount`,
                                     `pay_type`,
                                     `first_pay_amount`,
                                     `refund_amount`,
                                     `remark`,
                                     `start_time`,
                                     `end_time`,
                                     `create_time`,
                                     `update_time`,
                                      `biz_source`)
        VALUES (#{plusOrderInfo.userId},
                #{plusOrderInfo.channelId},
                #{plusOrderInfo.orderSn},
                #{plusOrderInfo.orderType},
                #{plusOrderInfo.configId},
                #{plusOrderInfo.programId},
                #{plusOrderInfo.programName},
                #{plusOrderInfo.orderState},
                #{plusOrderInfo.cancelType},
                #{plusOrderInfo.cancelReason},
                #{plusOrderInfo.orderAmount},
                #{plusOrderInfo.programPrice},
                #{plusOrderInfo.discountRate},
                #{plusOrderInfo.callTime},
                #{plusOrderInfo.payTime},
                #{plusOrderInfo.payAmount},
                #{plusOrderInfo.payType},
                #{plusOrderInfo.firstPayAmount},
                #{plusOrderInfo.refundAmount},
                #{plusOrderInfo.remark},
                #{plusOrderInfo.startTime},
                #{plusOrderInfo.endTime},
                NOW(),
                #{plusOrderInfo.updateTime},
                #{plusOrderInfo.bizSource})
    </insert>

    <delete id="deletePlusOrderInfo" parameterType="java.lang.Integer">
        DELETE
        FROM plus_order_info
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusOrderInfo"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        UPDATE plus_order_info
        SET
        <if test="plusOrderInfo.orderType != null">
            `order_type`= #{plusOrderInfo.orderType},
        </if>
        <if test="plusOrderInfo.orderState != null">
            `order_state`= #{plusOrderInfo.orderState},
        </if>
        <if test="plusOrderInfo.cancelType != null">
            `cancel_type`= #{plusOrderInfo.cancelType},
        </if>
        <if test="plusOrderInfo.cancelReason != null">
            `cancel_reason`= #{plusOrderInfo.cancelReason},
        </if>
        <if test="plusOrderInfo.callTime != null">
            `call_time`= #{plusOrderInfo.callTime},
        </if>
        <if test="plusOrderInfo.payTime != null">
            `pay_time`= #{plusOrderInfo.payTime},
        </if>
        <if test="plusOrderInfo.payAmount != null">
            `pay_amount`= #{plusOrderInfo.payAmount},
        </if>
        <if test="plusOrderInfo.payType != null">
            `pay_type`= #{plusOrderInfo.payType},
        </if>
        <if test="plusOrderInfo.refundAmount != null">
            `refund_amount`=#{plusOrderInfo.refundAmount},
        </if>
        <if test="plusOrderInfo.remark != null">
            `remark`= #{plusOrderInfo.remark},
        </if>
        <if test="plusOrderInfo.startTime != null">
            `start_time`= #{plusOrderInfo.startTime},
        </if>
        <if test="plusOrderInfo.endTime != null">
            `end_time`= #{plusOrderInfo.endTime},
        </if>
        update_time = now()
        WHERE `order_sn` = #{plusOrderInfo.orderSn}
    </update>

    <update id="updatePlusOrderInfoStatus"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        UPDATE plus_order_info
        SET
        <if test="plusOrderInfo.orderState != null">
            `order_state`= #{plusOrderInfo.orderState},
        </if>
        update_time = now()
        WHERE `id` = #{plusOrderInfo.id}
    </update>

    <select id="loadPlusOrderInfo" parameterType="java.lang.Integer" resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        WHERE `id` = #{id}
    </select>

    <sql id="getPageCondition">
        <where>
            <if test="plusOrderInfo.userId != null">
                and o.`user_id`= #{plusOrderInfo.userId}
            </if>
            <if test="plusOrderInfo.channelId != null">
                and o.`channel_id`= #{plusOrderInfo.channelId}
            </if>
            <if test="plusOrderInfo.orderSn != null">
                and o.`order_sn`= #{plusOrderInfo.orderSn}
            </if>
            <if test="plusOrderInfo.orderType != null">
                and o.`order_type`= #{plusOrderInfo.orderType}
            </if>
            <if test="plusOrderInfo.configId != null">
                and o.`config_id`= #{plusOrderInfo.configId}
            </if>
            <if test="plusOrderInfo.programId != null">
                and o.`program_id`= #{plusOrderInfo.programId}
            </if>
            <if test="plusOrderInfo.programName != null">
                and o.`program_name`=
                #{plusOrderInfo.programName}
            </if>
            <if test="plusOrderInfo.orderState != null">
                and o.`order_state`=
                #{plusOrderInfo.orderState}
            </if>
            <if test="plusOrderInfo.startCreateTime != null">
                and o.`create_time` <![CDATA[ >= ]]>  #{plusOrderInfo.startCreateTime}
            </if>

            <if test="plusOrderInfo.endCreateTime != null ">
                and o.`create_time` <![CDATA[ <= ]]> #{plusOrderInfo.endCreateTime}
            </if>

            <if test="plusOrderInfo.payType != null">
                and o.`pay_type`=
                #{plusOrderInfo.payType}
            </if>
        </where>
    </sql>


    <sql id="getPageCondition2">
        <where>
            <if test="plusOrderInfo.userId != null">
                and o.`user_id`= #{plusOrderInfo.userId}
            </if>
            <if test="plusOrderInfo.channelId != null">
                and o.`channel_id`= #{plusOrderInfo.channelId}
            </if>
            <if test="plusOrderInfo.orderSn != null">
                and o.`order_sn`= #{plusOrderInfo.orderSn}
            </if>
            <if test="plusOrderInfo.orderType != null">
                and o.`order_type`= #{plusOrderInfo.orderType}
            </if>
            <if test="plusOrderInfo.configId != null">
                and o.`config_id`= #{plusOrderInfo.configId}
            </if>
            <if test="plusOrderInfo.programId != null">
                and o.`program_id`= #{plusOrderInfo.programId}
            </if>
            <if test="plusOrderInfo.programName != null">
                and o.`program_name`=
                #{plusOrderInfo.programName}
            </if>
            <if test="plusOrderInfo.orderState != null">
                and o.`order_state`=
                #{plusOrderInfo.orderState}
            </if>
            <if test="plusOrderInfo.startCreateTime != null">
                and o.`create_time` <![CDATA[ >= ]]>  #{plusOrderInfo.startCreateTime}
            </if>

            <if test="plusOrderInfo.endCreateTime != null ">
                and o.`create_time` <![CDATA[ <= ]]> #{plusOrderInfo.endCreateTime}
            </if>

            <if test="plusOrderInfo.payType != null">
                and o.`pay_type`=
                #{plusOrderInfo.payType}
            </if>
            <!-- 换判断和字段 -->
            <if test="plusOrderInfo.plusState != null">
                and i.jx_status = #{plusOrderInfo.plusState}
            </if>
        </where>
    </sql>

    <select id="getPageList"
            parameterType="com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq"
            resultMap="PlusOrderEntity">
        SELECT
        <include refid="Base_Column_List_Ext"/>
        FROM plus_order_info o
        left join plus_order_ext_info ext on o.order_sn = ext.order_sn
        <include refid="getPageCondition"/>
        order by o.create_time desc
        LIMIT #{pageStart}, #{pageSize}
    </select>

    <select id="pageListByState"
            parameterType="com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq"
            resultMap="PlusOrderEntity">
        SELECT
        <include refid="Base_Column_List_B"/>
        FROM plus_order_info o
        left join plus_order_ext_info ext on o.order_sn = ext.order_sn
        <!-- 状态正常 -->
        <if test="plusOrderInfo.plusState == 1">
            left join member_plus_info_detail i on o.order_sn = i.order_sn
        </if>
        <!-- 状态过期 -->
        <if test="plusOrderInfo.plusState == 2 or plusOrderInfo.plusState == 0">
            left join expire_member_plus_info_detail i on o.order_sn = i.order_sn
        </if>
        <include refid="getPageCondition2"/>
        order by o.create_time desc
        LIMIT #{pageStart}, #{pageSize}
    </select>

    <select id="pageListCountByState"
            parameterType="com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_order_info o
        <!-- 状态正常 -->
        <if test="plusOrderInfo.plusState != null and plusOrderInfo.plusState == 1">
            left join member_plus_info_detail i on o.order_sn = i.order_sn
        </if>
        <!-- 状态过期 -->
        <if test="plusOrderInfo.plusState == 2 or plusOrderInfo.plusState == 0">
            left join expire_member_plus_info_detail i on o.order_sn = i.order_sn
        </if>
        <include refid="getPageCondition2"/>
    </select>

    <select id="pageListCount"
            parameterType="com.juzifenqi.plus.dto.req.admin.PlusOrderInfoQueryReq"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_order_info o
        <include refid="getPageCondition"/>
    </select>

    <select id="getByOrderSn" parameterType="java.lang.String" resultMap="PlusOrderInfo">
        SELECT *
        FROM plus_order_info
        where order_sn = #{orderSn}
    </select>

    <select id="getOrderByOrderSns"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        <where>
            <foreach collection="orderSns" item="orderSn" open=" order_sn  in  (" close=")"
                    separator=",">
                #{orderSn}
            </foreach>
        </where>
    </select>

    <select id="getPlusOrderInfoList"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo"
            resultMap="PlusOrderInfo">
        SELECT *
        FROM plus_order_info
        <include refid="getCondition"/>
    </select>

    <select id="getPlusOrderList"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo"
            resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        <where>
            <if test="plusOrderInfo.userId != null">
                and `user_id`= #{plusOrderInfo.userId}
            </if>
            <if test="plusOrderInfo.channelId != null">
                and`channel_id`= #{plusOrderInfo.channelId}
            </if>
            <if test="plusOrderInfo.orderState != null">
                and `order_state`=
                #{plusOrderInfo.orderState}
            </if>
            <if test="plusOrderInfo.payType != null">
                and `pay_type`= #{plusOrderInfo.payType}
            </if>
            <if test="plusOrderInfo.configId != null">
                and `config_id`= #{plusOrderInfo.configId}
            </if>
            <if test="plusOrderInfo.programId != null">
                and `program_id`= #{plusOrderInfo.programId}
            </if>
        </where>
    </select>

    <select id="getConfigIdByUserId"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `plus_order_info` where user_id=#{userId}
    </select>

    <select id="getMemberPlusOrderInfoList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo"
            resultMap="PlusOrderInfo">
        SELECT *
        FROM plus_order_info
        <include refid="getCondition"/>
        order by create_time desc,order_state
    </select>

    <sql id="getCondition">
        <where>
            <if test="plusOrderInfo.userId != null">
                and `user_id`= #{plusOrderInfo.userId}
            </if>
            <if test="plusOrderInfo.channelId != null">
                and`channel_id`= #{plusOrderInfo.channelId}
            </if>
            <if test="plusOrderInfo.orderSn != null">
                and `order_sn`= #{plusOrderInfo.orderSn}
            </if>
            <if test="plusOrderInfo.orderType != null">
                and `order_type`= #{plusOrderInfo.orderType}
            </if>
            <if test="plusOrderInfo.configId != null">
                and `config_id`= #{plusOrderInfo.configId}
            </if>
            <if test="plusOrderInfo.configId == null">
                and `config_id`!= 10
            </if>
            <if test="plusOrderInfo.programId != null">
                and `program_id`= #{plusOrderInfo.programId}
            </if>
            <if test="plusOrderInfo.programName != null">
                and `program_name`=
                #{plusOrderInfo.programName}
            </if>
            <if test="plusOrderInfo.orderState != null">
                and `order_state`=
                #{plusOrderInfo.orderState}
            </if>
            <if test="plusOrderInfo.orderAmount != null">
                and `order_amount`=
                #{plusOrderInfo.orderAmount}
            </if>
            <if test="plusOrderInfo.callTime != null">
                and `call_time`= #{plusOrderInfo.callTime}
            </if>
            <if test="plusOrderInfo.payTime != null">
                and `pay_time`= #{plusOrderInfo.payTime}
            </if>
            <if test="plusOrderInfo.payAmount != null">
                and`pay_amount`= #{plusOrderInfo.payAmount}
            </if>
            <if test="plusOrderInfo.payType != null">
                and `pay_type`= #{plusOrderInfo.payType}
            </if>
            <if test="plusOrderInfo.refundAmount != null">
                and`refund_amount`=
                #{plusOrderInfo.refundAmount}
            </if>
        </where>
    </sql>

    <update id="batchUpdateOrderStatus">
        update plus_order_info
        set `order_state`= 3 ,`cancel_type`=4, update_time = now()
        where `order_state`= 1 and `pay_type`= 3
        and `order_sn` in
        <foreach collection="orderSns" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
    </update>

    <update id="updatePlusOrderInfoByOrderSn">
        UPDATE plus_order_info
        SET
        <if test="plusOrderInfo.orderType != null">
            `order_type`= #{plusOrderInfo.orderType},
        </if>
        <if test="plusOrderInfo.orderState != null">
            `order_state`= #{plusOrderInfo.orderState},
        </if>
        <if test="plusOrderInfo.cancelType != null">
            `cancel_type`= #{plusOrderInfo.cancelType},
        </if>
        <if test="plusOrderInfo.cancelReason != null">
            `cancel_reason`=
            #{plusOrderInfo.cancelReason},
        </if>
        <if test="plusOrderInfo.callTime != null">
            `call_time`= #{plusOrderInfo.callTime},
        </if>
        <if test="plusOrderInfo.payTime != null">
            `pay_time`= #{plusOrderInfo.payTime},
        </if>
        <if test="plusOrderInfo.payAmount != null">
            `pay_amount`= #{plusOrderInfo.payAmount},
        </if>
        <if test="plusOrderInfo.refundAmount != null">
            `refund_amount`=
            #{plusOrderInfo.refundAmount},
        </if>
        <if test="plusOrderInfo.payType != null">
            `pay_type`= #{plusOrderInfo.payType},
        </if>
        <if test="plusOrderInfo.startTime != null">
            `start_time`= #{plusOrderInfo.startTime},
        </if>
        <if test="plusOrderInfo.endTime != null">
            `end_time`= #{plusOrderInfo.endTime},
        </if>
        update_time = now()
        WHERE `order_sn` = #{plusOrderInfo.orderSn}
    </update>

    <select id="getAfterPayOrderInfo" resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        where user_id = #{userId} and config_id = #{configId} and order_state = 1 and pay_type = 3
        and end_time &gt;= now() order by id desc limit 1
    </select>

    <select id="getAfterPayOrderInfoAfterAuthTime" resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        where user_id = #{userId} and config_id = #{configId} and order_state = 1 and pay_type = 3
        and end_time &gt;= now() and create_time > #{authTime} order by id desc limit 1
    </select>

    <select id="getAfterPayOrderInfoList" resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        where user_id = #{userId} and config_id in
        <foreach collection="configIds" index="index" item="configId" open="(" separator=","
                close=")">
            #{configId}
        </foreach>
        and order_state = 1 and pay_type = 3
        and end_time &gt;= now() order by id desc limit 1
    </select>

    <select id="getWaitPayOrderList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        where user_id = #{userId} and config_id in
        <foreach collection="configIds" index="index" item="configId" open="(" separator=","
                close=")">
            #{configId}
        </foreach>
        and order_state = 1
    </select>


    <select id="getSevenPlusOrder"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        select poi.*
        from member_plus_info_detail mpid
        left join plus_order_info poi on mpid.order_sn = poi.order_sn
        where
        mpid.user_id = #{userId}
        and mpid.channel_id = #{channel}
        and mpid.jx_status = 1
        and DATE_SUB(CURDATE(), INTERVAL 7 DAY) &lt; mpid.create_time
        and mpid.config_id in
        <foreach collection="ids" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
    </select>

    <select id="getPlusOrderByAuthTime"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        select poi.*
        from member_plus_info_detail mpid
        left join plus_order_info poi on mpid.order_sn = poi.order_sn
        where
        mpid.user_id = #{userId}
        and mpid.channel_id = #{channel}
        and mpid.jx_status = 1
        and mpid.create_time > #{authTime}
        and mpid.config_id in
        <foreach collection="ids" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
    </select>


    <select id="getAllSevenPlusOrder"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        select poi.*
        from member_plus_info_detail mpid
                 left join plus_order_info poi on mpid.order_sn = poi.order_sn
        where mpid.user_id = #{userId}
          and mpid.channel_id = #{channel}
          and mpid.jx_status = 1
          and DATE_SUB(now(), INTERVAL 7 DAY) &lt; mpid.create_time
        order by mpid.create_time desc
    </select>

    <select id="getPlusOrderInfoListByAuthTime"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        select poi.*
        from member_plus_info_detail mpid
                 left join plus_order_info poi on mpid.order_sn = poi.order_sn
        where mpid.user_id = #{userId}
          and mpid.channel_id = #{channel}
          and mpid.jx_status = 1
          and mpid.create_time > #{authTime}
        order by mpid.create_time desc
    </select>

    <select id="getInfoByOrderSn" resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        WHERE `order_sn` = #{orderSn}
        and user_id = #{userId}
        limit 1
    </select>

    <select id="getPayRecordByProgram" resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_info
        WHERE program_id = #{programId}
        AND order_state = 2
        ORDER BY create_time DESC
        LIMIT 40 ;
    </select>

    <select id="checkUserBuy" resultMap="PlusOrderInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_order_info
        WHERE user_id = #{userId}
        AND config_id = #{configId}
        AND channel_id = #{channel}
        AND order_state in (2,3)
        LIMIT 1
    </select>

    <select id="checkRepeatOrder" resultMap="PlusOrderInfo">
        SELECT order_sn
        FROM plus_order_info
        WHERE user_id = #{userId}
          AND `program_id` = #{programId}
          AND order_state = 1
          AND create_time &gt;= (NOW() - INTERVAL 5 HOUR) LIMIT 1
    </select>

    <select id="getNumByUserAndProId" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_order_info
        where user_id = #{memberId}
          and program_id = #{programId}
          and order_state in (2, 3)
    </select>

    <update id="updateBatchPeriods"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo">
        <foreach collection="list" item="item" separator=";">
            update
            `plus_order_info`
            set
            `start_time` = #{item.jxStartTime},
            `end_time` = #{item.jxEndTime},
            `update_time` = now()
            where
            `order_sn` = #{item.orderSn}
        </foreach>
    </update>

    <select id="countByOrderListAndState" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_order_info
        where
        order_sn in
        <foreach collection="orderList" item="orderSn" index="index" open="(" separator=","
                close=")">
            #{orderSn}
        </foreach>
        and order_state = #{orderState}
    </select>

    <select id="sumPayAmountByOrderListAndState" resultType="java.math.BigDecimal">
        SELECT sum(order_amount) as pay_amount
        FROM plus_order_info
        where
        order_sn in
        <foreach collection="orderList" item="orderSn" index="index" open="(" separator=","
                close=")">
            #{orderSn}
        </foreach>
        and order_state = #{orderState}
    </select>

    <select id="getWaitAfterPayList" resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        SELECT <include refid="Base_Column_List"/>
        FROM plus_order_info
        where order_sn in
        <foreach collection="orders" item="orderSn" index="index" open="(" separator=","
                close=")">
            #{orderSn}
        </foreach>
        and pay_type =3 and order_state = 1
    </select>

    <select id="getOrderList" resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderInfoPo">
        SELECT <include refid="Base_Column_List"/>
        FROM plus_order_info
        where order_sn in
        <foreach collection="orders" item="orderSn" index="index" open="(" separator=","
                 close=")">
            #{orderSn}
        </foreach>
    </select>

    <select id="getPlusOrderListByIdLimit"
            resultMap="PlusOrderEntity">
        SELECT <include refid="Base_Column_List"/>
        FROM plus_order_info
        WHERE id &gt; #{startId}
        AND id &lt; #{maxId}
        AND config_id = 10
        AND order_state IN (1,2)
        ORDER BY id
        LIMIT #{batchSize}
    </select>

    <select id="getPlusOrderByIds" resultMap="PlusOrderEntity">
        SELECT <include refid="Base_Column_List"/>
        FROM plus_order_info
        WHERE
        id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        AND config_id = 10
        AND order_state IN (1,2)
    </select>

    <select id="getPlusOutOrder"
            parameterType="com.juzifenqi.plus.dto.req.order.PlusOutOrderQueryReq"
            resultMap="PlusOrderEntity">
        SELECT
        <include refid="Base_Column_List_Ext"/>
        FROM plus_order_info o
        left join plus_order_ext_info ext on o.order_sn = ext.order_sn
        where o.user_id = #{outOrderReq.userId} and o.channel_id = #{outOrderReq.channelId}
        <if test="outOrderReq.orderSn != null">
            and o.order_sn = #{outOrderReq.orderSn}
        </if>
        <if test="outOrderReq.outOrderSn != null">
            and ext.out_order_sn = #{outOrderReq.outOrderSn}
        </if>
        limit 1
    </select>
</mapper>
