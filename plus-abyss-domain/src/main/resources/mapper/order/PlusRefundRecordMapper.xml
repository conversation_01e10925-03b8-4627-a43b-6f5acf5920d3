<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRefundRecordMapper">

    <resultMap id="PlusRefundRecord" type="com.juzifenqi.plus.module.order.repository.po.PlusRefundRecordPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="config_id" property="configId"/>
        <result column="order_status" property="orderStatus"/>
        <result column="opt_status" property="optStatus"/>
        <result column="remark" property="remark"/>
        <result column="num" property="num"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `plus_order_sn`,
        `config_id`,
        `order_status`,
        `opt_status`,
        `remark`,
        `num`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusRefundRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundRecordPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_refund_record (
              `user_id`,
              `channel_id`,
              `order_sn`,
              `plus_order_sn`,
              `config_id`,
              `order_status`,
              `opt_status`,
              <if test="remark != null">`remark`,</if>
              <if test="num != null">`num`,</if>
              `create_time`,
              `update_time`
      )
      VALUES(
                #{userId},
                #{channelId},
                #{orderSn},
                #{plusOrderSn},
                #{configId},
                #{orderStatus},
                #{optStatus},
                <if test="remark != null">#{remark},</if>
                <if test="num != null">#{num},</if>
                NOW(),
                NOW()
      )
    </insert>

    <update id="updatePlusRefundRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundRecordPo">
        UPDATE plus_refund_record
        SET
        <if test="plusRefundRecord.orderSn!= null">`order_sn`= #{plusRefundRecord.orderSn},</if>
        <if test="plusRefundRecord.orderStatus != null">`order_status`= #{plusRefundRecord.orderStatus},</if>
        <if test="plusRefundRecord.optStatus != null">`opt_status`= #{plusRefundRecord.optStatus},</if>
        <if test="plusRefundRecord.remark != null">`remark`= #{plusRefundRecord.remark},</if>
        <if test="plusRefundRecord.num != null">`num`= #{plusRefundRecord.num},</if>
        update_time = now()
        WHERE `id` = #{plusRefundRecord.id}
    </update>


    <select id="loadPlusRefundRecord" parameterType="java.lang.Integer"
            resultMap="PlusRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_record
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_record
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_refund_record
    </select>


    <select id="getPlusRefundList" resultMap="PlusRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_record
        where `opt_status` = #{optState} and `num` &lt; 4
        order by id
        LIMIT #{size}
    </select>

    <select id="loadByPlusOrderSn" parameterType="java.lang.String"
            resultMap="PlusRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_record
        WHERE `plus_order_sn` = #{plusOrderSn}
    </select>

    <update id="batchUpdateState">
        UPDATE plus_refund_record
        SET `opt_status` = #{optState},
        `remark` = #{stateValue},
        update_time  = now()
        WHERE `id` in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>
</mapper>
