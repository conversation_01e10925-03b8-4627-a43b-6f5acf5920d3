<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRefundBatchDetailMapper">

    <resultMap id="PlusRefundBatchDetail"
            type="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchDetailPo">
        <result column="id" property="id"/>
        <result column="batch_no" property="batchNo"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="program_id" property="programId"/>
        <result column="config_id" property="configId"/>
        <result column="opt_status" property="optStatus"/>
        <result column="fail_reason" property="failReason"/>
        <result column="remark" property="remark"/>
        <result column="handle_time" property="handleTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `batch_no`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `program_id`,
        `config_id`,
        `opt_status`,
        `fail_reason`,
        `remark`,
        `handle_time`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="getPageCondition">
        <where>
            <if test="queryParam.batchNo != null and queryParam.batchNo !=''">
                and `batch_no`= #{queryParam.batchNo}
            </if>
            <if test="queryParam.orderSn != null and queryParam.orderSn !=''">
                and `order_sn`= #{queryParam.orderSn}
            </if>
            <if test="queryParam.configId != null">and `config_id`=
                #{queryParam.configId}
            </if>
            <if test="queryParam.programId != null">and `program_id`=
                #{queryParam.programId}
            </if>
            <if test="queryParam.optStatus != null">and `opt_status`=
                #{queryParam.optStatus}
            </if>
        </where>
    </sql>

    <insert id="savePlusRefundBatchDetail"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchDetailPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_refund_batch_detail (
        `batch_no`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `program_id`,
        `config_id`,
        `opt_status`,
        `fail_reason`,
        `remark`,
        `handle_time`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="batchDetailList" separator=",">
            (
            #{batchDetailList.batchNo},
            #{batchDetailList.userId},
            #{batchDetailList.channelId},
            #{batchDetailList.orderSn},
            #{batchDetailList.programId},
            #{batchDetailList.configId},
            #{batchDetailList.optStatus},
            #{batchDetailList.failReason},
            #{batchDetailList.remark},
            #{batchDetailList.handleTime},
            NOW(),
            #{batchDetailList.updateTime}
            )
        </foreach>
    </insert>


    <update id="updatePlusRefundBatchDetail"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchDetailPo">
        UPDATE plus_refund_batch_detail
        SET
        <if test="plusRefundBatchDetail.optStatus != null">`opt_status`=
            #{plusRefundBatchDetail.optStatus},
        </if>
        <if test="plusRefundBatchDetail.failReason != null">`fail_reason`=
            #{plusRefundBatchDetail.failReason},
        </if>
        <if test="plusRefundBatchDetail.remark != null">`remark`= #{plusRefundBatchDetail.remark},
        </if>
        <if test="plusRefundBatchDetail.handleTime != null">`handle_time`=
            #{plusRefundBatchDetail.handleTime},
        </if>
        update_time = now()
        WHERE `id` = #{plusRefundBatchDetail.id}
    </update>

    <select id="loadPlusRefundBatchDetail" parameterType="java.lang.Integer"
            resultMap="PlusRefundBatchDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_batch_detail
        WHERE `id` = #{id}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_refund_batch_detail
        <include refid="getPageCondition"/>
    </select>

    <select id="getPlusBatchDetailByQuery"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchDetailPo"
            resultMap="PlusRefundBatchDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_batch_detail
        <include refid="getPageCondition"/>
        order by create_time desc
        LIMIT #{pageStart}, #{pagesize}
    </select>

    <select id="getPlusRefundBatchList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRefundBatchDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_batch_detail
        where `opt_status` = 0
        order by create_time asc
        LIMIT 0,20
    </select>

</mapper>
