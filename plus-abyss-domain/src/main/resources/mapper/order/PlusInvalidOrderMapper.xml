<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusInvalidOrderMapper">

    <resultMap id="PlusInvalidOrder"
            type="com.juzifenqi.plus.module.order.repository.po.PlusInvalidOrderPo">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="state" property="state"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `create_time`,
        `state`,
        `update_time`
    </sql>

    <insert id="saveInvalidOrder"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusInvalidOrderPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_invalid_order (
        `order_sn`,
        `create_time`,
        `state`,
        `update_time`
      )
      VALUES(
                #{plusInvalidOrder.orderSn},
                  NOW(),
                #{plusInvalidOrder.state},
                NOW()
      )
    </insert>

    <update id="updateState">
        UPDATE plus_invalid_order
        SET
        `state` = 2,
        update_time = now()
        where `order_sn` in (
        <foreach collection="orderSns" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </update>

    <select id="selectInvalidOrderList" resultType="java.lang.String">
        SELECT order_sn
        FROM plus_invalid_order where state = 1;
    </select>

</mapper>
