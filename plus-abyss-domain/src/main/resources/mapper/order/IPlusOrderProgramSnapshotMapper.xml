<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderProgramSnapshotMapper">

    <resultMap id="plusOrderProgramSnapshotPo"
            type="com.juzifenqi.plus.module.order.repository.po.PlusOrderProgramSnapshotPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="send_node" property="sendNode"/>
        <result column="order_sn" property="orderSn"/>
        <result column="config_id" property="configId"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,
        order_sn,
        config_id,
        program_id,
        create_time,
        update_time,
        send_node
    </sql>

    <insert id="insertProgramSnapshot" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_porgram_snapshot (
            program_id,
            config_id,
            create_time,
            update_time,
            send_node,
            order_sn
        )VALUES (
            #{programId},
            #{configId},
            NOW(),
            NOW(),
            #{sendNode},
            #{orderSn}
        )
    </insert>

    <insert id="batchInsertProgramSnapshot">
        INSERT INTO plus_order_porgram_snapshot (
            program_id,
            config_id,
            create_time,
            update_time,
            send_node,
            order_sn
        )VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.configId},
            NOW(),
            NOW(),
            #{record.sendNode},
            #{record.orderSn}
            )
        </foreach>
    </insert>

    <select id="getProgramSnapshotByOrderSn" resultMap="plusOrderProgramSnapshotPo">
        SELECT
        <include refid="Base_Column_List"/>
        from plus_order_porgram_snapshot
        where order_sn = #{orderSn}
    </select>

</mapper>
