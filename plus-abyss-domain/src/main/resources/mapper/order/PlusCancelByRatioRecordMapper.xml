<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusCancelByRatioRecordMapper">

    <resultMap id="CancelByRatioRecord" type="com.juzifenqi.plus.module.order.repository.po.PlusCancelByRatioRecordPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="refund_ratio" property="refundRatio"/>
        <result column="refund_amount" property="refundAmount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `channel_id`,
        `order_sn`,
        `order_amount`,
        `refund_ratio`,
        `refund_amount`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusCancelByRatioRecordPo">
        INSERT INTO plus_cancel_by_ratio_record (
        `member_id`,
        `channel_id`,
        `order_sn`,
        `order_amount`,
        `refund_ratio`,
        `refund_amount`,
        `create_time`,
        `update_time`
        )
        VALUES(
            #{memberId},
            #{channelId},
            #{orderSn},
            #{orderAmount},
            #{refundRatio},
            #{refundAmount},
            NOW(),
            NOW()
        )
    </insert>



    <select id="selectByOrderSn" parameterType="java.lang.String" resultMap="CancelByRatioRecord">
        SELECT *
        FROM plus_cancel_by_ratio_record
        where order_sn = #{orderSn}
    </select>

</mapper>
