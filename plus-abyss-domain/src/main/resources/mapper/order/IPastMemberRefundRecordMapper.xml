<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPastMemberRefundRecordMapper">

    <resultMap id="PastMemberRefundRecord"
            type="com.juzifenqi.plus.module.order.repository.po.PastMemberRefundRecordPo">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="defray_serial_no" property="defraySerialNo"/>
        <result column="opt_status" property="optStatus"/>
        <result column="opt_result" property="optResult"/>
        <result column="verify_status" property="verifyStatus"/>
        <result column="verify_result" property="verifyResult"/>
        <result column="retry_count" property="retryCount"/>
        <result column="refund_ratio" property="refundRatio"/>
        <result column="card_id" property="cardId"/>
        <result column="card_no" property="cardNo"/>
        <result column="card_no_uuid" property="cardNoUuid"/>
        <result column="customer_name" property="customerName"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="pay_amount" property="payAmount"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="operating_id" property="operatingId"/>
        <result column="operating_name" property="operatingName"/>
        <result column="remark" property="remark"/>
        <result column="business_type" property="businessType"/>
        <result column="pay_fail_msg" property="payFailMsg"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `defray_serial_no`,
        `opt_status`,
        `opt_result`,
        `verify_status`,
        `verify_result`,
        `retry_count`,
        `refund_ratio`,
        `card_id`,
        `card_no`,
        `card_no_uuid`,
        `customer_name`,
        `serial_number`,
        `pay_amount`,
        `cancel_reason`,
        `operating_id`,
        `operating_name`,
        `remark`,
        `create_time`,
        `update_time`,
        `pay_fail_msg`,
        `business_type`
    </sql>

    <insert id="savePastMemberRefundRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PastMemberRefundRecordPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO past_member_refund_record (`order_sn`,
                                               `defray_serial_no`,
                                               `opt_result`,
                                               `verify_result`,
                                               `refund_ratio`,
                                               `card_id`,
                                               `card_no`,
                                               `card_no_uuid`,
                                               `customer_name`,
                                               `serial_number`,
                                               `pay_amount`,
                                               `cancel_reason`,
                                               `operating_id`,
                                               `operating_name`,
                                               `remark`,
                                               `create_time`,
                                               `update_time`,
                                               `pay_fail_msg`,
                                               `business_type`)
        VALUES (#{orderSn},
                #{defraySerialNo},
                #{optResult},
                #{verifyResult},
                #{refundRatio},
                #{cardId},
                #{cardNo},
                #{cardNoUuid},
                #{customerName},
                #{serialNumber},
                #{payAmount},
                #{cancelReason},
                #{operatingId},
                #{operatingName},
                #{remark},
                NOW(),
                #{updateTime},
                #{payFailMsg},
                #{businessType})
    </insert>

    <update id="updateRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PastMemberRefundRecordPo">
        UPDATE past_member_refund_record
        SET
        <if test="pastMemberRefundRecord.optStatus != null">`opt_status`=
            #{pastMemberRefundRecord.optStatus},
        </if>
        <if test="pastMemberRefundRecord.optResult != null">`opt_result`=
            #{pastMemberRefundRecord.optResult},
        </if>
        <if test="pastMemberRefundRecord.verifyStatus != null">`verify_status`=
            #{pastMemberRefundRecord.verifyStatus},
        </if>
        <if test="pastMemberRefundRecord.verifyResult != null">`verify_result`=
            #{pastMemberRefundRecord.verifyResult},
        </if>
        <if test="pastMemberRefundRecord.retryCount != null">`retry_count`=
            #{pastMemberRefundRecord.retryCount},
        </if>
        <if test="pastMemberRefundRecord.serialNumber != null">`serial_number`=
            #{pastMemberRefundRecord.serialNumber},
        </if>
        <if test="pastMemberRefundRecord.remark != null">`remark`=
            #{pastMemberRefundRecord.remark},
        </if>
        <if test="pastMemberRefundRecord.payFailMsg != null">`pay_fail_msg`=
            #{pastMemberRefundRecord.payFailMsg},
        </if>
        <if test="pastMemberRefundRecord.payAmount != null">`pay_amount`=
            #{pastMemberRefundRecord.payAmount},
        </if>
        update_time = now()
        WHERE `id` = #{pastMemberRefundRecord.id}
    </update>

    <select id="loadPastMemberRefundRecord" parameterType="java.lang.Integer"
            resultMap="PastMemberRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM past_member_refund_record
        WHERE `id` = #{id}
    </select>


    <select id="loadByOrderSn" resultMap="PastMemberRefundRecord">
        select
        <include refid="Base_Column_List"/>
        from past_member_refund_record
        where order_sn=#{orderSn}
        order by id desc LIMIT 1
    </select>

    <select id="selectByOrderSn" resultMap="PastMemberRefundRecord">
        select
        <include refid="Base_Column_List"/>
        from past_member_refund_record
        where order_sn=#{orderSn} and serial_number is null
        order by id desc LIMIT 1
    </select>

    <select id="getToBeRefundList" resultMap="PastMemberRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM past_member_refund_record
        where `opt_status` = 0
        and business_type in (
        <foreach collection="businessTypes" index="index" item="businessType" separator=",">
            #{businessType}
        </foreach>
        )
        order by id
        LIMIT 0,60
    </select>


    <update id="batchProcessing">
        UPDATE past_member_refund_record
        set `opt_status`= 1, update_time = now()
        WHERE `id` in (
        <foreach collection="ids" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="getToBeVerifyList" resultMap="PastMemberRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM past_member_refund_record
        where `opt_status` = 1 and verify_status in (0,1) and retry_count &lt; 5
        order by id
        LIMIT 0,20
    </select>

    <select id="loadByOrderSnAndState"
            resultType="com.juzifenqi.plus.module.order.repository.po.PastMemberRefundRecordPo">
        select
        <include refid="Base_Column_List"/>
        from past_member_refund_record
        where order_sn=#{orderSn}
        and opt_status in (0,1,2,5)
    </select>

    <select id="loadBySerialNumber" resultMap="PastMemberRefundRecord">
        select
        <include refid="Base_Column_List"/>
        from past_member_refund_record
        where serial_number=#{serialNumber}
        order by id desc LIMIT 1
    </select>

    <select id="batchQueryPayInfo" resultMap="PastMemberRefundRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM past_member_refund_record
        where serial_number in
        <foreach collection="serialNumbers" item="serialNumber" index="index" open="(" separator=","
                close=")">
            #{serialNumber}
        </foreach>
    </select>

    <select id="getByDefraySerialNo" resultMap="PastMemberRefundRecord">
        select
        <include refid="Base_Column_List"/>
        from past_member_refund_record
        where defray_serial_no=#{defraySerialNo}
        order by id desc LIMIT 1
    </select>
    <select id="getWithEmptyBankAccountNoUuid" resultMap="PastMemberRefundRecord">
        select id, card_no
        from past_member_refund_record
        where card_no is not null
          and card_no_uuid is null
          and id > #{lastId}
        ORDER BY id ASC
            limit #{size}
    </select>
    <select id="getWithCardNoUuid" resultMap="PastMemberRefundRecord">
        select id, card_no, card_no_uuid
        from past_member_refund_record
        where card_no is not null
          and card_no_uuid is not null
          and id > #{lastId}
        ORDER BY id ASC
            limit #{size}
    </select>
    <update id="updateBatchCardNoUuid">
        <foreach collection="list" item="item" separator=";">
            update
            `past_member_refund_record`
            set
            `card_no_uuid` = #{item.cardNoUuid},
            `update_time` = now()
            where
            `id` = #{item.id}
        </foreach>
    </update>
</mapper>
