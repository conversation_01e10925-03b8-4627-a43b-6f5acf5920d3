<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRefundLogMapper">

    <resultMap id="PlusRefundLog"
            type="com.juzifenqi.plus.module.order.repository.po.PlusRefundLogPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_status" property="orderStatus"/>
        <result column="log_type" property="logType"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `order_sn`,
        `order_status`,
        `log_type`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusRefundLog"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundLogPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_refund_log (
        `user_id`,
        `order_sn`,
        `order_status`,
        `log_type`,
        `remark`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{userId},
        #{orderSn},
        #{orderStatus},
        #{logType},
        #{remark},
        NOW(),
        #{updateTime}
        )
    </insert>


    <delete id="deletePlusRefundLog" parameterType="java.lang.Integer">
        DELETE FROM plus_refund_log
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusRefundLog"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundLogPo">
        UPDATE plus_refund_log
        SET
        <if test="plusRefundLog.userId != null">`user_id`= #{plusRefundLog.userId},</if>
        <if test="plusRefundLog.orderSn != null">`order_sn`= #{plusRefundLog.orderSn},</if>
        <if test="plusRefundLog.orderStatus != null">`order_status`= #{plusRefundLog.orderStatus},
        </if>
        <if test="plusRefundLog.logType != null">`log_type`= #{plusRefundLog.logType},</if>
        <if test="plusRefundLog.remark != null">`remark`= #{plusRefundLog.remark},</if>
        update_time = now()
        WHERE `id` = #{plusRefundLog.id}
    </update>


    <select id="loadPlusRefundLog" parameterType="java.lang.Integer" resultMap="PlusRefundLog">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_log
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusRefundLog">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_log
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_refund_log
    </select>

</mapper>
