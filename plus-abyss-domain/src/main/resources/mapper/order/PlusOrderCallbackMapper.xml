<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderCallbackMapper">

    <resultMap id="PlusOrderCallback" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderCallbackPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="callback_type" property="callbackType" />
        <result column="callback_url" property="callbackUrl" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `user_id`,
        `channel_id`,
        `callback_type`,
        `callback_url`,
        `create_time`
    </sql>

    <insert id="batchInsert" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderCallbackPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_callback (
        `order_sn`,
        `user_id`,
        `channel_id`,
        `callback_type`,
        `callback_url`,
        `create_time`
        )
        VALUES
        <foreach collection="list" index="index" item="l" separator=",">
            (
            #{l.orderSn},
            #{l.userId},
            #{l.channelId},
            #{l.callbackType},
            #{l.callbackUrl},
            NOW()
            )
        </foreach>
    </insert>

    <select id="selectByOrderSn" resultMap="PlusOrderCallback">
        select <include refid="Base_Column_List"/> from plus_order_callback where order_sn = #{orderSn}
        and callback_type = #{callbackType} limit 1
    </select>

    <select id="selectByOrderSns" resultMap="PlusOrderCallback">
        select <include refid="Base_Column_List"/> from plus_order_callback where order_sn in
        <foreach collection="orderSns" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
        and callback_type = #{callbackType}
    </select>
</mapper>
