<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRenewDetailMapper">

    <resultMap id="PlusRenewDetail" type="com.juzifenqi.plus.module.order.repository.po.PlusRenewDetailPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel" property="channel"/>
        <result column="info_id" property="infoId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="serial_number" property="serialNumber"/>
        <result column="state" property="state"/>
        <result column="pay_state" property="payState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel`,
        `info_id`,
        `order_sn`,
        `serial_number`,
        `state`,
        `pay_state`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusRenewDetail"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRenewDetailPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_renew_detail (
              `info_id`,
              `user_id`,
              `channel`,
              `order_sn`,
              `serial_number`,
              `state`,
              `pay_state`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{infoId},
                #{userId},
                #{channel},
                #{orderSn},
                #{serialNumber},
                #{state},
                #{payState},
                  NOW(),
                #{updateTime}
      )
    </insert>

    <update id="updatePlusRenewDetail"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRenewDetailPo">
        UPDATE plus_renew_detail
        SET
        <if test="plusRenewDetail.infoId != null">`info_id`= #{plusRenewDetail.infoId},</if>
        <if test="plusRenewDetail.orderSn != null">`order_sn`= #{plusRenewDetail.orderSn},</if>
        <if test="plusRenewDetail.state != null">`state`= #{plusRenewDetail.state},</if>
        <if test="plusRenewDetail.payState != null">`pay_state`= #{plusRenewDetail.payState},</if>
        update_time = now()
        WHERE `id` = #{plusRenewDetail.id}
    </update>

    <select id="loadPlusRenewDetail" parameterType="java.lang.Integer" resultMap="PlusRenewDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_renew_detail
        WHERE `id` = #{id}
    </select>

    <select id="getRenewDetailByOrder" resultMap="PlusRenewDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_renew_detail
        WHERE `order_sn` = #{orderSn}
    </select>

</mapper>
