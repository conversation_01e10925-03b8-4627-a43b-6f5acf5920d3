<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRelationMapper">

    <resultMap id="PlusOrderRelation"
            type="com.juzifenqi.plus.module.order.repository.po.PlusOrderRelationPo">
        <result column="id" property="id"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="order_sn" property="orderSn"/>
        <result column="config_id" property="configId"/>
        <result column="business_type" property="businessType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `plus_order_sn`,
        `order_sn`,
        `config_id`,
        `business_type`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusOrderRelation"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRelationPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_relation (
        `plus_order_sn`,
        `order_sn`,
        `config_id`,
        `business_type`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{plusOrderSn},
        #{orderSn},
        #{configId},
        #{businessType},
        NOW(),
        #{updateTime}
        )
    </insert>


    <insert id="insertPlusOrderRelation"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRelationPo">
        INSERT INTO plus_order_relation (
        `plus_order_sn`,
        `order_sn`,
        `config_id`,
        `business_type`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{plusOrderRelation.plusOrderSn},
        #{plusOrderRelation.orderSn},
        #{plusOrderRelation.configId},
        #{plusOrderRelation.businessType},
        NOW(),
        #{plusOrderRelation.updateTime}
        )
    </insert>


    <delete id="deletePlusOrderRelation" parameterType="java.lang.Integer">
        DELETE FROM plus_order_relation
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusOrderRelation"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRelationPo">
        UPDATE plus_order_relation
        SET
        <if test="plusOrderRelation.plusOrderSn != null">`plus_order_sn`=
            #{plusOrderRelation.plusOrderSn},
        </if>
        <if test="plusOrderRelation.orderSn != null">`order_sn`= #{plusOrderRelation.orderSn},</if>
        <if test="plusOrderRelation.configId != null">`config_id`= #{plusOrderRelation.configId},
        </if>
        <if test="plusOrderRelation.businessType != null">`business_type`=
            #{plusOrderRelation.businessType},
        </if>
        update_time = now()
        WHERE `id` = #{plusOrderRelation.id}
    </update>


    <select id="loadPlusOrderRelation" parameterType="java.lang.Integer"
            resultMap="PlusOrderRelation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_relation
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusOrderRelation">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_relation
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_order_relation
    </select>


    <select id="listByBusinessTypeAndOrderSn" resultMap="PlusOrderRelation">
        select
        <include refid="Base_Column_List"/>
        from plus_order_relation where business_type = #{businessType} and order_sn in
        (
        <foreach collection="orderSns" index="index" item="orderSn" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <select id="listByBusinessTypeAndPlusOrderSn" resultMap="PlusOrderRelation">
        select
        <include refid="Base_Column_List"/>
        from plus_order_relation where business_type = #{businessType} and plus_order_sn in
        (
        <foreach collection="orderSns" index="index" item="orderSn" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>
</mapper>
