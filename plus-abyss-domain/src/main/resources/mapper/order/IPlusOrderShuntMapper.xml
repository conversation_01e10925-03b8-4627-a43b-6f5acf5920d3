<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderShuntMapper">

    <resultMap id="PlusOrderShunt" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="contract_no" property="contractNo"/>
        <result column="contract_no_back" property="contractNoBack"/>
        <result column="plan_in_supplier" property="planInSupplier"/>
        <result column="pay_type" property="payType"/>
        <result column="in_supplier" property="inSupplier"/>
        <result column="plan_in_business_scene" property="planInBusinessScene"/>
        <result column="bak" property="bak"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel_id`,
        `config_id`,
        `order_sn`,
        `contract_no`,
        `contract_no_back`,
        `plan_in_supplier`,
        `in_supplier`,
        `plan_in_business_scene`,
        `pay_type`,
        `bak`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insertPlusOrderShunt"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo">
        INSERT INTO plus_order_shunt (`user_id`,
                                      `channel_id`,
                                      `config_id`,
                                      `order_sn`,
                                      `contract_no`,
                                      `contract_no_back`,
                                      `plan_in_supplier`,
                                      `in_supplier`,
                                      `plan_in_business_scene`,
                                      `bak`,
                                      `create_time`)
        VALUES (#{plusOrderShunt.userId},
                #{plusOrderShunt.channelId},
                #{plusOrderShunt.configId},
                #{plusOrderShunt.orderSn},
                #{plusOrderShunt.contractNo},
                #{plusOrderShunt.contractNoBack},
                #{plusOrderShunt.planInSupplier},
                #{plusOrderShunt.inSupplier},
                #{plusOrderShunt.planInBusinessScene},
                #{plusOrderShunt.bak},
                NOW())
    </insert>

    <select id="selectByOrderSn" parameterType="java.lang.String" resultMap="PlusOrderShunt">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_shunt
        WHERE order_sn = #{orderSn} order by id desc limit 1
    </select>

    <update id="update"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo">
        UPDATE plus_order_shunt
        SET
        <if test="plusOrderShunt.contractNoBack != null">`contract_no_back`=
            #{plusOrderShunt.contractNoBack},
        </if>
        <if test="plusOrderShunt.inSupplier != null">`in_supplier`= #{plusOrderShunt.inSupplier},
        </if>
        <if test="plusOrderShunt.planInBusinessScene != null">`plan_in_business_scene`= #{plusOrderShunt.planInBusinessScene},
        </if>
        <if test="plusOrderShunt.bak != null">`bak`= #{plusOrderShunt.bak},</if>
        <if test="plusOrderShunt.payType != null">`pay_type`= #{plusOrderShunt.payType},</if>
        update_time = now()
        WHERE `id` = #{plusOrderShunt.id}
    </update>
</mapper>
