<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRelationBakMapper">


    <insert id="save"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRelationPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_relation_bak (
        `plus_order_sn`,
        `order_sn`,
        `config_id`,
        `business_type`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{plusOrderSn},
        #{orderSn},
        #{configId},
        #{businessType},
        NOW(),
        #{updateTime}
        )
    </insert>

</mapper>
