<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusDeductLogMapper">

    <resultMap id="PlusDeductLog" type="com.juzifenqi.plus.module.order.repository.po.PlusDeductLogPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="plus_order_sn" property="plusOrderSn" />
        <result column="order_sn" property="orderSn"/>
        <result column="log_type" property="logType"/>
        <result column="log_code" property="logCode"/>
        <result column="deduct_status" property="deductStatus"/>
        <result column="deduct_msg" property="deductMsg"/>
        <result column="bank_id" property="bankId"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `plus_order_sn`,
        `order_sn`,
        `log_type`,
        `log_code`,
        `deduct_status`,
        `deduct_msg`,
        `bank_id`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusDeductLog"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusDeductLogPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_deduct_log (
              `user_id`,
              `plus_order_sn`,
              `order_sn`,
              `log_type`,
              `log_code`,
              `deduct_status`,
              `deduct_msg`,
              `bank_id`,
              `remark`,
              `create_time`
      )
      VALUES(
                #{userId},
                #{plusOrderSn},
                #{orderSn},
                #{logType},
                #{logCode},
                #{deductStatus},
                #{deductMsg},
                #{bankId},
                #{remark},
                  NOW()
      )
    </insert>

    <select id="getByOrderSnAndUserId"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusDeductLogPo">
        select
        <include refid="Base_Column_List"/>
        from plus_deduct_log
        where plus_order_sn = #{orderSn}
        and user_id = #{userId}
        order By id desc
    </select>

</mapper>
