<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderModelSnapshotMapper">

    <resultMap id="plusOrderModelSnapshotPo"
            type="com.juzifenqi.plus.module.order.repository.po.PlusOrderModelSnapshotPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="model_id" property="modelId"/>
        <result column="remark" property="remark"/>
        <result column="short_name" property="shortName"/>
        <result column="guide_copy" property="guideCopy"/>
        <result column="saving_copy" property="savingCopy"/>
        <result column="sort" property="sort"/>
        <result column="rule_explain" property="ruleExplain"/>
        <result column="rule_image" property="ruleImage"/>
        <result column="alert_state" property="alertState"/>
        <result column="create_time" property="createTime"/>
        <result column="sort_time" property="sortTime"/>
        <result column="send_type" property="sendType"/>
        <result column="order_sn" property="orderSn"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        order_sn,
        `program_id`,
        `model_id`,
        `remark`,
        `short_name`,
        `guide_copy`,
        `saving_copy`,
        `sort`,
        `rule_explain`,
        `rule_image`,
        `alert_state`,
        `create_time`,
        `sort_time`,
        `send_type`
    </sql>

    <insert id="insertModelSnapshot"
            parameterType="com.juzifenqi.plus.module.program.repository.po.PlusProModelPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_model_snapshot (`program_id`,
        `model_id`,
        `remark`,
        `short_name`,
        `guide_copy`,
        `saving_copy`,
        `sort`,
        `rule_explain`,
        `rule_image`,
        `alert_state`,
        `create_time`,
        `sort_time`,
        send_type,
        order_sn)
        VALUES (#{programId},
        #{modelId},
        #{remark},
        #{shortName},
        #{guideCopy},
        #{savingCopy},
        #{sort},
        #{ruleExplain},
        #{ruleImage},
        #{alertState},
        NOW(),
        #{sortTime},
        #{sendType},
        #{orderSn})
    </insert>

    <insert id="batchInsertModelSnapshot">
        INSERT INTO plus_order_model_snapshot (`program_id`,
        `model_id`,
        `remark`,
        `short_name`,
        `guide_copy`,
        `saving_copy`,
        `sort`,
        `rule_explain`,
        `rule_image`,
        `alert_state`,
        `create_time`,
        `sort_time`,
        send_type,
        order_sn)
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.programId},
            #{record.modelId},
            #{record.remark},
            #{record.shortName},
            #{record.guideCopy},
            #{record.savingCopy},
            #{record.sort},
            #{record.ruleExplain},
            #{record.ruleImage},
            #{record.alertState},
            NOW(),
            #{record.sortTime},
             #{record.sendType},
             #{record.orderSn}
            )
        </foreach>
    </insert>

    <select id="listModelSnapshotByOrderSnBySort" resultMap="plusOrderModelSnapshotPo">
        SELECT
        <include refid="Base_Column_List"/>
        from plus_order_model_snapshot
        where order_sn = #{orderSn}
        order by sort asc, sort_time desc
    </select>

    <select id="getModelSnapshotByOrderSnAndModelId" resultMap="plusOrderModelSnapshotPo">
        SELECT <include refid="Base_Column_List"/>
        from plus_order_model_snapshot
        where order_sn = #{orderSn}
        and model_id = #{modelId}
    </select>

</mapper>
