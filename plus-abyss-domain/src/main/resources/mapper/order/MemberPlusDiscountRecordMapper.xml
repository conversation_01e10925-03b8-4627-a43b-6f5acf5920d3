<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IMemberPlusDiscountRecordMapper">

    <resultMap id="MemberPlusDiscountRecord"
            type="com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountRecordPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="conf_code" property="confCode"/>
        <result column="conf_tag" property="confTag"/>
        <result column="conf_type" property="confType"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="config_id" property="configId"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="discount_start_time" property="discountStartTime"/>
        <result column="discount_end_time" property="discountEndTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `conf_code`,
        `conf_tag`,
        `conf_type`,
        `discount_rate`,
        `config_id`,
        `effective_time`,
        `discount_start_time`,
        `discount_end_time`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusDiscountRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountRecordPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_discount_record (
        `user_id`,
        `channel_id`,
        `conf_code`,
        `conf_tag`,
        `conf_type`,
        `discount_rate`,
        `config_id`,
        `effective_time`,
        `discount_start_time`,
        `discount_end_time`,
        `remark`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{userId},
        #{channelId},
        #{confCode},
        #{confTag},
        #{confType},
        #{discountRate},
        #{configId},
        #{effectiveTime},
        #{discountStartTime},
        #{discountEndTime},
        #{remark},
        NOW(),
        #{updateTime}
        )
    </insert>

    <select id="getUserRecord" parameterType="java.util.Map"
            resultMap="MemberPlusDiscountRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_discount_record
        WHERE `user_id` = #{userId} and `channel_id` = #{channelId} and `conf_code` = #{confCode}
        order by id desc limit 1
    </select>

    <select id="getUserRecordList"  resultMap="MemberPlusDiscountRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_discount_record
        WHERE `user_id` = #{userId} and `channel_id` = #{channelId} and `conf_type` = #{confType}
        and config_id = #{configId}
        order by id
    </select>
</mapper>
