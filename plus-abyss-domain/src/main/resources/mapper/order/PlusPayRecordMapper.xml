<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusPayRecordMapper">

    <resultMap id="PlusPayRecord" type="com.juzifenqi.plus.module.order.repository.po.PlusPayRecordPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="order_sn" property="orderSn" />
        <result column="config_id" property="configId" />
        <result column="serial_number" property="serialNumber" />
        <result column="pay_state" property="payState" />
        <result column="pay_message" property="payMessage" />
        <result column="pay_type" property="payType" />
        <result column="pay_success_time" property="paySuccessTime" />
        <result column="pay_fail_time" property="payFailTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `config_id`,
        `serial_number`,
        `pay_state`,
        `pay_message`,
        `pay_type`,
        `pay_success_time`,
        `pay_fail_time`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusPayRecord" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusPayRecordPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_pay_record (
            `user_id`,
            `channel_id`,
            `order_sn`,
            `config_id`,
            `serial_number`,
            `pay_state`,
            `pay_message`,
            `pay_type`,
            `pay_success_time`,
            `pay_fail_time`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{userId},
                  #{channelId},
                  #{orderSn},
                  #{configId},
                  #{serialNumber},
                  #{payState},
                  #{payMessage},
                  #{payType},
                  #{paySuccessTime},
                  #{payFailTime},
                  NOW(),
                  #{updateTime}
    </insert>

    <update id="updatePlusPayRecord" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusPayRecordPo" >
        UPDATE plus_pay_record
        SET
        <if test="payState != null">`pay_state`= #{payState},</if>
        <if test="payMessage != null">`pay_message`= #{payMessage},</if>
        <if test="paySuccessTime != null">`pay_success_time`= #{paySuccessTime},</if>
        <if test="payFailTime != null">`pay_fail_time`= #{payFailTime},</if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="loadPlusPayRecord" resultMap="PlusPayRecord">
        SELECT <include refid="Base_Column_List" />
        FROM plus_pay_record
        WHERE order_sn = #{orderSn}
    </select>

</mapper>