<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IMemberPlusCancelNoticeRecordMapper">

    <resultMap id="MemberPlusCancelNoticeRecord"
            type="com.juzifenqi.plus.module.order.repository.po.MemberPlusCancelNoticeRecordPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel" property="channel"/>
        <result column="orderNo" property="orderNo"/>
        <result column="program_id" property="programId"/>
        <result column="conf_id" property="confId"/>
        <result column="type" property="type"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel`,
        `orderNo`,
        `program_id`,
        `conf_id`,
        `type`,
        `content`,
        `create_time`
    </sql>

    <insert id="insertMemberPlusCancelNoticeRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusCancelNoticeRecordPo">
        INSERT INTO member_plus_credit_record
        (`user_id`,
         `channel`,
         `orderNo`,
         `program_id`,
         `conf_id`,
         `type`,
         `content`,
         `create_time`)
        VALUES (#{record.userId},
                #{record.channel},
                #{record.orderNo},
                #{record.programId},
                #{record.confId},
                #{record.type},
                #{record.content},
                NOW())
    </insert>

    <update id="updateMemberPlusCancelNoticeRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusCancelNoticeRecordPo">
        UPDATE member_plus_credit_record
        SET
        <if test="record.userId != null">`user_id`=
            #{record.userId},
        </if>
        <if test="record.channel != null">`channel`=
            #{record.channel},
        </if>
        <if test="record.orderNo != null">`orderNo`=
            #{record.orderNo},
        </if>
        <if test="record.programId != null">`program_id`=
            #{record.programId},
        </if>
        <if test="record.confId != null">`conf_id`=
            #{record.confId},
        </if>
        <if test="record.type != null">
            `type`= #{record.type},
        </if>
        <if test="record.content != null">`content`=
            #{record.content},
        </if>
        WHERE `id` = #{record.id}
    </update>

    <select id="loadMemberPlusCancelNoticeRecord" parameterType="java.lang.Integer"
            resultMap="MemberPlusCancelNoticeRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_credit_record
        WHERE `id` = #{id}
    </select>

    <select id="getCancelPlusNoticeRiskByStatus" resultMap="MemberPlusCancelNoticeRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_credit_record
        WHERE `status` = 2
        AND num <![CDATA[ <  ]]>  4
    </select>

</mapper>
