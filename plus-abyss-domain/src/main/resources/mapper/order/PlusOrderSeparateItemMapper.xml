<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderSeparateItemMapper">
    <resultMap id="PlusOrderSeparateItem" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="apply_serial_no" property="applySerialNo" />
        <result column="supplier_type" property="supplierType" />
        <result column="supplier_id" property="supplierId" />
        <result column="merchant_id" property="merchantId" />
        <result column="separate_type" property="separateType" />
        <result column="separate_rate" property="separateRate" />
        <result column="separate_amount" property="separateAmount" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `apply_serial_no`,
        `supplier_type`,
        `supplier_id`,
        `merchant_id`,
        `separate_type`,
        `separate_rate`,
        `separate_amount`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusOrderSeparateItem" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_separate_item (
            `order_sn`,
            `apply_serial_no`,
            `supplier_type`,
            `supplier_id`,
            `merchant_id`,
            `separate_type`,
            `separate_rate`,
            `separate_amount`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{orderSn},
                  #{applySerialNo},
                  #{supplierType},
                  #{supplierId},
                  #{merchantId},
                  #{separateType},
                  #{separateRate},
                  #{separateAmount},
                  #{remark},
                  NOW(),
                  #{updateTime}
              )
    </insert>

    <update id="updatePlusOrderSeparateItem" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo" >
        UPDATE plus_order_separate_item
        SET
        <if test="plusOrderSeparateItem.orderSn != null">`order_sn`= #{plusOrderSeparateItem.orderSn},</if>
        <if test="plusOrderSeparateItem.applySerialNo != null">`apply_serial_no`= #{plusOrderSeparateItem.applySerialNo},</if>
        <if test="plusOrderSeparateItem.supplierType != null">`supplier_type`= #{plusOrderSeparateItem.supplierType},</if>
        <if test="plusOrderSeparateItem.supplierId != null">`supplier_id`= #{plusOrderSeparateItem.supplierId},</if>
        <if test="plusOrderSeparateItem.merchantId != null">`merchant_id`= #{plusOrderSeparateItem.merchantId},</if>
        <if test="plusOrderSeparateItem.separateType != null">`separate_type`= #{plusOrderSeparateItem.separateType},</if>
        <if test="plusOrderSeparateItem.separateRate != null">`separate_rate`= #{plusOrderSeparateItem.separateRate},</if>
        <if test="plusOrderSeparateItem.separateAmount != null">`separate_amount`= #{plusOrderSeparateItem.separateAmount},</if>
        <if test="plusOrderSeparateItem.remark != null">`remark`= #{plusOrderSeparateItem.remark},</if>
        update_time = now()
        WHERE `id` = #{plusOrderSeparateItem.id}
    </update>

    <insert id="batchInsert" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_separate_item (
        `order_sn`,
        `apply_serial_no`,
        `supplier_type`,
        `supplier_id`,
        `merchant_id`,
        `separate_type`,
        `separate_rate`,
        `separate_amount`,
        `remark`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.orderSn},
            #{item.applySerialNo},
            #{item.supplierType},
            #{item.supplierId},
            #{item.merchantId},
            #{item.separateType},
            #{item.separateRate},
            #{item.separateAmount},
            #{item.remark},
            NOW(),
            #{item.updateTime}
            )
        </foreach>
    </insert>

    <select id="getPlusOrderSeparateItems"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_separate_item
        WHERE order_sn = #{orderSn}
          AND apply_serial_no = #{applySerialNo}
        <if test="supplierType != null">
            AND supplier_type = #{supplierType}
        </if>
    </select>

    <resultMap id="PlusOrderSeparateAdminItem" type="com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemAdminEntity" >
        <result column="apply_serial_no" property="applySerialNo" />
        <result column="supplier_type" property="supplierType" />
        <result column="supplier_id" property="supplierId" />
        <result column="separate_type" property="separateType" />
        <result column="separate_rate" property="separateRate" />
        <result column="separate_amount" property="separateAmount" />
        <result column="separate_state" property="separateState" />
    </resultMap>

    <select id="getOrderSeparateItemList"
            resultType="com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemAdminEntity">
        SELECT s.separate_state,si.apply_serial_no,si.supplier_type,si.supplier_id,
               si.separate_type,si.separate_rate,si.separate_amount
        FROM plus_order_separate s,plus_order_separate_item si
        WHERE s.order_sn = si.order_sn
          AND s.apply_serial_no = si.apply_serial_no
          AND s.order_sn = #{orderSn}
          AND s.separate_enable_state = #{separateEnableState}
        ORDER BY s.create_time desc,si.separate_amount desc
    </select>

    <select id="getSeparateItemsByOrderSnAndPayAction"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparateItemPo">
        SELECT si.*
        FROM plus_order_separate s, plus_order_separate_item si
        WHERE s.order_sn = si.order_sn
          AND s.apply_serial_no = si.apply_serial_no
          AND s.order_sn = #{orderSn}
          AND s.order_pay_action = #{orderPayAction}
          AND s.separate_state = 2
        ORDER BY si.create_time desc
    </select>
</mapper>
