
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRdzxRepayPlanMapper">

    <resultMap id="PlusRdzxRepayPlan" type="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRepayPlanPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="plus_order_sn" property="plusOrderSn" />
        <result column="user_id" property="userId" />
        <result column="opt_status" property="optStatus" />
        <result column="retry_num" property="retryNum" />
        <result column="plan_time" property="planTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `plus_order_sn`,
        `user_id`,
        `opt_status`,
        `retry_num`,
        `plan_time`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusRdzxRepayPlan" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRepayPlanPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_rdzx_repay_plan (
            `order_sn`,
            `plus_order_sn`,
            `user_id`,
            `opt_status`,
            `retry_num`,
            `plan_time`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{orderSn},
                  #{plusOrderSn},
                  #{userId},
                  #{optStatus},
                  #{retryNum},
                  #{planTime},
                  NOW(),
                  NOW()
              )
    </insert>


    <delete id="deletePlusRdzxRepayPlan" parameterType="java.lang.Integer">
        DELETE FROM plus_rdzx_repay_plan
        WHERE `id` = #{id}
    </delete>

    <select id="getPlusRdzxRepayPlan"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRepayPlanPo">
        SELECT <include refid="Base_Column_List" />
        FROM plus_rdzx_repay_plan
        WHERE `opt_status` = 0 order by id asc limit 50
    </select>

    <update id="updatePlusRdzxRepayPlan" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRepayPlanPo" >
        UPDATE plus_rdzx_repay_plan
        SET
        <if test="plusRdzxRepayPlan.optStatus != null">`opt_status`= #{plusRdzxRepayPlan.optStatus},</if>
        <if test="plusRdzxRepayPlan.retryNum != null">`retry_num`= #{plusRdzxRepayPlan.retryNum}+1,</if>
        <if test="plusRdzxRepayPlan.planTime != null">`plan_time`= #{plusRdzxRepayPlan.planTime},</if>
        update_time = now()
        WHERE `id` = #{plusRdzxRepayPlan.id}
    </update>

    <update id="updatePlusRdzxRepayPlanCancel">
        UPDATE plus_rdzx_repay_plan
        SET
        <if test="plusRdzxRepayPlan.optStatus != null">`opt_status`= #{plusRdzxRepayPlan.optStatus},</if>
        update_time = now()
        WHERE `plus_order_sn` = #{plusRdzxRepayPlan.plusOrderSn}  and opt_status in (0,2)
    </update>
</mapper>
