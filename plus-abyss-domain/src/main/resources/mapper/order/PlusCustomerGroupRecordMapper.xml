<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusCustomerGroupRecordMapper">

    <resultMap id="PlusCustomerGroupRecord" type="com.juzifenqi.plus.module.order.repository.po.PlusCustomerGroupRecordPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="msg_body" property="msgBody" />
        <result column="diff_rate" property="diffRate" />
        <result column="reduce_quota_button" property="reduceQuotaButton" />
        <result column="reduce_quota_button_type" property="reduceQuotaButtonType" />
        <result column="waive_loan_button" property="waiveLoanButton" />
        <result column="exit_status" property="exitStatus" />
        <result column="error_msg" property="errorMsg" />
        <result column="periods" property="periods" />
        <result column="order_amount" property="orderAmount" />
        <result column="reject_order_sn" property="rejectOrderSn" />
        <result column="msg_id" property="msgId" />
        <result column="zp_retry_num" property="zpRetryNum"/>
        <result column="retry_num" property="retryNum"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `user_id`,
        `channel_id`,
        `msg_body`,
        `diff_rate`,
        `reduce_quota_button`,
        `waive_loan_button`,
        `exit_status`,
        `error_msg`,
        `periods`,
        `order_amount`,
        `reject_order_sn`,
        `msg_id`,
        `zp_retry_num`,
        `retry_num`,
        `create_time`,
        `update_time`,
        `reduce_quota_button_type`
    </sql>

    <insert id="savePlusCustomerGroupRecord" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusCustomerGroupRecordPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_customer_group_record (
            `order_sn`,
            `user_id`,
            `channel_id`,
            `msg_body`,
            `diff_rate`,
            `reduce_quota_button`,
            `waive_loan_button`,
            `exit_status`,
            `error_msg`,
            `periods`,
            `order_amount`,
            `reject_order_sn`,
            `msg_id`,
            `zp_retry_num`,
            `retry_num`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{orderSn},
                  #{userId},
                  #{channelId},
                  #{msgBody},
                  #{diffRate},
                  #{reduceQuotaButton},
                  #{waiveLoanButton},
                  #{exitStatus},
                  #{errorMsg},
                  #{periods},
                  #{orderAmount},
                  #{rejectOrderSn},
                  #{msgId},
                  #{zpRetryNum},
                  #{retryNum},
                  NOW(),
                  #{updateTime}
              )
    </insert>


    <insert id="insertPlusCustomerGroupRecord" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusCustomerGroupRecordPo">
        INSERT INTO plus_customer_group_record (
            `order_sn`,
            `user_id`,
            `channel_id`,
            `msg_body`,
            `diff_rate`,
            `reduce_quota_button`,
            `waive_loan_button`,
            `exit_status`,
            `error_msg`,
            `periods`,
            `order_amount`,
            `reject_order_sn`,
            `msg_id`,
            `zp_retry_num`,
            `retry_num`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{plusCustomerGroupRecord.orderSn},
                  #{plusCustomerGroupRecord.userId},
                  #{plusCustomerGroupRecord.channelId},
                  #{plusCustomerGroupRecord.msgBody},
                  #{plusCustomerGroupRecord.diffRate},
                  #{plusCustomerGroupRecord.reduceQuotaButton},
                  #{plusCustomerGroupRecord.waiveLoanButton},
                  #{plusCustomerGroupRecord.exitStatus},
                  #{plusCustomerGroupRecord.errorMsg},
                  #{plusCustomerGroupRecord.periods},
                  #{plusCustomerGroupRecord.orderAmount},
                  #{plusCustomerGroupRecord.rejectOrderSn},
                  #{plusCustomerGroupRecord.msgId},
                  #{plusCustomerGroupRecord.zpRetryNum},
                  #{plusCustomerGroupRecord.retryNum},
                  NOW(),
                  #{plusCustomerGroupRecord.updateTime}
              )
    </insert>


    <delete id="deletePlusCustomerGroupRecord" parameterType="java.lang.Integer">
        DELETE FROM plus_customer_group_record
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusCustomerGroupRecord" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusCustomerGroupRecordPo" >
        UPDATE plus_customer_group_record
        SET
        <if test="plusCustomerGroupRecord.orderSn != null">`order_sn`= #{plusCustomerGroupRecord.orderSn},</if>
        <if test="plusCustomerGroupRecord.userId != null">`user_id`= #{plusCustomerGroupRecord.userId},</if>
        <if test="plusCustomerGroupRecord.channelId != null">`channel_id`= #{plusCustomerGroupRecord.channelId},</if>
        <if test="plusCustomerGroupRecord.msgBody != null">`msg_body`= #{plusCustomerGroupRecord.msgBody},</if>
        <if test="plusCustomerGroupRecord.diffRate != null">`diff_rate`= #{plusCustomerGroupRecord.diffRate},</if>
        <if test="plusCustomerGroupRecord.reduceQuotaButton != null">`reduce_quota_button`= #{plusCustomerGroupRecord.reduceQuotaButton},</if>
        <if test="plusCustomerGroupRecord.waiveLoanButton != null">`waive_loan_button`= #{plusCustomerGroupRecord.waiveLoanButton},</if>
        <if test="plusCustomerGroupRecord.exitStatus != null">`exit_status`= #{plusCustomerGroupRecord.exitStatus},</if>
        <if test="plusCustomerGroupRecord.errorMsg != null">`error_msg`= #{plusCustomerGroupRecord.errorMsg},</if>
        <if test="plusCustomerGroupRecord.periods != null">`periods`= #{plusCustomerGroupRecord.periods},</if>
        <if test="plusCustomerGroupRecord.orderAmount != null">`order_amount`= #{plusCustomerGroupRecord.orderAmount},</if>
        <if test="plusCustomerGroupRecord.rejectOrderSn != null">`reject_order_sn`= #{plusCustomerGroupRecord.rejectOrderSn},</if>
        <if test="plusCustomerGroupRecord.msgId != null">`msg_id`= #{plusCustomerGroupRecord.msgId},</if>
        <if test="plusCustomerGroupRecord.zpRetryNum != null">`zp_retry_num`= #{plusCustomerGroupRecord.zpRetryNum},</if>
        <if test="plusCustomerGroupRecord.retryNum != null">`retry_num`= #{plusCustomerGroupRecord.retryNum},</if>
        <if test="plusCustomerGroupRecord.reduceQuotaButtonType != null">`reduce_quota_button_type`= #{plusCustomerGroupRecord.reduceQuotaButtonType},</if>
        update_time = now()
        WHERE `id` = #{plusCustomerGroupRecord.id}
    </update>


    <select id="loadPlusCustomerGroupRecord" parameterType="java.lang.Integer" resultMap="PlusCustomerGroupRecord">
        SELECT <include refid="Base_Column_List" />
        FROM plus_customer_group_record
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusCustomerGroupRecord">
        SELECT <include refid="Base_Column_List" />
        FROM plus_customer_group_record
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_customer_group_record
    </select>

    <select id="selectByOrderNo" parameterType="java.lang.String" resultMap="PlusCustomerGroupRecord">
        SELECT <include refid="Base_Column_List" />
        FROM plus_customer_group_record
        where order_sn = #{orderNo} order by id desc limit 1
    </select>

    <select id="selectByStateZp" parameterType="java.lang.Integer" resultMap="PlusCustomerGroupRecord">
        SELECT <include refid="Base_Column_List" />
        FROM plus_customer_group_record
        where exit_status = #{state} and zp_retry_num &lt; 5 limit #{size}
    </select>

    <select id="selectByState" parameterType="java.lang.Integer" resultMap="PlusCustomerGroupRecord">
        SELECT <include refid="Base_Column_List" />
        FROM plus_customer_group_record
        where exit_status in
        <foreach collection="states" item="state" index="index" open="(" separator="," close=")">
            #{state}
        </foreach>
        and retry_num &lt; 7
        limit #{start}, #{size}
    </select>

    <select id="selectByOrderNoAndUserId"
            resultMap="PlusCustomerGroupRecord">
        SELECT <include refid="Base_Column_List" />
        FROM plus_customer_group_record
        where reject_order_sn = #{rejectOrderSn}
        and user_id = #{userId}
    </select>

    <update id="updateBatchState">
        UPDATE plus_customer_group_record
        set `exit_status`= #{state}, error_msg = #{msg}, update_time = now()
        WHERE `id` in (
        <foreach collection="ids" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
</mapper>