<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IMemberPlusRenewPlanMapper">

    <resultMap id="MemberPlusRenewPlan"
            type="com.juzifenqi.plus.module.order.repository.po.MemberPlusRenewPlanPo">
        <result column="id" property="id"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="renew_state" property="renewState"/>
        <result column="renew_retry_count" property="renewRetryCount"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="periods" property="periods"/>
        <result column="renew_amount" property="renewAmount"/>
        <result column="group_id" property="groupId"/>
        <result column="plan_time" property="planTime"/>
        <result column="period_start_time" property="periodStartTime"/>
        <result column="period_end_time" property="periodEndTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `plus_order_sn`,
        `renew_state`,
        `renew_retry_count`,
        `user_id`,
        `channel_id`,
        `config_id`,
        `program_id`,
        `periods`,
        `renew_amount`,
        `group_id`,
        `plan_time`,
        `period_start_time`,
        `period_end_time`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusRenewPlan"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusRenewPlanPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO member_plus_renew_plan (
              `plus_order_sn`,
              `renew_state`,
              `renew_retry_count`,
              `user_id`,
              `channel_id`,
              `config_id`,
              `program_id`,
              `periods`,
              `renew_amount`,
              `group_id`,
              `plan_time`,
              `period_start_time`,
              `period_end_time`,
              `remark`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{plusOrderSn},
                #{renewState},
                #{renewRetryCount},
                #{userId},
                #{channelId},
                #{configId},
                #{programId},
                #{periods},
                #{renewAmount},
                #{groupId},
                #{planTime},
                #{periodStartTime},
                #{periodEndTime},
                #{remark},
                  NOW(),
                #{updateTime}
      )
    </insert>


    <insert id="insertMemberPlusRenewPlan"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusRenewPlanPo">
        INSERT INTO member_plus_renew_plan (
            `plus_order_sn`,
            `renew_state`,
            `renew_retry_count`,
            `user_id`,
            `channel_id`,
            `config_id`,
            `program_id`,
            `periods`,
            `renew_amount`,
            `group_id`,
            `plan_time`,
            `period_start_time`,
            `period_end_time`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
            #{memberPlusRenewPlan.plusOrderSn},
            #{memberPlusRenewPlan.renewState},
            #{memberPlusRenewPlan.renewRetryCount},
            #{memberPlusRenewPlan.userId},
            #{memberPlusRenewPlan.channelId},
            #{memberPlusRenewPlan.configId},
            #{memberPlusRenewPlan.programId},
            #{memberPlusRenewPlan.periods},
            #{memberPlusRenewPlan.renewAmount},
            #{memberPlusRenewPlan.groupId},
            #{memberPlusRenewPlan.planTime},
            #{memberPlusRenewPlan.periodStartTime},
            #{memberPlusRenewPlan.periodEndTime},
            #{memberPlusRenewPlan.remark},
            NOW(),
            #{memberPlusRenewPlan.updateTime}
        )
    </insert>


    <delete id="deleteMemberPlusRenewPlan" parameterType="java.lang.Integer">
        DELETE FROM member_plus_renew_plan
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusRenewPlan"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusRenewPlanPo">
        UPDATE member_plus_renew_plan
        SET
        <if test="memberPlusRenewPlan.plusOrderSn != null">`plus_order_sn`=
            #{memberPlusRenewPlan.plusOrderSn},
        </if>
        <if test="memberPlusRenewPlan.renewState != null">`renew_state`=
            #{memberPlusRenewPlan.renewState},
        </if>
        <if test="memberPlusRenewPlan.renewRetryCount != null">`renew_retry_count`=
            #{memberPlusRenewPlan.renewRetryCount},
        </if>
        <if test="memberPlusRenewPlan.userId != null">`user_id`= #{memberPlusRenewPlan.userId},</if>
        <if test="memberPlusRenewPlan.channelId != null">`channel_id`=
            #{memberPlusRenewPlan.channelId},
        </if>
        <if test="memberPlusRenewPlan.configId != null">`config_id`=
            #{memberPlusRenewPlan.configId},
        </if>
        <if test="memberPlusRenewPlan.programId != null">`program_id`=
            #{memberPlusRenewPlan.programId},
        </if>
        <if test="memberPlusRenewPlan.periods != null">`periods`= #{memberPlusRenewPlan.periods},
        </if>
        <if test="memberPlusRenewPlan.renewAmount != null">`renew_amount`=
            #{memberPlusRenewPlan.renewAmount},
        </if>
        <if test="memberPlusRenewPlan.groupId != null">`group_id`= #{memberPlusRenewPlan.groupId},
        </if>
        <if test="memberPlusRenewPlan.planTime != null">`plan_time`=
            #{memberPlusRenewPlan.planTime},
        </if>
        <if test="memberPlusRenewPlan.periodStartTime != null">`period_start_time`=
            #{memberPlusRenewPlan.periodStartTime},
        </if>
        <if test="memberPlusRenewPlan.periodEndTime != null">`period_end_time`=
            #{memberPlusRenewPlan.periodEndTime},
        </if>
        <if test="memberPlusRenewPlan.remark != null">`remark`= #{memberPlusRenewPlan.remark},</if>
        update_time = now()
        WHERE `id` = #{memberPlusRenewPlan.id}
    </update>


    <select id="loadMemberPlusRenewPlan" parameterType="java.lang.Integer"
            resultMap="MemberPlusRenewPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_renew_plan
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusRenewPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_renew_plan
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_renew_plan
    </select>
    <select id="getCurrentRenewPlan"
            resultType="com.juzifenqi.plus.module.order.repository.po.MemberPlusRenewPlanPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_renew_plan
        where group_id = #{groupId}
        and period_start_time &lt;= now()
        and period_end_time &gt; now() order by id desc limit 1
    </select>

</mapper>
