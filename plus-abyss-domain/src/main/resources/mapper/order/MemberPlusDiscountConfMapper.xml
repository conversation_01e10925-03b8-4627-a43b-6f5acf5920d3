<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IMemberPlusDiscountConfMapper">

    <resultMap id="MemberPlusDiscountConf"
            type="com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountConfPo">
        <result column="id" property="id"/>
        <result column="conf_name" property="confName"/>
        <result column="conf_code" property="confCode"/>
        <result column="conf_type" property="confType"/>
        <result column="conf_tag" property="confTag"/>
        <result column="config_id" property="configId"/>
        <result column="discount_rate" property="discountRate"/>
        <result column="effective_time" property="effectiveTime"/>
        <result column="conf_state" property="confState"/>
        <result column="opt_user_id" property="optUserId"/>
        <result column="opt_user_name" property="optUserName"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `conf_name`,
        `conf_code`,
        `conf_type`,
        `conf_tag`,
        `config_id`,
        `discount_rate`,
        `effective_time`,
        `conf_state`,
        `opt_user_id`,
        `opt_user_name`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <select id="getByCode" parameterType="java.lang.String"
            resultMap="MemberPlusDiscountConf">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_discount_conf
        WHERE `conf_code` = #{confCode} and conf_state = 1
    </select>

    <select id="getByName" parameterType="java.lang.String"
            resultMap="MemberPlusDiscountConf">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_discount_conf
        WHERE `conf_name` = #{confName}
    </select>


    <insert id="saveMemberPlusDiscountConf"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountConfPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_discount_conf (
            `conf_name`,
            `conf_code`,
            `conf_type`,
            `conf_tag`,
            `config_id`,
            `discount_rate`,
            `effective_time`,
            `opt_user_id`,
            `opt_user_name`,
            `remark`,
            `create_time`
        )
        VALUES(
                  #{confName},
                  #{confCode},
                  #{confType},
                  #{confTag},
                  #{configId},
                  #{discountRate},
                  #{effectiveTime},
                  #{optUserId},
                  #{optUserName},
                  #{remark},
                  NOW()
              )
    </insert>

    <select id="getById" parameterType="java.lang.Integer"
            resultMap="MemberPlusDiscountConf">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_discount_conf
        WHERE `id` = #{id}
    </select>

    <update id="updateMemberPlusDiscountConf"
            parameterType="com.juzifenqi.plus.module.order.repository.po.MemberPlusDiscountConfPo">
        UPDATE plus_discount_conf
        SET
        <if test="param.confName != null and param.confName != ''">`conf_name`=
            #{param.confName},
        </if>
        <if test="param.configId != null">`config_id`=
            #{param.configId},
        </if>
        <if test="param.confTag != null">`conf_tag`=
            #{param.confTag},
        </if>
        <if test="param.discountRate != null">`discount_rate`=
            #{param.discountRate},
        </if>
        <if test="param.effectiveTime != null">`effective_time`=
            #{param.effectiveTime},
        </if>
        <if test="param.confState != null">`conf_state`=
            #{param.confState},
        </if>
        <if test="param.optUserId != null">`opt_user_id`=
            #{param.optUserId},
        </if>
        <if test="param.optUserName != null">`opt_user_name`=
            #{param.optUserName},
        </if>
        <if test="param.remark != null">`remark`=
            #{param.remark},
        </if>
        update_time = now()
        WHERE `id` = #{param.id}
    </update>

    <select id="pageListByQuery" parameterType="java.util.Map" resultMap="MemberPlusDiscountConf">
        select
        <include refid="Base_Column_List"/>
        FROM plus_discount_conf
        <include refid="whereConditions2"/>
        order by `create_time` desc
        LIMIT #{pageStart}, #{pagesize}
    </select>

    <sql id="whereConditions2">
        <where>
            <if test="param.confName != null and param.confName != ''">
                and `conf_name` = #{param.confName}
            </if>
            <if test="param.configId != null">
                and `config_id` = #{param.configId}
            </if>
            <if test="param.confType != null">
                and `conf_type` = #{param.confType}
            </if>
            <if test="param.confState != null">
                and `conf_state` = #{param.confState}
            </if>
        </where>
    </sql>

    <select id="pageListCountByQuery" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_discount_conf
        <include refid="whereConditions2"/>
    </select>
</mapper>
