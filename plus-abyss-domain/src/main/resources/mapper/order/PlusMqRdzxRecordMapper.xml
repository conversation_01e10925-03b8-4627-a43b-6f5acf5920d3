<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusMqRdzxRecordMapper">

    <resultMap id="PlusMqRdzxRecord" type="com.juzifenqi.plus.module.order.repository.po.PlusMqRdzxRecordPo">
        <result column="id" property="id"/>
        <result column="msg_id" property="msgId"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="period_num" property="periodNum"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="order_channel" property="orderChannel"/>
        <result column="order_node" property="orderNode"/>
        <result column="opt_status" property="optStatus"/>
        <result column="remark" property="remark"/>
        <result column="bank_id" property="bankId"/>
        <result column="capital_id" property="capitalId"/>
        <result column="retry_num" property="retryNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="rd_equity_mark" property="rdEquityMark"/>
        <result column="repay_cash_back" property="repayCashBack"/>
        <result column="order_type" property="orderType"/>
        <result column="order_sub_type" property="orderSubType"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `msg_id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `period_num`,
        `order_amount`,
        `order_channel`,
        `order_node`,
        `opt_status`,
        `remark`,
        `bank_id`,
        `capital_id`,
        `retry_num`,
        `create_time`,
        `update_time`,
        `rd_equity_mark`,
        `repay_cash_back`,
        `order_type`,
         `order_sub_type`
    </sql>

    <insert id="savePlusMqRdzxRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusMqRdzxRecordPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_mq_rdzx_record (
              `msg_id`,
              `user_id`,
              `channel_id`,
              `order_sn`,
              `period_num`,
              `order_amount`,
              `order_channel`,
              `order_node`,
              `opt_status`,
              `remark`,
              `bank_id`,
              `retry_num`,
              `capital_id`,
              `create_time`,
              `update_time`,
              `rd_equity_mark`,
              `repay_cash_back`,
              `order_type`,
              `order_sub_type`
      )
      VALUES(
                #{msgId},
                #{userId},
                #{channelId},
                #{orderSn},
                #{periodNum},
                #{orderAmount},
                #{orderChannel},
                #{orderNode},
                #{optStatus},
                #{remark},
                #{bankId},
                #{retryNum},
                #{capitalId},
                  NOW(),
                #{updateTime},
                #{rdEquityMark},
                #{repayCashBack},
                #{orderType},
                #{orderSubType}
      )
    </insert>


    <select id="list" resultType="com.juzifenqi.plus.module.order.repository.po.PlusMqRdzxRecordPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_mq_rdzx_record
        where  opt_status  = #{optStatus}
        order by id
        <if test="sort == 1">
            asc
        </if>
        <if test="sort == 2">
            desc
        </if>
        limit 50
    </select>

    <update id="updatePlusMqRdzxRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusMqRdzxRecordPo">
        UPDATE plus_mq_rdzx_record
        SET
        <if test="plusMqRdzxRecord.msgId != null">`msg_id`= #{plusMqRdzxRecord.msgId},</if>
        <if test="plusMqRdzxRecord.userId != null">`user_id`= #{plusMqRdzxRecord.userId},</if>
        <if test="plusMqRdzxRecord.channelId != null">`channel_id`= #{plusMqRdzxRecord.channelId},
        </if>
        <if test="plusMqRdzxRecord.orderSn != null">`order_sn`= #{plusMqRdzxRecord.orderSn},</if>
        <if test="plusMqRdzxRecord.orderChannel != null">`order_channel`=
            #{plusMqRdzxRecord.orderChannel},
        </if>
        <if test="plusMqRdzxRecord.orderNode != null">`order_node`= #{plusMqRdzxRecord.orderNode},
        </if>
        <if test="plusMqRdzxRecord.optStatus != null">`opt_status`= #{plusMqRdzxRecord.optStatus},
        </if>
        <if test="plusMqRdzxRecord.remark != null">`remark`= #{plusMqRdzxRecord.remark},</if>
        <if test="plusMqRdzxRecord.retryNum != null">`retry_num`= #{plusMqRdzxRecord.retryNum},</if>
        update_time = now()
        WHERE `id` = #{plusMqRdzxRecord.id}
    </update>
    <select id="loadPlusMqRdzxRecord" parameterType="java.lang.Integer"
            resultMap="PlusMqRdzxRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_mq_rdzx_record
        WHERE `id` = #{id}
    </select>
    <select id="getOrderRecordInfo"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusMqRdzxRecordPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_mq_rdzx_record
        WHERE `order_sn` = #{orderId} limit 1
    </select>

    <update id="updateStatusByIds">
        update plus_mq_rdzx_record set opt_status = #{status}, update_time = now()
        <where>
            <foreach collection="ids" item="id" separator="," open="id in (" close=")">
                ${id}
            </foreach>
        </where>
    </update>

</mapper>
