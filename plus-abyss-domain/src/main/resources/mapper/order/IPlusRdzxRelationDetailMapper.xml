<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRdzxRelationDetailMapper">

    <resultMap id="PlusRdzxRelationDetail"
            type="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="loan_amount" property="loanAmount"/>
        <result column="fk_rate" property="fkRate"/>
        <result column="period_num" property="periodNum"/>
        <result column="service_fee" property="serviceFee"/>
        <result column="discount_amount" property="discountAmount"/>
        <result column="bank_id" property="bankId"/>
        <result column="plan_time" property="planTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `plus_order_sn`,
        `loan_amount`,
        `fk_rate`,
        `period_num`,
        `service_fee`,
        `discount_amount`,
        `bank_id`,
        `plan_time`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusRdzxRelationDetail"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_rdzx_relation_detail (
              `user_id`,
              `channel_id`,
              `order_sn`,
              `plus_order_sn`,
              `loan_amount`,
              `fk_rate`,
              `period_num`,
              `service_fee`,
              `discount_amount`,
              `discount_rate`,
              `bank_id`,
              `plan_time`,
              `remark`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{userId},
                #{channelId},
                #{orderSn},
                #{plusOrderSn},
                #{loanAmount},
                #{fkRate},
                #{periodNum},
                #{serviceFee},
                #{discountAmount},
                #{discountRate},
                #{bankId},
                #{planTime},
                #{remark},
                  NOW(),
                #{updateTime}
      )
    </insert>

    <update id="updatePlusRdzxRelationDetail"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        UPDATE plus_rdzx_relation_detail
        SET
        <if test="plusRdzxRelationDetail.userId != null">`user_id`=
            #{plusRdzxRelationDetail.userId},
        </if>
        <if test="plusRdzxRelationDetail.channelId != null">`channel_id`=
            #{plusRdzxRelationDetail.channelId},
        </if>
        <if test="plusRdzxRelationDetail.orderSn != null">`order_sn`=
            #{plusRdzxRelationDetail.orderSn},
        </if>
        <if test="plusRdzxRelationDetail.serviceFee != null">`service_fee`=
            #{plusRdzxRelationDetail.serviceFee},
        </if>
        <if test="plusRdzxRelationDetail.discountAmount != null">`discount_amount`=
            #{plusRdzxRelationDetail.discountAmount},
        </if>
        <if test="plusRdzxRelationDetail.bankId != null">`bank_id`=
            #{plusRdzxRelationDetail.bankId},
        </if>
        <if test="plusRdzxRelationDetail.planTime != null">`plan_time`=
            #{plusRdzxRelationDetail.planTime},
        </if>
        <if test="plusRdzxRelationDetail.remark != null">`remark`=
            #{plusRdzxRelationDetail.remark},
        </if>
        update_time = now()
        WHERE `order_sn` = #{plusRdzxRelationDetail.orderSn}
    </update>


    <select id="loadPlusRdzxRelationDetail" parameterType="java.lang.Integer"
            resultMap="PlusRdzxRelationDetail">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_rdzx_relation_detail
        WHERE `id` = #{id}
    </select>

    <select id="selectByUserId"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
            plus_rdzx_relation_detail
        WHERE
            user_id = #{userId}
    </select>

    <select id="selectByPlusOrderSn" parameterType="java.lang.String"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_rdzx_relation_detail
        WHERE
        plus_order_sn = #{plusOrderSn}
    </select>

    <select id="selectByOrderSnAndUserId"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_rdzx_relation_detail
        WHERE
        plus_order_sn = #{plusOrderSn} and user_id = #{userId}
    </select>

    <select id="selectByPlusOrderSnList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_rdzx_relation_detail
        WHERE
        <foreach collection="plusOrderSnList" item="orderSn" separator="," open="plus_order_sn in ("
                close=")">
            #{orderSn}
        </foreach>
    </select>

    <select id="selectByLoanOrderSnList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_rdzx_relation_detail
        WHERE
        <foreach collection="loanOrderSnList" item="orderSn" separator="," open="order_sn in ("
                 close=")">
            #{orderSn}
        </foreach>
    </select>

    <select id="selectJobList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_rdzx_relation_detail
        WHERE
        create_time>='2022-10-27 00:00:00' and create_time &lt;='2022-12-17 20:10:48'
        and id> #{start}
        limit #{size}
    </select>

    <select id="selectLastByUserId"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_rdzx_relation_detail
        WHERE
        user_id = #{userId} and channel_id = #{channel}
        order by create_time desc
        limit 1
    </select>

    <select id="getByOrderSn"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRdzxRelationDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        plus_rdzx_relation_detail
        WHERE
        order_sn = #{orderSn}
        order by id desc limit 1
    </select>

</mapper>
