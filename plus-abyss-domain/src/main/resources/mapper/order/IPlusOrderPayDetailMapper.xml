<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderPayDetailMapper">

    <resultMap id="PlusOrderPayDetail" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderPayDetailPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="order_sn" property="orderSn" />
        <result column="trade_sn" property="tradeSn" />
        <result column="order_type" property="orderType" />
        <result column="config_id" property="configId" />
        <result column="program_id" property="programId" />
        <result column="program_name" property="programName" />
        <result column="pay_type" property="payType" />
        <result column="pay_state" property="payState" />
        <result column="order_amount" property="orderAmount" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `trade_sn`,
        `order_type`,
        `config_id`,
        `program_id`,
        `program_name`,
        `pay_type`,
        `pay_state`,
        `order_amount`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusOrderPayDetail" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderPayDetailPo" useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_order_pay_detail (
              `user_id`,
              `channel_id`,
              `order_sn`,
              `trade_sn`,
              `order_type`,
              `config_id`,
              `program_id`,
              `program_name`,
              `pay_type`,
              `pay_state`,
              `order_amount`,
              `remark`,
              `create_time`
      )
      VALUES(
                #{userId},
                #{channelId},
                #{orderSn},
                #{tradeSn},
                #{orderType},
                #{configId},
                #{programId},
                #{programName},
                #{payType},
                #{payState},
                #{orderAmount},
                #{remark},
                  NOW()
      )
    </insert>


    <update id="invalidDetail">
        UPDATE plus_order_pay_detail
        SET
        `pay_state`= 0,
        update_time = now()
        WHERE `id` = #{id}
    </update>


    <select id="loadByPlusOrderSn" parameterType="java.lang.String" resultMap="PlusOrderPayDetail">
        SELECT <include refid="Base_Column_List" />
        FROM plus_order_pay_detail
        WHERE `order_sn` = #{plusOrderSn}
    </select>

</mapper>
