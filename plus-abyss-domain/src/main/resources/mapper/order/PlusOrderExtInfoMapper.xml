<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderExtInfoMapper">

    <resultMap id="PlusOrderExtInfo" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderExtInfoPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="order_sn" property="orderSn" />
        <result column="order_amount" property="orderAmount" />
        <result column="virtual_amount" property="virtualAmount" />
        <result column="business_type" property="businessType" />
        <result column="order_flag" property="orderFlag"/>
        <result column="pay_type" property="payType"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `order_amount`,
        `virtual_amount`,
        `business_type`,
        `order_flag`,
        `pay_type`,
        `pay_success_return_url`,
        `serial_number`,
        `out_order_sn`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderExtInfoPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_ext_info (
            `user_id`,
            `channel_id`,
            `order_sn`,
            `order_amount`,
            `virtual_amount`,
            `business_type`,
            `order_flag`,
            `pay_type`,
            `pay_success_return_url`,
            `serial_number`,
            `out_order_sn`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{userId},
                  #{channelId},
                  #{orderSn},
                  #{orderAmount},
                  #{virtualAmount},
                  #{businessType},
                  #{orderFlag},
                  #{payType},
                  #{paySuccessReturnUrl},
                  #{serialNumber},
                  #{outOrderSn},
                  NOW(),
                  #{updateTime}
              )
    </insert>

    <select id="getByOrderSn" parameterType="java.lang.String" resultMap="PlusOrderExtInfo">
        SELECT <include refid="Base_Column_List" />
        FROM plus_order_ext_info
        where order_sn = #{orderSn} order by id desc limit 1
    </select>

    <select id="selectByOrderSns" parameterType="java.lang.String" resultMap="PlusOrderExtInfo">
        SELECT <include refid="Base_Column_List" />
        FROM plus_order_ext_info
        where order_sn in
        <foreach collection="list" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
    </select>

</mapper>