<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRefundApplyMapper">

    <resultMap id="PlusOrderRefundApply" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundApplyPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="program_id" property="programId" />
        <result column="config_id" property="configId" />
        <result column="refund_type" property="refundType" />
        <result column="refund_rate" property="refundRate" />
        <result column="deal_state" property="dealState" />
        <result column="deal_msg" property="dealMsg" />
        <result column="retry_count" property="retryCount" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `user_id`,
        `deal_msg`,
        `program_id`,
        `config_id`,
        `channel_id`,
        `refund_type`,
        `refund_rate`,
        `deal_state`,
        `retry_count`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusOrderRefundApply" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundApplyPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_refund_apply (
        `order_sn`,
        `user_id`,
        `channel_id`,
        `config_id`,
        `program_id`,
        `refund_type`,
        `refund_rate`,
        `deal_state`,
        `deal_msg`,
        `retry_count`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{orderSn},
        #{userId},
        #{channelId},
        #{configId},
        #{programId},
        #{refundType},
        #{refundRate},
        #{dealState},
        #{dealMsg},
        #{retryCount},
        NOW(),
        #{updateTime}
        )
    </insert>


    <update id="updatePlusOrderRefundApply" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundApplyPo" >
        UPDATE plus_order_refund_apply
        SET
        <if test="dealState != null">`deal_state`= #{dealState},</if>
        <if test="dealMsg != null">`deal_msg`= #{dealMsg},</if>
        <if test="retryCount != null">`retry_count`= #{retryCount},</if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <update id="batchUpdateState">
        UPDATE plus_order_refund_apply
        SET deal_state = #{dealState} ,deal_msg = #{dealMsg} ,update_time = now()
        WHERE `id` in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="selectList" resultMap="PlusOrderRefundApply">
        select <include refid="Base_Column_List"/> from plus_order_refund_apply
        where channel_id = #{channelId} and deal_state = #{dealState} order by id limit #{limit}
    </select>

    <update id="batchUpdate">
        UPDATE plus_order_refund_apply
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="deal_state =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.dealState!=null">
                        when id = #{i.id} then #{i.dealState}
                    </if>
                </foreach>
            </trim>
            <trim prefix="deal_msg =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.dealMsg!=null">
                        when id = #{i.id} then #{i.dealMsg}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    when id = #{i.id} then now()
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" separator="," item="i" index="index" open="(" close=")">
            #{i.id}
        </foreach>
    </update>

    <select id="selectByOrderSn" resultMap="PlusOrderRefundApply">
        select <include refid="Base_Column_List"/> from plus_order_refund_apply
        where order_sn  = #{orderSn} and deal_state in
        <foreach collection="states" separator="," item="state" index="index" open="(" close=")">
            #{state}
        </foreach>
    </select>
</mapper>
