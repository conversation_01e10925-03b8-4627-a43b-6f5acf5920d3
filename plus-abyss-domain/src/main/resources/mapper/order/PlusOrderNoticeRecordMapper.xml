<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderNoticeRecordMapper">

    <resultMap id="PlusOrderNoticeRecord" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderNoticeRecordPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="notice_type" property="noticeType" />
        <result column="notice_state" property="noticeState" />
        <result column="callback_url" property="callbackUrl" />
        <result column="notice_body" property="noticeBody" />
        <result column="notice_result" property="noticeResult" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `user_id`,
        `channel_id`,
        `notice_type`,
        `notice_state`,
        `callback_url`,
        `notice_body`,
        `notice_result`,
        `create_time`
    </sql>

    <insert id="batchInsert" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderNoticeRecordPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_notice_record (
        `order_sn`,
        `user_id`,
        `channel_id`,
        `notice_type`,
        `notice_state`,
        `callback_url`,
        `notice_body`,
        `notice_result`,
        `create_time`
        )
        VALUES
        <foreach collection="list" index="index" item="l" separator=",">
            (
            #{l.orderSn},
            #{l.userId},
            #{l.channelId},
            #{l.noticeType},
            #{l.noticeState},
            #{l.callbackUrl},
            #{l.noticeBody},
            #{l.noticeResult},
            NOW()
            )
        </foreach>
    </insert>
</mapper>
