<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderSeparateMapper">
    <resultMap id="PlusOrderSeparate" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="user_id" property="userId" />
        <result column="apply_serial_no" property="applySerialNo" />
        <result column="shunt_supplier_id" property="shuntSupplierId" />
        <result column="separate_enable_state" property="separateEnableState" />
        <result column="settle_enable_state" property="settleEnableState" />
        <result column="total_separate_amount" property="totalSeparateAmount" />
        <result column="bank_card_id" property="bankCardId" />
        <result column="business_scene" property="businessScene" />
        <result column="pay_serial_no" property="paySerialNo" />
        <result column="separate_state" property="separateState" />
        <result column="pay_callback_time" property="payCallbackTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="order_pay_action" property="orderPayAction" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `user_id`,
        `apply_serial_no`,
        `shunt_supplier_id`,
        `separate_enable_state`,
        `settle_enable_state`,
        `total_separate_amount`,
        `bank_card_id`,
        `business_scene`,
        `pay_serial_no`,
        `separate_state`,
        `pay_callback_time`,
        `remark`,
        `create_time`,
        `update_time`,
        `order_pay_action`
    </sql>

    <insert id="savePlusOrderSeparate" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_separate (
            `order_sn`,
            `user_id`,
            `apply_serial_no`,
            `shunt_supplier_id`,
            `separate_enable_state`,
            `settle_enable_state`,
            `total_separate_amount`,
            `bank_card_id`,
            `business_scene`,
            `pay_serial_no`,
            `separate_state`,
            `pay_callback_time`,
            `remark`,
            `create_time`,
            `update_time`,
            `order_pay_action`
        )
        VALUES(
                  #{orderSn},
                  #{userId},
                  #{applySerialNo},
                  #{shuntSupplierId},
                  #{separateEnableState},
                  #{settleEnableState},
                  #{totalSeparateAmount},
                  #{bankCardId},
                  #{businessScene},
                  #{paySerialNo},
                  #{separateState},
                  #{payCallbackTime},
                  #{remark},
                  NOW(),
                  #{updateTime},
                  #{orderPayAction}
              )
    </insert>

    <update id="updatePlusOrderSeparate" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo" >
        UPDATE plus_order_separate
        SET
        <if test="plusOrderSeparate.paySerialNo != null">`pay_serial_no`= #{plusOrderSeparate.paySerialNo},</if>
        <if test="plusOrderSeparate.separateState != null">`separate_state`= #{plusOrderSeparate.separateState},</if>
        <if test="plusOrderSeparate.payCallbackTime != null">`pay_callback_time`= #{plusOrderSeparate.payCallbackTime},</if>
        <if test="plusOrderSeparate.remark != null">`remark`= #{plusOrderSeparate.remark},</if>
        update_time = now()
        WHERE `id` = #{plusOrderSeparate.id}
    </update>

    <update id="updatePlusOrderSeparateState">
        UPDATE plus_order_separate
        SET `separate_state` = #{separateState},
            update_time     = now()
        WHERE `order_sn` = #{orderSn}
          AND `order_pay_action` = #{orderPayAction}
    </update>

    <select id="getOrderSeparateByApplyNo"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_separate
        WHERE order_sn = #{orderSn}
          AND apply_serial_no = #{applySerialNo}
    </select>

    <select id="getOrderSeparateByOrderNo"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_separate
        WHERE order_sn = #{orderSn}
    </select>

    <select id="getOrderSeparateByStatus"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_separate
        WHERE order_sn = #{orderSn}
          AND separate_state = #{separateState}
        order by id desc
        limit 1
    </select>

    <select id="getLastByOrderSn"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderSeparatePo">
        select
        <include refid="Base_Column_List"/>
        from plus_order_separate
        where order_sn = #{orderSn}
        order by id desc
        limit 1
    </select>
</mapper>
