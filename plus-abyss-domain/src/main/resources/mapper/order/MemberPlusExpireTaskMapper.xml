<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusExpireTaskMapper">

    <resultMap id="MemberPlusExpireTask" type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusExpireTaskPo" >
        <result column="id" property="id" />
        <result column="plus_order_sn" property="plusOrderSn" />
        <result column="task_state" property="taskState" />
        <result column="retry_count" property="retryCount" />
        <result column="plan_time" property="planTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `plus_order_sn`,
        `task_state`,
        `retry_count`,
        `plan_time`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusExpireTask" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusExpireTaskPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_expire_task (
        `plus_order_sn`,
        `task_state`,
        `retry_count`,
        `plan_time`,
        `remark`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{plusOrderSn},
        #{taskState},
        #{retryCount},
        #{planTime},
        #{remark},
        NOW(),
        #{updateTime}
        )
    </insert>


    <insert id="insertMemberPlusExpireTask" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusExpireTaskPo">
        INSERT INTO member_plus_expire_task (
        `plus_order_sn`,
        `task_state`,
        `retry_count`,
        `plan_time`,
        `remark`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{memberPlusExpireTask.plusOrderSn},
        #{memberPlusExpireTask.taskState},
        #{memberPlusExpireTask.retryCount},
        #{memberPlusExpireTask.planTime},
        #{memberPlusExpireTask.remark},
        NOW(),
        #{memberPlusExpireTask.updateTime}
        )
    </insert>


    <delete id="deleteMemberPlusExpireTask" parameterType="java.lang.Integer">
        DELETE FROM member_plus_expire_task
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusExpireTask" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusExpireTaskPo" >
        UPDATE member_plus_expire_task
        SET
        <if test="memberPlusExpireTask.plusOrderSn != null">`plus_order_sn`= #{memberPlusExpireTask.plusOrderSn},</if>
        <if test="memberPlusExpireTask.taskState != null">`task_state`= #{memberPlusExpireTask.taskState},</if>
        <if test="memberPlusExpireTask.retryCount != null">`retry_count`= #{memberPlusExpireTask.retryCount},</if>
        <if test="memberPlusExpireTask.planTime != null">`plan_time`= #{memberPlusExpireTask.planTime},</if>
        <if test="memberPlusExpireTask.remark != null">`remark`= #{memberPlusExpireTask.remark},</if>
        update_time = now()
        WHERE `id` = #{memberPlusExpireTask.id}
    </update>
    <update id="updateStatusBatch">
        UPDATE member_plus_expire_task
        SET `task_state`= 2,
        update_time = now()
        WHERE `id` in
        <foreach collection="ids" item="item" open="(" close=")" separator=",">
            #{item}
        </foreach>
    </update>
    <update id="updateStatusSingle">
        UPDATE member_plus_expire_task
        SET `task_state`= #{taskState},
            `remark`= #{remark},
            `retry_count` = `retry_count` + 1,
            update_time = now()
        WHERE `id` = #{id}
    </update>


    <select id="loadMemberPlusExpireTask" parameterType="java.lang.Integer" resultMap="MemberPlusExpireTask">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_expire_task
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusExpireTask">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_expire_task
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_expire_task
    </select>
    <select id="getExpireTask"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusExpireTaskPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_expire_task
        where
        task_state in (1,4)
        and plan_time between DATE(NOW()) - INTERVAL 2 DAY and NOW()
        and retry_count &lt;= 4
        order by id
        limit 20
    </select>

</mapper>
