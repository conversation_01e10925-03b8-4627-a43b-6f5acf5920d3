<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusMqOrderStateMapper">

    <resultMap id="PlusMqOrderState" type="com.juzifenqi.plus.module.order.repository.po.PlusMqOrderStatePo">
        <result column="id" property="id"/>
        <result column="msg_id" property="msgId"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_channel" property="orderChannel"/>
        <result column="order_node" property="orderNode"/>
        <result column="order_status" property="orderStatus"/>
        <result column="mq_msg" property="mqMsg"/>
        <result column="opt_status" property="optStatus"/>
        <result column="remark" property="remark"/>
        <result column="num" property="num"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `msg_id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `order_channel`,
        `order_node`,
        `order_status`,
        `mq_msg`,
        `opt_status`,
        `remark`,
        `num`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusMqOrderStatePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_mq_order_state (
        `msg_id`,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `order_channel`,
        `order_node`,
        `order_status`,
        `mq_msg`,
        `opt_status`,
        <if test="remark != null">`remark`,</if>
        <if test="num != null">`num`,</if>
        `create_time`,
        `update_time`
        )
        VALUES(
        #{msgId},
        #{userId},
        #{channelId},
        #{orderSn},
        #{orderChannel},
        #{orderNode},
        #{orderStatus},
        #{mqMsg},
        #{optStatus},
        <if test="remark != null">#{remark},</if>
        <if test="num != null">#{num},</if>
        NOW(),
        NOW()
        )
    </insert>

    <update id="updateById"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusMqOrderStatePo">
        UPDATE plus_mq_order_state
        <set>
            <if test="record.optStatus != null">`opt_status`= #{record.optStatus},</if>
            <if test="record.remark != null">`remark`= #{record.remark},</if>
            <if test="record.num != null">`num`= #{record.num},</if>
            <if test="record.updateTime != null">`update_time`= #{record.updateTime},</if>
        </set>
        WHERE `id` = #{record.id} and `opt_status` not in (1,3)
    </update>

    <select id="selectById" parameterType="java.lang.Integer" resultMap="PlusMqOrderState">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_mq_order_state
        WHERE `id` = #{id}
    </select>

    <select id="selectInitList" parameterType="java.lang.Integer" resultMap="PlusMqOrderState">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_mq_order_state
        WHERE `opt_status` in (0,2) and num &lt; 3
        ORDER BY `create_time`
        LIMIT #{offset}, #{pagesize}
    </select>

    <update id="batchUpdateState">
        update plus_mq_order_state set opt_status = #{state},remark=#{remark},update_time = now()
        <where>
            <foreach collection="ids" item="id" separator="," open="id in (" close=")">
                ${id}
            </foreach>
        </where>
    </update>
</mapper>
