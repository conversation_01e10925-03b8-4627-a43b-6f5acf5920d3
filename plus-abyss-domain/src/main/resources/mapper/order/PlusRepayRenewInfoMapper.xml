<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRenewInfoMapper">

    <resultMap id="PlusRenewInfo" type="com.juzifenqi.plus.module.order.repository.po.PlusRenewInfoPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel" property="channel"/>
        <result column="program_id" property="programId"/>
        <result column="bank_id" property="bankId"/>
        <result column="money" property="money"/>
        <result column="order_sn" property="orderSn"/>
        <result column="state" property="state"/>
        <result column="renew_num" property="renewNum"/>
        <result column="cancel_renew_time" property="cancelRenewTime"/>
        <result column="config_id" property="configId"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel`,
        `program_id`,
        `bank_id`,
        `money`,
        `order_sn`,
        `state`,
        `renew_num`,
        `cancel_renew_time`,
        `config_id`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="getCondition">
        <where>
            <if test="queryParam.startTime != null and '' != queryParam.startTime">
                and create_time &gt;= #{queryParam.startTime}
            </if>
            <if test="queryParam.endTime != null and '' != queryParam.endTime">
                and create_time &lt;= #{queryParam.endTime}
            </if>
            <if test="queryParam.cancelRenewStartTime != null and '' != queryParam.cancelRenewStartTime">
                and cancel_renew_time &gt;= #{queryParam.cancelRenewStartTime}
            </if>
            <if test="queryParam.cancelRenewEndTime != null and '' != queryParam.cancelRenewEndTime">
                and cancel_renew_time &lt;= #{queryParam.cancelRenewEndTime}
            </if>

            <if test="queryParam.userId != null and queryParam.userId != ''">
                and `user_id` = #{queryParam.userId}
            </if>
            <if test="queryParam.configId != null and queryParam.configId != ''">
                and `config_id` = #{queryParam.configId}
            </if>
            <if test="queryParam.state != null">
                and `state` = #{queryParam.state}
            </if>
            <if test="queryParam.channelId != null and queryParam.channelId != ''">
                and `channel` = #{queryParam.channelId}
            </if>
        </where>
    </sql>

    <insert id="insertPlusRenewInfo"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRenewInfoPo">
        INSERT INTO plus_renew_info
        (`user_id`,
        `channel`,
        `program_id`,
        `bank_id`,
        `money`,
        `order_sn`,
        `state`,
        `renew_num`,
        `cancel_renew_time`,
        `config_id`,
        `create_time`,
        `update_time`)
        VALUES (#{plusRenewInfo.userId},
        #{plusRenewInfo.channel},
        #{plusRenewInfo.programId},
        #{plusRenewInfo.bankId},
        #{plusRenewInfo.money},
        #{plusRenewInfo.orderSn},
        #{plusRenewInfo.state},
        #{plusRenewInfo.renewNum},
        #{plusRenewInfo.cancelRenewTime},
        #{plusRenewInfo.configId},
        NOW(),
        #{plusRenewInfo.updateTime})
    </insert>


    <delete id="deletePlusRenewInfo" parameterType="java.lang.Integer">
        DELETE
        FROM plus_renew_info
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusRenewInfo"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRenewInfoPo">
        UPDATE plus_renew_info
        SET
        <if test="plusRenewInfo.userId != null">`user_id`= #{plusRenewInfo.userId},</if>
        <if test="plusRenewInfo.programId != null">`program_id`= #{plusRenewInfo.programId},</if>
        <if test="plusRenewInfo.bankId != null">`bank_id`= #{plusRenewInfo.bankId},</if>
        <if test="plusRenewInfo.money != null">`money`= #{plusRenewInfo.money},</if>
        <if test="plusRenewInfo.orderSn != null">`order_sn`= #{plusRenewInfo.orderSn},</if>
        <if test="plusRenewInfo.state != null">`state`= #{plusRenewInfo.state},</if>
        <if test="plusRenewInfo.renewNum != null">`renew_num`= #{plusRenewInfo.renewNum},</if>
        <if test="plusRenewInfo.configId != null">`config_id`= #{plusRenewInfo.configId},</if>
        cancel_renew_time = now(),
        update_time = now()
        WHERE `id` = #{plusRenewInfo.id}
    </update>

    <update id="updateRenewByOrderId">
        UPDATE plus_renew_info
        SET
        <if test="plusRenewInfo.state != null">`state`= #{plusRenewInfo.state},</if>
        <if test="plusRenewInfo.cancelRenewTime != null">`cancel_renew_time`=
            #{plusRenewInfo.cancelRenewTime},
        </if>
        update_time = now()
        WHERE `order_sn` = #{plusRenewInfo.orderSn} and `user_id`= #{plusRenewInfo.userId}
    </update>


    <update id="updateRenewById">
        UPDATE plus_renew_info
        SET
        <if test="plusRenewInfo.state != null">`state`= #{plusRenewInfo.state},</if>
        <if test="plusRenewInfo.cancelRenewTime != null">`cancel_renew_time`=
            #{plusRenewInfo.cancelRenewTime},
        </if>
        update_time = now()
        WHERE id = #{plusRenewInfo.id}
    </update>

    <update id="updateRenewAll">
        UPDATE plus_renew_info
        SET
        <if test="plusRenewInfo.state != null">`state`= #{plusRenewInfo.state},</if>
        update_time = now()
        WHERE `user_id`= #{plusRenewInfo.userId} and config_id=#{plusRenewInfo.configId}
        and order_sn != #{plusRenewInfo.orderSn}
    </update>

    <update id="updateRenewAllByConfig">
        update plus_renew_info
        set `state` = #{state}, update_time = now()
        where `user_id` = #{userId}
        and config_id = #{configId}
    </update>

    <select id="loadPlusRenewInfo" parameterType="java.lang.Integer" resultMap="PlusRenewInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_renew_info
        WHERE `id` = #{id}
    </select>

    <select id="getPlusRenew" resultType="java.lang.Integer">
        select count(1)
        from plus_renew_info
        where `user_id` = #{plusRenewInfo.userId} and order_sn = #{plusRenewInfo.orderSn}
        <if test="plusRenewInfo.state != null and plusRenewInfo.state != ''">
            and `state` = #{plusRenewInfo.state}
        </if>
    </select>


    <select id="getUserEffectiveRenew" resultMap="PlusRenewInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_renew_info
        where `user_id` = #{userId}
          and `channel` = #{channel}
          and `config_id` = #{configId}
          and `state` = 1
    </select>

    <select id="getRenewByOrder" resultMap="PlusRenewInfo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_renew_info
        where `user_id` = #{userId} and `order_sn` = #{orderSn}
    </select>

    <select id="getPlusRenewCount" resultType="java.lang.Boolean">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_renew_info
        where `user_id` = #{userId}
        and `channel` = #{channel}
        and `config_id` = #{configId}
        and `state` = 1
    </select>

    <update id="updateRenewNum" parameterType="java.lang.Integer">
        UPDATE plus_renew_info
        SET renew_num = renew_num + 1,
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusRenewInfo">
        select
        <include refid="Base_Column_List"/>
        from `plus_renew_info`
        <include refid="getCondition"/>
        order by `id` desc
        limit #{queryParam.startPage}, #{queryParam.pageSize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from plus_renew_info
        <include refid="getCondition"/>
    </select>
</mapper>