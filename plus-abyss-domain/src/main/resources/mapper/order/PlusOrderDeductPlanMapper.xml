<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderDeductPlanMapper">

    <resultMap id="PlusOrderDeductPlan"
            type="com.juzifenqi.plus.module.order.repository.po.PlusOrderDeductPlanPo">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="opt_status" property="optStatus"/>
        <result column="plan_time" property="planTime"/>
        <result column="msg_plan_time" property="msgPlanTime"/>
        <result column="deduct_num" property="deductNum"/>
        <result column="deduct_suc_time" property="deductSucTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `order_sn`,
        `plus_order_sn`,
        `user_id`,
        `channel_id`,
        `config_id`,
        `opt_status`,
        `plan_time`,
        `msg_plan_time`,
        `deduct_num`,
        `deduct_suc_time`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusOrderDeductPlan"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderDeductPlanPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_deduct_plan (`order_sn`,
                                            `plus_order_sn`,
                                            `user_id`,
                                            `channel_id`,
                                            `config_id`,
                                            `opt_status`,
                                            `msg_status`,
                                            `plan_time`,
                                            `msg_plan_time`,
                                            `deduct_num`,
                                            `deduct_suc_time`,
                                            `remark`,
                                            `create_time`,
                                            `update_time`)
        VALUES (#{orderSn},
                #{plusOrderSn},
                #{userId},
                #{channelId},
                #{configId},
                #{optStatus},
                #{msgStatus},
                #{planTime},
                #{msgPlanTime},
                #{deductNum},
                #{deductSucTime},
                #{remark},
                NOW(),
                #{updateTime})
    </insert>


    <update id="updatePlusOrderDeductPlan"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderDeductPlanPo">
        UPDATE plus_order_deduct_plan
        SET
        <if test="plusOrderDeductPlan.userId != null">`user_id`= #{plusOrderDeductPlan.userId},</if>
        <if test="plusOrderDeductPlan.channelId != null">`channel_id`=
            #{plusOrderDeductPlan.channelId},
        </if>
        <if test="plusOrderDeductPlan.optStatus != null">`opt_status`=
            #{plusOrderDeductPlan.optStatus},
        </if>
        <if test="plusOrderDeductPlan.planTime != null">`plan_time`=
            #{plusOrderDeductPlan.planTime},
        </if>
        <if test="plusOrderDeductPlan.msgPlanTime != null">`msg_plan_time`=
            #{plusOrderDeductPlan.msgPlanTime},
        </if>
        <if test="plusOrderDeductPlan.msgStatus != null">`msg_status`=
            #{plusOrderDeductPlan.msgStatus},
        </if>
        <if test="plusOrderDeductPlan.deductNum != null">`deduct_num`=
            #{plusOrderDeductPlan.deductNum},
        </if>
        <if test="plusOrderDeductPlan.deductSucTime != null">`deduct_suc_time`=
            #{plusOrderDeductPlan.deductSucTime},
        </if>
        update_time = now()
        WHERE `order_sn` = #{plusOrderDeductPlan.orderSn} and config_id = 10
    </update>

    <update id="updatePlusOrderDeductPlanCancel">
        UPDATE plus_order_deduct_plan
        SET
        <if test="plusOrderDeductPlan.optStatus != null">`opt_status`=
            #{plusOrderDeductPlan.optStatus},
        </if>
        <if test="plusOrderDeductPlan.optStatus != null and plusOrderDeductPlan.optStatus == 2">
            `deduct_suc_time`= now(),
        </if>
        deduct_num = deduct_num + 1,
        update_time = now()
        WHERE `plus_order_sn` = #{plusOrderDeductPlan.plusOrderSn} and opt_status in (0,1,3)
    </update>


    <update id="updatePlusOrderDeductPlanState">
        UPDATE plus_order_deduct_plan
        SET  `opt_status`= #{optStatus},
        update_time = now()
        WHERE `plus_order_sn` = #{plusOrderSn}
    </update>


    <update id="updateDeductWaitHandle">
        UPDATE plus_order_deduct_plan
        SET `opt_status`= 0,
            plan_time   = #{plusOrderDeductPlan.planTime},
            update_time = now()
        WHERE `plus_order_sn` = #{plusOrderDeductPlan.plusOrderSn}
          and opt_status = 3
          and deduct_num &lt; 6
    </update>

    <update id="updateToDeduct">
        UPDATE plus_order_deduct_plan
        SET `opt_status`= 0,
            plan_time   = #{plusOrderDeductPlan.planTime},
            update_time = now(),
            deduct_num = #{plusOrderDeductPlan.deductNum},
            remark = ''
        WHERE id = #{plusOrderDeductPlan.id}
          and opt_status = 3
          and deduct_num > 5;
    </update>

    <update id="deductSmsDone">
        UPDATE plus_order_deduct_plan
        SET `msg_status`= 1,
        update_time = now()
        WHERE `id`  = #{id}
        and msg_status = 0
    </update>


    <select id="loadPlusOrderDeductPlan" parameterType="java.lang.Integer"
            resultMap="PlusOrderDeductPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_deduct_plan
        WHERE `id` = #{id}
    </select>

    <select id="getRepayDayDeductPlanCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_order_deduct_plan
        where opt_status in (0, 3)
          and config_id = 10
          and plan_time BETWEEN CONCAT(CURDATE(), ' 00:00:00') AND CONCAT(CURDATE(), ' 23:59:59')
    </select>


    <select id="getRepayDayDeductPlan" parameterType="java.lang.Integer"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderDeductPlanPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_deduct_plan
        where
        <if test="maxId != 0">
            id &gt; #{maxId} and
        </if>
        opt_status in(0,3) and config_id = 10
        and plan_time BETWEEN CONCAT(CURDATE(),' 00:00:00') AND CONCAT(CURDATE(),' 23:59:59')
        order by id
        limit 2000
    </select>


    <select id="getOverdueDeductPlanCount" resultType="java.lang.Integer">
        SELECT count(*)
        FROM plus_order_deduct_plan
        where opt_status = 3
          and config_id = 10
          and plan_time <![CDATA[ < ]]> CONCAT(CURDATE(), ' 00:00:00')
    </select>

    <select id="getOverdueDeductPlan" parameterType="java.lang.Integer"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderDeductPlanPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_deduct_plan
        where
        <if test="maxId != 0">
            id &gt; #{maxId} and
        </if>
        opt_status = 3 and config_id = 10
        and plan_time <![CDATA[ < ]]> CONCAT(CURDATE(),' 00:00:00')
        order by id
        limit 2000
    </select>

    <select id="getInfoByPlusOrderSn" parameterType="java.lang.String"
            resultMap="PlusOrderDeductPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_deduct_plan
        where plus_order_sn =#{plusOrderSn} limit 1
    </select>

    <select id="getDelayRepayDayDeductPlan" parameterType="java.util.Date"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderDeductPlanPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_deduct_plan
        where opt_status =0 and config_id != 10 and plan_time between #{limitTime} and NOW()
        and deduct_num &lt; 6
        order by id
        limit 50
    </select>


    <select id="getDelayMsgDeductPlan" parameterType="java.util.Date"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderDeductPlanPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_deduct_plan
        where  msg_status =0 and config_id != 10 and msg_plan_time between
        #{limitTime} and NOW()
        order by id
        limit 50
    </select>


    <update id="invalidDeductPlan">
        UPDATE plus_order_deduct_plan
        SET `opt_status`= 4,update_time = now()
        WHERE `plus_order_sn` = #{plusOrderSn} and opt_status in (0,1,3)
    </update>

    <update id="invalidMsgPlan">
        UPDATE plus_order_deduct_plan
        SET `msg_status`= 4,update_time = now()
        WHERE `plus_order_sn` = #{plusOrderSn} and msg_status = 0
    </update>

    <select id="getDeductError" resultType="integer">
        select config_id
        from plus_order_deduct_plan
        where opt_status = 3
          and plan_time > DATE_SUB(NOW(), INTERVAL #{beforeMinutes} MINUTE)
    </select>

</mapper>
