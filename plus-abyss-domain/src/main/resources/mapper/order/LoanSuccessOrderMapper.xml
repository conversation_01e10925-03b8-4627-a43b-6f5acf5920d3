<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.ILoanSuccessOrderMapper">

    <resultMap id="LoanSuccessOrder"
            type="com.juzifenqi.plus.module.order.repository.po.LoanSuccessOrderPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="order_create_time" property="orderCreateTime"/>
        <result column="loan_time" property="loanTime"/>
        <result column="wait_time" property="waitTime"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `order_sn`,
        `order_amount`,
        `order_create_time`,
        `loan_time`,
        `wait_time`,
        `remark`,
        `create_time`
    </sql>

    <select id="getPayRecordByWaitTime" resultMap="LoanSuccessOrder">
        SELECT
        <include refid="Base_Column_List"/>
        FROM loan_success_order
        order by create_time desc limit 50
    </select>

</mapper>
