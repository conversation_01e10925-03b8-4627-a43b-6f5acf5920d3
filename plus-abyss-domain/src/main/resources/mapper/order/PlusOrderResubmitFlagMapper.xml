<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderResubmitFlagMapper">

  <resultMap id="PlusOrderResubmitFlag"
    type="com.juzifenqi.plus.module.order.repository.po.PlusOrderResubmitFlagPo">
    <result column="id" property="id"/>
    <result column="user_id" property="userId"/>
    <result column="channel_id" property="channelId"/>
    <result column="order_sn" property="orderSn"/>
    <result column="config_id" property="configId"/>
    <result column="verify_status" property="verifyStatus"/>
    <result column="verify_result" property="verifyResult"/>
    <result column="retry_count" property="retryCount"/>
    <result column="resubmit_flag" property="resubmitFlag"/>
    <result column="create_time" property="createTime"/>
    <result column="update_time" property="updateTime"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`
    ,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `config_id`,
        `verify_status`,
        `verify_result`,
        `retry_count`,
        `resubmit_flag`,
        `create_time`,
        `update_time`
  </sql>

  <insert id="savePlusOrderResubmitFlag"
    parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderResubmitFlagPo"
    useGeneratedKeys="true" keyProperty="id">
    INSERT INTO plus_order_resubmit_flag (`user_id`,
                                          `channel_id`,
                                          `order_sn`,
                                          `config_id`,
                                          `create_time`)
    VALUES (#{userId},
            #{channelId},
            #{orderSn},
            #{configId},
            NOW())
  </insert>
    <update id="batchProcessing">
        UPDATE plus_order_resubmit_flag
        SET `verify_status`= 1, update_time = now()
        WHERE `id` IN (
        <foreach collection="ids" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>
    <update id="updatePlusOrderResubmitFlag">
        UPDATE plus_order_resubmit_flag
        SET
        <if test="plusOrderResubmitFlag.verifyStatus != null">`verify_status`=
            #{plusOrderResubmitFlag.verifyStatus},
        </if>
        <if test="plusOrderResubmitFlag.verifyResult != null">`verify_result`=
            #{plusOrderResubmitFlag.verifyResult},
        </if>
        <if test="plusOrderResubmitFlag.retryCount != null">`retry_count`=
            #{plusOrderResubmitFlag.retryCount},
        </if>
        <if test="plusOrderResubmitFlag.resubmitFlag != null">`resubmit_flag`=
            #{plusOrderResubmitFlag.resubmitFlag},
        </if>
        update_time = now()
        WHERE `id` = #{plusOrderResubmitFlag.id}
    </update>
    <select id="getToBeVerifyList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusOrderResubmitFlagPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_resubmit_flag
        where verify_status in (0,3) and retry_count &lt; 2
        order by id
        LIMIT 0,60
    </select>

</mapper>
