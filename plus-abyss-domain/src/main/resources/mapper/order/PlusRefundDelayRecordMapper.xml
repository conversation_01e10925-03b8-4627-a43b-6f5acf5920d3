<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusRefundDelayRecordMapper">
    <resultMap id="PlusRefundDelayRecord"
            type="com.juzifenqi.plus.module.order.repository.po.PlusRefundDelayRecordPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="config_id" property="configId"/>
        <result column="order_status" property="orderStatus"/>
        <result column="plan_time" property="planTime"/>
        <result column="start_time" property="startTime"/>
        <result column="delay_time" property="delayTime"/>
        <result column="opt_status" property="optStatus"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel_id`,
        `order_sn`,
        `plus_order_sn`,
        `config_id`,
        `order_status`,
        `plan_time`,
        `start_time`,
        `delay_time`,
        `opt_status`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>


    <insert id="savePlusRefundDelayRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundDelayRecordPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_refund_delay_record
        (`user_id`,
         `channel_id`,
         `order_sn`,
         `plus_order_sn`,
         `config_id`,
         `order_status`,
         `plan_time`,
         `start_time`,
         `delay_time`,
         `opt_status`,
         `remark`,
         `create_time`,
         `update_time`)
        VALUES (#{userId},
                #{channelId},
                #{orderSn},
                #{plusOrderSn},
                #{configId},
                #{orderStatus},
                #{planTime},
                #{startTime},
                #{delayTime},
                #{optStatus},
                #{remark},
                NOW(),
                NOW())
    </insert>

    <select id="getRefundList"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRefundDelayRecordPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_refund_delay_record
        where `opt_status` = 0
        and plan_time <![CDATA[ <=  ]]> now()
        LIMIT #{size}
    </select>

    <update id="updatePlusRefundDelayRecord"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusRefundDelayRecordPo">
        UPDATE plus_refund_delay_record
        SET `opt_status` = 1,
            update_time  = now()
        WHERE `id` = #{id}
    </update>

    <select id="getByPlusOrderSn"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusRefundDelayRecordPo">
        select
        <include refid="Base_Column_List"/>
        from plus_refund_delay_record
        where plus_order_sn = #{orderSn}
    </select>

    <update id="batchUpdateState">
        UPDATE plus_refund_delay_record
        SET `opt_status` = #{optState},
        update_time  = now()
        WHERE `id` in
        <foreach collection="ids" item="id" open="(" close=")" separator=",">
            #{id}
        </foreach>
    </update>

</mapper>
