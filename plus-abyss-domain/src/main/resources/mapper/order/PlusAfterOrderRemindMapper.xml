<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusAfterOrderRemindMapper">

    <resultMap id="PlusAfterOrderRemind"
            type="com.juzifenqi.plus.module.order.repository.po.PlusAfterOrderRemindPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="state" property="state"/>
        <result column="plan_time" property="planTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `config_id`,
        `order_sn`,
        `state`,
        `plan_time`,
        `create_time`,
        `update_time`
    </sql>


    <insert id="insertPlusAfterOrderRemind"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusAfterOrderRemindPo">
        INSERT INTO plus_after_order_remind (
                `member_id`,
                `order_sn`,
                `config_id`,
                `state`,
                `plan_time`,
                `create_time`,
                `update_time`
              )
        VALUES(
            #{plusAfterOrderRemind.memberId},
            #{plusAfterOrderRemind.orderSn},
            #{plusAfterOrderRemind.configId},
            #{plusAfterOrderRemind.state},
            #{plusAfterOrderRemind.planTime},
            NOW(),
            now()
        )
    </insert>

    <update id="updatePlusAfterOrderRemindByOrderSn"
            parameterType="com.juzifenqi.plus.module.order.repository.po.PlusAfterOrderRemindPo">
        UPDATE plus_after_order_remind
        SET
        <if test="plusAfterOrderRemind.state != null">`state`= #{plusAfterOrderRemind.state},</if>
        update_time = now()
        WHERE   `state`= 1
        <if test="plusAfterOrderRemind.orderSn != null">
          and    `order_sn` = #{plusAfterOrderRemind.orderSn}
        </if>
        <if test="plusAfterOrderRemind.id != null">
        and  id=#{plusAfterOrderRemind.id}
        </if>
    </update>

    <select id="getRemindList"
            resultMap="PlusAfterOrderRemind">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_after_order_remind
        WHERE date_format(plan_time,'%y%m%d') =date_format( now(),'%y%m%d')
        and config_id != 12
        and `state`= 1
        limit 300
    </select>
    <select id="getRemindListByConfigId"
            resultType="com.juzifenqi.plus.module.order.repository.po.PlusAfterOrderRemindPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_after_order_remind
        WHERE plan_time &lt;= now()
        AND config_id = #{configId}
        AND `state`= 1
        LIMIT 200
    </select>


</mapper>
