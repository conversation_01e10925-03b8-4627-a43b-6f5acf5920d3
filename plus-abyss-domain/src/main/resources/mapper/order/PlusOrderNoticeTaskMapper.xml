<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.order.repository.dao.IPlusOrderNoticeTaskMapper">

    <resultMap id="PlusOrderNoticeTask" type="com.juzifenqi.plus.module.order.repository.po.PlusOrderNoticeTaskPo" >
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="notice_type" property="noticeType" />
        <result column="notice_state" property="noticeState" />
        <result column="retry_count" property="retryCount" />
        <result column="callback_url" property="callbackUrl" />
        <result column="bus_param" property="busParam"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `user_id`,
        `channel_id`,
        `notice_type`,
        `notice_state`,
        `retry_count`,
        `callback_url`,
        `bus_param`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="save" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderNoticeTaskPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_notice_task (
        `order_sn`,
        `user_id`,
        `channel_id`,
        `notice_type`,
        `notice_state`,
        `retry_count`,
        `callback_url`,
        `bus_param`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{orderSn},
        #{userId},
        #{channelId},
        #{noticeType},
        #{noticeState},
        #{retryCount},
        #{callbackUrl},
        #{busParam},
        NOW(),
        #{updateTime}
        )
    </insert>

    <insert id="batchSave" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderNoticeTaskPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_notice_task (
        `order_sn`,
        `user_id`,
        `channel_id`,
        `notice_type`,
        `notice_state`,
        `retry_count`,
        `callback_url`,
        `bus_param`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="l" separator=",">
            (
            #{l.orderSn},
            #{l.userId},
            #{l.channelId},
            #{l.noticeType},
            #{l.noticeState},
            #{l.retryCount},
            #{l.callbackUrl},
            #{l.busParam},
            NOW(),
            #{l.updateTime}
            )
        </foreach>
    </insert>

    <update id="batchUpdate" parameterType="com.juzifenqi.plus.module.order.repository.po.PlusOrderNoticeTaskPo" >
        UPDATE plus_order_notice_task
        <trim prefix="set" suffixOverrides=",">
            <trim prefix="notice_state =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.noticeState!=null">
                        when id = #{i.id} then #{i.noticeState}
                    </if>
                </foreach>
            </trim>
            <trim prefix="retry_count =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    <if test="i.retryCount!=null">
                        when id = #{i.id} then #{i.retryCount}
                    </if>
                </foreach>
            </trim>
            <trim prefix="update_time =case" suffix="end,">
                <foreach collection="list" item="i" index="index">
                    when id = #{i.id} then now()
                </foreach>
            </trim>
        </trim>
        where id in
        <foreach collection="list" separator="," item="i" index="index" open="(" close=")">
            #{i.id}
        </foreach>
    </update>

    <update id="batchUpdateState" >
        UPDATE plus_order_notice_task set notice_state = #{noticeState}, update_time = now()
        where id in
        <foreach collection="ids" separator="," item="id" index="index" open="(" close=")">
            #{id}
        </foreach>
    </update>

    <select id="selectJobList" resultMap="PlusOrderNoticeTask">
        select <include refid="Base_Column_List"/> from plus_order_notice_task
        where notice_state = #{noticeState} and notice_type = #{noticeType} and channel_id = #{channelId}
        and retry_count &lt; 3
        order by id limit #{limit}
    </select>
</mapper>
