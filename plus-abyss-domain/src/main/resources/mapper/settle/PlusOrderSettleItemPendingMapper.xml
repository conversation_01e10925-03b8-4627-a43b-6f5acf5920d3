<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.settle.repository.dao.IPlusOrderSettleItemPendingMapper">
    <resultMap id="PlusOrderSettleItemPending"
            type="com.juzifenqi.plus.module.settle.repository.po.PlusOrderSettleItemPendingPo">
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="ori_apply_serial_no" property="oriApplySerialNo" />
        <result column="refund_success_time" property="refundSuccessTime" />
        <result column="settle_bill_id" property="settleBillId" />
        <result column="shunt_supplier_id" property="shuntSupplierId" />
        <result column="separate_supplier_id" property="separateSupplierId" />
        <result column="separate_type" property="separateType" />
        <result column="refund_amount" property="refundAmount" />
        <result column="settle_amount" property="settleAmount" />
        <result column="is_create_settle" property="isCreateSettle" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="channel_id" property="channelId" />
    </resultMap>

    <sql id="getCondition">
        <where>
            <if test="req.shuntSupplierId != null">
                and p.shunt_supplier_id = #{req.shuntSupplierId}
            </if>
            <if test="req.separateSupplierId != null">
                and p.separate_supplier_id = #{req.separateSupplierId}
            </if>
            <if test="req.orderSn != null and req.orderSn != ''">
                and p.order_sn = #{req.orderSn}
            </if>
            <if test="req.settleStartTime != null and '' != req.settleStartTime">
                and b.pay_callback_time &gt;= #{req.settleStartTime}
            </if>
            <if test="req.settleEndTime != null and '' != req.settleEndTime">
                and b.pay_callback_time &lt;= #{req.settleEndTime}
            </if>
        </where>
    </sql>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `ori_apply_serial_no`,
        `refund_success_time`,
        `settle_bill_id`,
        `shunt_supplier_id`,
        `separate_supplier_id`,
        `separate_type`,
        `refund_amount`,
        `settle_amount`,
        `is_create_settle`,
        `remark`,
        `create_time`,
        `update_time`,
        `channel_id`
    </sql>

    <insert id="batchInsert"
            parameterType="com.juzifenqi.plus.module.settle.repository.po.PlusOrderSettleItemPendingPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_order_settle_item_pending (
        `order_sn`,
        `ori_apply_serial_no`,
        `refund_success_time`,
        `settle_bill_id`,
        `shunt_supplier_id`,
        `separate_supplier_id`,
        `separate_type`,
        `refund_amount`,
        `settle_amount`,
        `is_create_settle`,
        `remark`,
        `create_time`,
        `update_time`,
        `channel_id`
        )
        VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.orderSn},
            #{item.oriApplySerialNo},
            #{item.refundSuccessTime},
            #{item.settleBillId},
            #{item.shuntSupplierId},
            #{item.separateSupplierId},
            #{item.separateType},
            #{item.refundAmount},
            #{item.settleAmount},
            #{item.isCreateSettle},
            #{item.remark},
            NOW(),
            #{item.updateTime},
            #{item.channelId}
            )
        </foreach>
    </insert>

    <select id="getSettlePendingSupplierByDate"
            resultType="com.juzifenqi.plus.module.settle.repository.po.PlusOrderSettleItemPendingPo">
        SELECT DISTINCT shunt_supplier_id, separate_supplier_id, channel_id
        FROM plus_order_settle_item_pending
        WHERE refund_success_time &gt;= #{beginTime}
          AND refund_success_time &lt;= #{endTime}
          AND is_create_settle = #{isCreateSettle}
    </select>

    <select id="getSettlePendingItemsByOrderSn"
            resultType="com.juzifenqi.plus.module.settle.repository.po.PlusOrderSettleItemPendingPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_order_settle_item_pending
        WHERE order_sn = #{orderSn}
    </select>

    <select id="getSettlePendingByDate"
            resultType="com.juzifenqi.plus.module.settle.repository.po.PlusOrderSettleItemPendingPo">
        SELECT id, shunt_supplier_id, separate_supplier_id, settle_amount,channel_id
        FROM plus_order_settle_item_pending
        WHERE refund_success_time &gt;= #{beginTime}
          AND refund_success_time &lt;= #{endTime}
          AND is_create_settle = #{isCreateSettle}
          AND shunt_supplier_id = #{shuntSupplierId}
          AND separate_supplier_id = #{separateSupplierId}
        <if test="channelId != null">
            AND channel_id = #{channelId}
        </if>
    </select>

    <update id="updateSettleItemPendingByIds" parameterType="map">
        UPDATE plus_order_settle_item_pending
        SET settle_bill_id = #{settleBillId},
            is_create_settle = #{isCreateSettle},
            update_time = now()
        WHERE id IN (
        <foreach collection="ids" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
          AND is_create_settle = #{allowCreateSettle}
    </update>

    <resultMap id="PlusOrderSettleItemPendingAdmin"
            type="com.juzifenqi.plus.module.settle.model.contract.entity.PlusOrderSettleItemPendingAdminEntity">
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="refund_success_time" property="refundSuccessTime" />
        <result column="shunt_supplier_id" property="shuntSupplierId" />
        <result column="separate_supplier_id" property="separateSupplierId" />
        <result column="separate_type" property="separateType" />
        <result column="refund_amount" property="refundAmount" />
        <result column="settle_amount" property="settleAmount" />
        <result column="settle_state" property="settleState" />
        <result column="pay_callback_time" property="payCallbackTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <select id="getPendingPageList" parameterType="java.util.Map" resultMap="PlusOrderSettleItemPendingAdmin">
        select
            p.id,p.order_sn,p.refund_success_time,p.shunt_supplier_id,p.separate_supplier_id,
            p.separate_type,p.refund_amount,p.settle_amount,b.pay_callback_time,
            case when is_create_settle = 1 then 1 else b.settle_state END as settle_state
         from plus_order_settle_item_pending p
         left join settle_bill b on b.id = p.settle_bill_id
        <include refid="getCondition"/>
        <!-- 结算时间排序很特殊，有结算时间的要倒序，但是空的要排在前面，所以需要特殊写法 -->
        order by -pay_callback_time asc,p.create_time asc
        limit #{req.startPage}, #{req.pageSize}
    </select>

    <select id="pendingPageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from plus_order_settle_item_pending p
        left join settle_bill b on b.id = p.settle_bill_id
        <include refid="getCondition"/>
    </select>
</mapper>
