<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.settle.repository.dao.ISettleBillMapper">
    <resultMap id="SettleBill" type="com.juzifenqi.plus.module.settle.repository.po.SettleBillPo" >
        <result column="id" property="id" />
        <result column="settle_pending_date" property="settlePendingDate" />
        <result column="settle_bill_no" property="settleBillNo" />
        <result column="shunt_supplier_id" property="shuntSupplierId" />
        <result column="separate_supplier_id" property="separateSupplierId" />
        <result column="settle_amount" property="settleAmount" />
        <result column="apply_settle_time" property="applySettleTime" />
        <result column="settle_state" property="settleState" />
        <result column="pay_callback_time" property="payCallbackTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="channel_id" property="channelId"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `settle_pending_date`,
        `settle_bill_no`,
        `shunt_supplier_id`,
        `separate_supplier_id`,
        `settle_amount`,
        `apply_settle_time`,
        `settle_state`,
        `pay_callback_time`,
        `remark`,
        `create_time`,
        `update_time`,
        `channel_id`
    </sql>

    <sql id="getCondition">
        <where>
            <if test="req.settleStartTime != null and '' != req.settleStartTime">
                and pay_callback_time &gt;= #{req.settleStartTime}
            </if>
            <if test="req.settleEndTime != null and '' != req.settleEndTime">
                and pay_callback_time &lt;= #{req.settleEndTime}
            </if>
        </where>
    </sql>

    <insert id="saveSettleBill" parameterType="com.juzifenqi.plus.module.settle.repository.po.SettleBillPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO settle_bill (`settle_pending_date`,
                                 `settle_bill_no`,
                                 `shunt_supplier_id`,
                                 `separate_supplier_id`,
                                 `settle_amount`,
                                 `apply_settle_time`,
                                 `settle_state`,
                                 `pay_callback_time`,
                                 `remark`,
                                 `create_time`,
                                 `update_time`,
                                 `channel_id`)
        VALUES (#{settlePendingDate},
                #{settleBillNo},
                #{shuntSupplierId},
                #{separateSupplierId},
                #{settleAmount},
                #{applySettleTime},
                #{settleState},
                #{payCallbackTime},
                #{remark},
                NOW(),
                #{updateTime},
                #{channelId})
    </insert>

    <select id="getWaitDefraySettleBillEntities" resultType="com.juzifenqi.plus.module.settle.repository.po.SettleBillPo">
        SELECT
        <include refid="Base_Column_List"/>
          FROM settle_bill
        WHERE settle_pending_date = str_to_date(#{date},'%Y-%m-%d')
          AND settle_state = #{settleState}
    </select>

    <update id="batchProcessing" parameterType="map">
        UPDATE settle_bill
        SET `settle_state` = #{settleState},
            `update_time` = now()
        WHERE `id` IN (
        <foreach collection="ids" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
        AND settle_state = #{allSettleState}
    </update>

    <update id="updateSettleBillById" parameterType="com.juzifenqi.plus.module.settle.repository.po.SettleBillPo" >
        UPDATE settle_bill
        SET
        <if test="applySettleTime != null">
            `apply_settle_time`= #{applySettleTime},
        </if>
        <if test="settleState != null">
            `settle_state`= #{settleState},
        </if>
        <if test="payCallbackTime != null">
            `pay_callback_time`= #{payCallbackTime},
        </if>
        <if test="remark != null">
            `remark`= #{remark},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="getSettleBillById" resultType="com.juzifenqi.plus.module.settle.repository.po.SettleBillPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM settle_bill
        WHERE `id` = #{id}
    </select>

    <select id="getSettleBillPageList" parameterType="map" resultMap="SettleBill">
        select
        <include refid="Base_Column_List"/>
        from settle_bill
        <include refid="getCondition"/>
        order by -pay_callback_time asc,create_time asc
        limit #{req.startPage}, #{req.pageSize}
    </select>

    <select id="settleBillPageListCount" parameterType="map" resultType="Integer">
        select count(1)
        from settle_bill
        <include refid="getCondition"/>
    </select>
</mapper>
