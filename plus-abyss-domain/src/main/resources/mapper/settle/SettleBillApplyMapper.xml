<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.settle.repository.dao.ISettleBillApplyMapper">
    <resultMap id="SettleBillApply" type="com.juzifenqi.plus.module.settle.repository.po.SettleBillApplyPo" >
        <result column="id" property="id" />
        <result column="settle_bill_id" property="settleBillId" />
        <result column="apply_serial_no" property="applySerialNo" />
        <result column="business_scene" property="businessScene" />
        <result column="bank_account_no" property="bankAccountNo" />
        <result column="bank_account_no_uuid" property="bankAccountNoUuid" />
        <result column="bank_account_name" property="bankAccountName" />
        <result column="bank_account_type" property="bankAccountType" />
        <result column="bank_branch_no" property="bankBranchNo" />
        <result column="bank_name" property="bankName" />
        <result column="bank_province_name" property="bankProvinceName" />
        <result column="bank_province_code" property="bankProvinceCode" />
        <result column="bank_city_code" property="bankCityCode" />
        <result column="bank_city_name" property="bankCityName" />
        <result column="settle_state" property="settleState" />
        <result column="pay_serial_Number" property="paySerialNumber" />
        <result column="pay_callback_time" property="payCallbackTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `settle_bill_id`,
        `apply_serial_no`,
        `business_scene`,
        `bank_account_no`,
        `bank_account_no_uuid`,
        `bank_account_name`,
        `bank_account_type`,
        `bank_branch_no`,
        `bank_name`,
        `bank_province_name`,
        `bank_province_code`,
        `bank_city_code`,
        `bank_city_name`,
        `settle_state`,
        `pay_serial_Number`,
        `pay_callback_time`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveSettleBillApply" parameterType="com.juzifenqi.plus.module.settle.repository.po.SettleBillApplyPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO settle_bill_apply (
            `settle_bill_id`,
            `apply_serial_no`,
            `business_scene`,
            `bank_account_no`,
            `bank_account_no_uuid`,
            `bank_account_name`,
            `bank_account_type`,
            `bank_branch_no`,
            `bank_name`,
            `bank_province_name`,
            `bank_province_code`,
            `bank_city_code`,
            `bank_city_name`,
            `settle_state`,
            `pay_serial_Number`,
            `pay_callback_time`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{settleBillId},
                  #{applySerialNo},
                  #{businessScene},
                  #{bankAccountNo},
                  #{bankAccountNoUuid},
                  #{bankAccountName},
                  #{bankAccountType},
                  #{bankBranchNo},
                  #{bankName},
                  #{bankProvinceName},
                  #{bankProvinceCode},
                  #{bankCityCode},
                  #{bankCityName},
                  #{settleState},
                  #{paySerialNumber},
                  #{payCallbackTime},
                  #{remark},
                  NOW(),
                  #{updateTime}
              )
    </insert>

    <update id="updateSettleBillApplyById" parameterType="com.juzifenqi.plus.module.settle.repository.po.SettleBillApplyPo" >
        UPDATE settle_bill_apply
        SET
        <if test="paySerialNumber != null">
            `pay_serial_Number`= #{paySerialNumber},
        </if>
        <if test="settleState != null">
            `settle_state`= #{settleState},
        </if>
        <if test="payCallbackTime != null">
            `pay_callback_time`= #{payCallbackTime},
        </if>
        <if test="remark != null">
            `remark`= #{remark},
        </if>
        update_time = now()
        WHERE `id` = #{id}
    </update>
    <select id="getSettleBillApplyByApplySerialNo" resultType="com.juzifenqi.plus.module.settle.repository.po.SettleBillApplyPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM settle_bill_apply
        WHERE apply_serial_no = #{applySerialNo}
    </select>
    <select id="getWithEmptyBankAccountNoUuid" resultType="com.juzifenqi.plus.module.settle.repository.po.SettleBillApplyPo">
        select id, bank_account_no
        from settle_bill_apply
        where bank_account_no is not null
          and bank_account_no_uuid is null
          and id > #{lastId}
        ORDER BY id ASC
            limit #{size}
    </select>
    <select id="getWithBankAccountNoUuid" resultType="com.juzifenqi.plus.module.settle.repository.po.SettleBillApplyPo">
        select id, bank_account_no, bank_account_no_uuid
        from settle_bill_apply
        where bank_account_no is not null
          and bank_account_no_uuid is not null
          and id > #{lastId}
        ORDER BY id ASC
            limit #{size}
    </select>
    <update id="updateBatchBankAccountNoUuid">
        <foreach collection="list" item="item" separator=";">
            update
            `settle_bill_apply`
            set
            `bank_account_no_uuid` = #{item.bankAccountNoUuid},
            `update_time` = now()
            where
            `id` = #{item.id}
        </foreach>
    </update>

</mapper>
