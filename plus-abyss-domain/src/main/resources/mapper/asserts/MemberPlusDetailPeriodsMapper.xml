<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusDetailPeriodsMapper">

    <resultMap id="MemberPlusDetailPeriods"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusDetailPeriodsPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="program_id" property="programId"/>
        <result column="config_id" property="configId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel_id`,
        `start_time`,
        `end_time`,
        `program_id`,
        `config_id`,
        `order_sn`,
        `create_time`,
        `update_time`,
        `bi_time`
    </sql>


    <select id="loadMemberPlusDetailPeriods" parameterType="java.lang.Integer"
            resultMap="MemberPlusDetailPeriods">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_detail_periods
        WHERE `id` = #{id}
    </select>

    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO member_plus_detail_periods (
        `user_id`,
        `channel_id`,
        `start_time`,
        `end_time`,
        `program_id`,
        `config_id`,
        `order_sn`,
        `create_time`,
        `update_time`,
        `bi_time`
        )
        VALUES
        <foreach collection="mpdpList" index="index" item="mpdp" separator=",">
            (
            #{mpdp.userId},
            #{mpdp.channelId},
            #{mpdp.startTime},
            #{mpdp.endTime},
            #{mpdp.programId},
            #{mpdp.configId},
            #{mpdp.orderSn},
            NOW(),
            NOW(),
            NOW()
            )
        </foreach>
    </insert>


    <select id="getCurrentPeriods" parameterType="java.lang.String"
            resultMap="MemberPlusDetailPeriods">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_detail_periods
        WHERE `order_sn` = #{orderSn}
        and start_time &lt;=now()
        and end_time &gt;=now()
        limit 1
    </select>

    <select id="getNextPeriods" parameterType="java.lang.String"
            resultMap="MemberPlusDetailPeriods">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_detail_periods WHERE `order_sn` = #{orderSn} and start_time &gt;=now() order
        by start_time limit 1
    </select>


    <select id="getByOrderSn" parameterType="java.lang.String"
            resultMap="MemberPlusDetailPeriods">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_detail_periods WHERE `order_sn` = #{orderSn} order
        by start_time
    </select>


    <delete id="deleteByOrderSn">
        delete from member_plus_detail_periods where `order_sn` in
        <foreach collection="orders" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
    </delete>
</mapper>
