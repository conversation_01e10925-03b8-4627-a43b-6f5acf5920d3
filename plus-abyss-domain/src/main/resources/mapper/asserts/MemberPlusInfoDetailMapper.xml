<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusInfoDetailMapper">
    <resultMap id="MemberPlusInfoDetail"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="plus_info_id" property="plusInfoId"/>
        <result column="config_id" property="configId"/>
        <result column="config_name" property="configName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="periods" property="periods"/>
        <result column="period_type" property="periodType"/>
        <result column="jx_status" property="jxStatus"/>
        <result column="jx_start_time" property="jxStartTime"/>
        <result column="jx_end_time" property="jxEndTime"/>
        <result column="program_id" property="programId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_type" property="plusType"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel_id`,
        `plus_info_id`,
        `config_id`,
        `config_name`,
        `create_time`,
        `update_time`,
        `periods`,
        `period_type`,
        `jx_status`,
        `jx_start_time`,
        `jx_end_time`,
        `program_id`,
        `channel_id`,
        order_sn,
        plus_type
    </sql>

    <sql id="Base_Column_List_B">
        m.`id`,
        m.`user_id`,
        m.`channel_id`,
        m.`plus_info_id`,
        m.`config_id`,
        m.`config_name`,
        m.`create_time`,
        m.`update_time`,
        m.`periods`,
        m.`period_type`,
        m.`jx_status`,
        m.`jx_start_time`,
        m.`jx_end_time`,
        m.`program_id`,
        m.`channel_id`,
        m.`order_sn`,
        m.`plus_type`,
        o.`order_flag`
    </sql>

    <sql id="getConditionNew">
        <where>
            <if test="queryParam.jxStartTime != null and '' != queryParam.jxStartTime">
                and p.jx_start_time &gt;= #{queryParam.jxStartTime}
            </if>
            <if test="queryParam.jxStartEndTime != null and '' !=queryParam.jxStartEndTime">
                and p.jx_start_time &lt;= #{queryParam.jxStartEndTime}
            </if>
            <if test="queryParam.jxEndStartTime != null and '' != queryParam.jxEndStartTime">
                and p.jx_end_time &gt;= #{queryParam.jxEndStartTime}
            </if>
            <if test="queryParam.jxEndTime != null and '' !=queryParam.jxEndTime">
                and p.jx_end_time &lt;= #{queryParam.jxEndTime}
            </if>
            <if test="queryParam.jxStatus != null">
                and p.`jx_status` = #{queryParam.jxStatus}
            </if>
            <if test="queryParam.configId != null">
                and p.`config_id` = #{queryParam.configId}
            </if>
            <if test="queryParam.userId != null">
                and p.`user_id` = #{queryParam.userId}
            </if>
            <if test="queryParam.channelId != null">
                and p.`channel_id` = #{queryParam.channelId}
            </if>
        </where>
    </sql>

    <insert id="insertMemberPlusInfoDetail"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo"
            useGeneratedKeys="true" keyColumn="id" keyProperty="memberPlusInfoDetail.id">
    INSERT INTO member_plus_info_detail
    (`user_id`,
    `channel_id`,
    `plus_info_id`,
    `config_id`,
    `config_name`,
    `create_time`,
    `update_time`,
    `periods`,
    `period_type`,
    `jx_status`,
    `jx_start_time`,
    `jx_end_time`,
    `program_id`, order_sn, plus_type)
    VALUES (#{memberPlusInfoDetail.userId},
    #{memberPlusInfoDetail.channelId},
    #{memberPlusInfoDetail.plusInfoId},
    #{memberPlusInfoDetail.configId},
    #{memberPlusInfoDetail.configName},
    NOW(),
    NOW(),
    #{memberPlusInfoDetail.periods},
    #{memberPlusInfoDetail.periodType},
    #{memberPlusInfoDetail.jxStatus},
    #{memberPlusInfoDetail.jxStartTime},
    #{memberPlusInfoDetail.jxEndTime},
    #{memberPlusInfoDetail.programId},
    #{memberPlusInfoDetail.orderSn},
    #{memberPlusInfoDetail.plusType})
</insert>

    <update id="updateMemberPlusInfoDetail"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        UPDATE member_plus_info_detail
        SET
        <if test="memberPlusInfoDetail.jxStatus != null">`jx_status`=
            #{memberPlusInfoDetail.jxStatus},
        </if>
        <if test="memberPlusInfoDetail.jxStartTime != null">`jx_start_time`=
            #{memberPlusInfoDetail.jxStartTime},
        </if>
        <if test="memberPlusInfoDetail.jxEndTime != null">`jx_end_time`=
            #{memberPlusInfoDetail.jxEndTime},
        </if>
        update_time = now()
        WHERE `id` = #{memberPlusInfoDetail.id}
    </update>

    <select id="getMemberPlusInfoDetailByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE `order_sn` = #{orderSn}
    </select>

    <select id="getMaxDetailByConfigId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE jx_status=1
        and config_id=#{configId}
        and user_id=#{userId}
        order by id desc
    </select>

    <select id="getJxExpiredMemberIds"
            resultType="integer">
        select id
        from member_plus_info_detail
        where jx_status = 1 and jx_end_time &lt; now()
        limit 100;
    </select>

    <select id="getJxExpiredMember"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info_detail
        where id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="getDetailByConfigId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE jx_status=1
        and config_id=#{configId}
        and user_id=#{userId}
        and jx_start_time &lt;=now()
        and jx_end_time &gt;=now()
        limit 1
    </select>

    <select id="getUserLastInfoByConfigId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE
        config_id=#{configId}
        and user_id=#{userId}
        and channel_id=#{channel}
        and jx_status=1
        and jx_end_time &gt;=now()
        order by id desc
        limit 1
    </select>

    <select id="getUserFirstInfoByConfigId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE
        config_id=#{configId}
        and user_id=#{userId}
        and channel_id=#{channel}
        and jx_status=1
        and jx_end_time &gt;=now()
        order by id
        limit 1
    </select>


    <select id="getHistoryDetail"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{userId}
        and channel_id = #{channel}
        and config_id=#{configId}
        and jx_status=1
        and jx_start_time &lt;=now()
        and jx_end_time &gt;=now()
        order by id asc
        limit 1
    </select>

    <select id="getInfoByProgramId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{userId}
        and channel_id=#{channel}
        and program_id=#{programId}
        and jx_status=1
        and jx_start_time &lt;=now()
        and jx_end_time &gt;=now()
        limit 1
    </select>

    <select id="getExpiredMemberOrderById"
            resultType="integer">
        select
        id
        from member_plus_info_detail
        where jx_status in (0,2) order by id desc
        limit 300;
    </select>


    <select id="getMemberPlusInfoById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE id=#{id}
        and user_id=#{userId}
        and channel_id=#{channel}
        and jx_status=1
    </select>


    <select id="getPlusInfoDetail"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{userId}
        and config_id=#{configId}
        and order_sn=#{orderSn}
    </select>


    <select id="getMemberPlusInfoDetail"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{memberId}
        and order_sn=#{orderSn}
    </select>

    <delete id="deleteByIds">
        DELETE FROM
        member_plus_info_detail
        WHERE
        `id` in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="getMemberPlusInfoList"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{memberId}
        and order_sn in
        <foreach collection="orderSns" item="orderSn" open="(" close=")" separator=",">
            #{orderSn}
        </foreach>
        and jx_status=1
        and jx_start_time &lt;=now()
        and jx_end_time &gt;=now()
    </select>


    <select id="getInfoByOrders"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT jx_status,order_sn
        FROM member_plus_info_detail
        <where>
            <foreach collection="orderSns" item="orderSn" open=" order_sn  in  (" close=")"
                    separator=",">
                #{orderSn}
            </foreach>
        </where>
    </select>


    <select id="getMemberListByUserId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info_detail
        where user_id = #{memberId}
        and jx_status = 1
        and config_id in
        <foreach collection="ids" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
        order by create_time desc
    </select>

    <select id="getMemberPlusListByUserId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{userId}
        and channel_id=#{channel}
        and jx_status=1
        and jx_end_time &gt;=now()
        order by id
    </select>

    <select id="getCurrentAllMemberInfo"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info_detail
        where user_id = #{memberId}
        and channel_id=#{channel}
        and jx_status = 1
        and config_id in
        <foreach collection="ids" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
        order by create_time desc
    </select>

    <select id="getUserMaxEndTimePlus"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{userId}
        and config_id in
        <foreach collection="configIds" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
        and jx_status=1
        order by jx_end_time desc
        limit 1
    </select>


    <select id="getUserMinStartTimePlus"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{userId}
        and config_id in
        <foreach collection="configIds" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
        and jx_status=1
        order by jx_start_time
        limit 1
    </select>

    <select id="getInfoByOrder"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_info_detail
        WHERE user_id=#{userId}
        and channel_id=#{channel}
        and order_sn=#{orderSn}
        and jx_status=1
        and jx_start_time &lt;=now()
        and jx_end_time &gt;=now()
        limit 1
    </select>

    <select id="getUserPLusList"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo">
        SELECT
        <include refid="Base_Column_List_B"/>
        FROM member_plus_info_detail m left join plus_order_ext_info o on m.order_sn = o.order_sn
        where m.user_id=#{userId}
        and m.jx_status=1
        and m.config_id in
        <foreach collection="configIds" item="i" index="index" open="(" separator="," close=")">
            #{i}
        </foreach>
        order by m.create_time
    </select>

    <update id="updateBatchPeriods"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailExtPo">
        <foreach collection="list" item="item" separator=";">
            update
            `member_plus_info_detail`
            set
            `jx_start_time` = #{item.jxStartTime},
            `jx_end_time` = #{item.jxEndTime},
            `update_time` = now()
            where
            `id` = #{item.id}
        </foreach>
    </update>

    <select id="getNextDetailByUser"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoDetailPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info_detail
        where user_id = #{userId}
        and channel_id=#{channelId}
        and jx_status = 1
        and config_id = #{configId}
        and jx_start_time > now()
        order by jx_start_time limit 1
    </select>

    <select id="getFirstPayTime" resultType="string">
        select
            p.pay_time
        from
            member_plus_info_detail m
                left join plus_order_info p on m.order_sn = p.order_sn
        where
          m.user_id = #{userId}
          and m.jx_status = 1
          and m.jx_end_time >= now()
          and m.config_id in
            <foreach collection="configIds" item="i" index="index" open="(" separator="," close=")">
                #{i}
            </foreach>
          and p.order_state = 2
        order by p.pay_time limit 1
    </select>


    <select id="getUserFirstInfoInLastWeek" resultType="com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity">
        select mpid.*,ifnull(poi.pay_time,poi.update_time) as order_pay_time,
            case when poi.id is null then 0 else 1 end as sort
        from member_plus_info_detail mpid
                 left join plus_order_info poi on mpid.order_sn = poi.order_sn and poi.order_state = 2
        where mpid.user_id = #{userId}
          and mpid.channel_id = #{channelId}
          and mpid.config_id in
            <foreach collection="configIds" item="configId" open="(" close=")" separator=",">
                #{configId}
            </foreach>
          and mpid.jx_status = 1
          and mpid.create_time > DATE_SUB(now(), INTERVAL 7 DAY)
        order by sort desc, order_pay_time asc,mpid.create_time asc
        limit 1
    </select>

    <select id="getMemberPlusInfoDetailByAuthTime" resultType="com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusInfoDetailEntity">
        select mpid.*,ifnull(poi.pay_time,poi.update_time) as order_pay_time,
            case when poi.id is null then 0 else 1 end as sort
        from member_plus_info_detail mpid
                 left join plus_order_info poi on mpid.order_sn = poi.order_sn and poi.order_state = 2
        where mpid.user_id = #{userId}
          and mpid.channel_id = #{channelId}
          and mpid.config_id in
            <foreach collection="configIds" item="configId" open="(" close=")" separator=",">
                #{configId}
            </foreach>
          and mpid.jx_status = 1
          and mpid.create_time > #{authTime}
        order by sort desc, order_pay_time asc,mpid.create_time asc
        limit 1
    </select>
</mapper>
