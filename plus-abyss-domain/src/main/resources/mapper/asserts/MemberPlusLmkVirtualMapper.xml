<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusLmkVirtualMapper">

    <resultMap id="MemberPlusLmkVirtual"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLmkVirtualPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="product_id" property="productId"/>
        <result column="product_name" property="productName"/>
        <result column="virtual_goods_id" property="virtualGoodsId"/>
        <result column="img_url" property="imgUrl"/>
        <result column="sku" property="sku"/>
        <result column="order_sn" property="orderSn"/>
        <result column="virtual_order_sn" property="virtualOrderSn"/>
        <result column="recharge_account" property="rechargeAccount"/>
        <result column="use_status" property="useStatus"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel_id`,
        `config_id`,
        `program_id`,
        `product_id`,
        `product_name`,
        `virtual_goods_id`,
        `sku`,
        `img_url`,
        `order_sn`,
        `virtual_order_sn`,
        `recharge_account`,
        `use_status`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="insert"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLmkVirtualPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_lmk_virtual (`user_id`,
                                             `channel_id`,
                                             `config_id`,
                                             `program_id`,
                                             `product_id`,
                                             `product_name`,
                                             `virtual_goods_id`,
                                             `img_url`,
                                             `sku`,
                                             `order_sn`,
                                             `virtual_order_sn`,
                                             `use_status`,
                                             `create_time`,
                                             `update_time`)
        VALUES (#{userId},
                #{channelId},
                #{configId},
                #{programId},
                #{productId},
                #{productName},
                #{virtualGoodsId},
                #{imgUrl},
                #{sku},
                #{orderSn},
                #{virtualOrderSn},
                #{useStatus},
                NOW(),
                #{updateTime})
    </insert>


    <update id="update"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLmkVirtualPo">
        UPDATE member_plus_lmk_virtual
        SET
        <if test="virtualOrderSn != null">`virtual_order_sn`= #{virtualOrderSn},</if>
        <if test="useStatus != null">`use_status`= #{useStatus},</if>
        <if test="rechargeAccount != null">`recharge_account` = #{rechargeAccount},</if>
        update_time = now()
        WHERE `id` = #{id}
    </update>


    <select id="selectById" parameterType="java.lang.Integer" resultMap="MemberPlusLmkVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_lmk_virtual
        WHERE `id` = #{id}
    </select>

    <select id="selectByOrderSn" parameterType="java.lang.String" resultMap="MemberPlusLmkVirtual">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_lmk_virtual
        WHERE order_sn = #{orderSn} order by id desc limit 1
    </select>
</mapper>
