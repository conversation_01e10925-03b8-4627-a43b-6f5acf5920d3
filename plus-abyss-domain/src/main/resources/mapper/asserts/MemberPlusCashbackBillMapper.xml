<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusCashbackBillMapper">

    <resultMap id="MemberPlusCashbackBill"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackBillPo">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="model_id" property="modelId"/>
        <result column="cashback_amount" property="cashbackAmount"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `order_sn`,
        `plus_order_sn`,
        `user_id`,
        `channel_id`,
        `model_id`,
        `cashback_amount`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusCashbackBill"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackBillPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_cashback_bill (`order_sn`,
                                               `plus_order_sn`,
                                               `user_id`,
                                               `channel_id`,
                                               `model_id`,
                                               `cashback_amount`,
                                               `remark`,
                                               `create_time`,
                                               `update_time`)
        VALUES (#{orderSn},
                #{plusOrderSn},
                #{userId},
                #{channelId},
                #{modelId},
                #{cashbackAmount},
                #{remark},
                NOW(),
                #{updateTime})
    </insert>


    <insert id="insertMemberPlusCashbackBill"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackBillPo">
        INSERT INTO member_plus_cashback_bill (`order_sn`,
                                               `plus_order_sn`,
                                               `user_id`,
                                               `channel_id`,
                                               `model_id`,
                                               `cashback_amount`,
                                               `remark`,
                                               `create_time`,
                                               `update_time`)
        VALUES (#{memberPlusCashbackBill.orderSn},
                #{memberPlusCashbackBill.plusOrderSn},
                #{memberPlusCashbackBill.userId},
                #{memberPlusCashbackBill.channelId},
                #{memberPlusCashbackBill.modelId},
                #{memberPlusCashbackBill.cashbackAmount},
                #{memberPlusCashbackBill.remark},
                NOW(),
                #{memberPlusCashbackBill.updateTime})
    </insert>


    <delete id="deleteMemberPlusCashbackBill" parameterType="java.lang.Integer">
        DELETE
        FROM member_plus_cashback_bill
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusCashbackBill"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackBillPo">
        UPDATE member_plus_cashback_bill
        SET
        <if test="memberPlusCashbackBill.orderSn != null">`order_sn`=
            #{memberPlusCashbackBill.orderSn},
        </if>
        <if test="memberPlusCashbackBill.plusOrderSn != null">`plus_order_sn`=
            #{memberPlusCashbackBill.plusOrderSn},
        </if>
        <if test="memberPlusCashbackBill.userId != null">`user_id`=
            #{memberPlusCashbackBill.userId},
        </if>
        <if test="memberPlusCashbackBill.channelId != null">`channel_id`=
            #{memberPlusCashbackBill.channelId},
        </if>
        <if test="memberPlusCashbackBill.modelId != null">`model_id`=
            #{memberPlusCashbackBill.modelId},
        </if>
        <if test="memberPlusCashbackBill.cashbackAmount != null">`cashback_amount`=
            #{memberPlusCashbackBill.cashbackAmount},
        </if>
        <if test="memberPlusCashbackBill.remark != null">`remark`=
            #{memberPlusCashbackBill.remark},
        </if>
        update_time = now()
        WHERE `id` = #{memberPlusCashbackBill.id}
    </update>


    <select id="loadMemberPlusCashbackBill" parameterType="java.lang.Integer"
            resultMap="MemberPlusCashbackBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_cashback_bill
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusCashbackBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_cashback_bill
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_cashback_bill
    </select>

    <select id="getCashbackBillList" parameterType="java.util.Map" resultMap="MemberPlusCashbackBill">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_cashback_bill
        where user_id = #{userId}
    </select>

</mapper>
