<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IExpireMemberCouponReceiveDetailMapper">

    <resultMap id="ExpireMemberCouponReceiveDetail"
            type="com.juzifenqi.plus.module.asserts.repository.po.coupon.ExpireMemberCouponReceiveDetailPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="receive_status" property="receiveStatus"/>
        <result column="type" property="type"/>
        <result column="other_coupon_id" property="otherCouponId"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="order_sn" property="orderSn"/>
        <result column="channel" property="channel"/>
        <result column="bak" property="bak"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `coupon_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `type`,
        `other_coupon_id`,
        `program_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `order_sn`,
        `channel`,
        `bak`,
        `create_time`,
        `update_time`,
        `bi_time`
    </sql>

    <insert id="batchInsertInfo">
        INSERT expire_member_coupon_receive_detail (
        `member_id`,
        `coupon_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `type`,
        `other_coupon_id`,
        `program_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `order_sn`,
        `channel`,
        `bak`,
        `create_time`,
        `update_time`,
        `bi_time`
        ) VALUES
        <foreach collection="list" index="index" item="expire" separator=",">
            (
            #{expire.memberId},
            #{expire.couponId},
            #{expire.receiveStartTime},
            #{expire.receiveEndTime},
            #{expire.receiveStatus},
            #{expire.type},
            #{expire.otherCouponId},
            #{expire.programId},
            #{expire.couponUserId},
            #{expire.couponNos},
            #{expire.orderSn},
            #{expire.channel},
            #{expire.bak},
            #{expire.createTime},
            #{expire.updateTime},
            #{expire.biTime}
            )
        </foreach>
    </insert>

    <select id="getExpireByCouponUserIdOrCouponNos" resultMap="ExpireMemberCouponReceiveDetail">
        select <include refid="Base_Column_List"/>
        from expire_member_coupon_receive_detail
        where member_id = #{userId}
        and (
        <if test="couponUserIds != null and couponUserIds.size > 0">
            coupon_user_id in
            <foreach collection="couponUserIds" item="couponUserId" open="(" separator="," close=")">
                #{couponUserId}
            </foreach>
        </if>
        <if test="couponNos != null and couponNos.size > 0">
            <if test="couponUserIds != null and couponUserIds.size > 0">
                or
            </if>
            coupon_nos in
            <foreach collection="couponNos" item="couponNo" open="(" separator="," close=")">
                #{couponNo}
            </foreach>
        </if>
        )
    </select>

</mapper>
