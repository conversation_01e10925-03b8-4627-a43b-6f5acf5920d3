<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponSrghMapper">
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponSrghPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="coupon_plan" property="couponPlan"/>
        <result column="send_date" property="sendDate"/>
        <result column="status" property="status"/>
        <result column="order_sn" property="orderSn"/>
        <result column="create_time" property="createTime"/>
        <result column="channel" property="channel"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , member_id, program_id, coupon_id, coupon_user_id, coupon_plan, send_date, status,
    order_sn, create_time, channel, update_time, bi_time, config_id
    </sql>

    <update id="updateCouponSrgh">
        update member_coupon_srgh
        set send_date = NOW(),
            status = 1,
            coupon_user_id = #{couponUserId},
            coupon_nos = #{couponNos},
            update_time = now()
        where id = #{id}
    </update>

    <update id="updateByCancel">
        update member_coupon_srgh
        set status = 2, update_time = now()
        where member_id = #{memberId} and order_sn = #{orderSn}
    </update>

    <insert id="batchInsertCouponSrgh" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_srgh (
        `member_id`,
        `program_id`,
        `coupon_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `coupon_plan`,
        `send_date`,
        `status`,
        `order_sn`,
        `create_time`,
        `channel`,
        `config_id`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.memberId},
            #{record.programId},
            #{record.couponId},
            #{record.couponUserId},
            #{record.couponNos},
            #{record.couponPlan},
            #{record.sendDate},
            #{record.status},
            #{record.orderSn},
            NOW(),
            #{record.channel},
            #{record.configId}
            )
        </foreach>
    </insert>

    <select id="getById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_srgh
        where id = #{id}
    </select>

    <select id="grantCouponSrghByMemberId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponSrghPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_srgh a
        <where>
            member_id = #{memberId}
            <if test="null != status and status != ''">
                and a.status=#{status}
            </if>
            <if test="orderSn != null and orderSn != ''">
                and a.order_sn = #{orderSn}
            </if>
        </where>
    </select>

    <select id="getUserCouponByMemberId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponSrghPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_srgh a
        where a.member_id = #{memberId}
        and a.status = #{status}
        limit 1;
    </select>

    <select id="getMemberCouponByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponSrghPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_srgh a
        where member_id = #{memberId}
        and order_sn = #{orderSn}
        order by coupon_plan desc
    </select>

    <select id="getMemberCouponListByMemberId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponSrghPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_srgh a
        where a.member_id = #{memberId} and a.status = 0;
    </select>

    <select id="getFeatureData"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponSrghPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_srgh
        where config_id = #{configId}
        and status in (0,1)
        and member_id = #{memberId}
        order by coupon_plan desc limit 1;
    </select>
</mapper>