<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponZshlMapper">

    <resultMap id="MemberCouponZshl"
            type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponZshlPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="receive_status" property="receiveStatus"/>
        <result column="program_id" property="programId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="channel" property="channel"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `coupon_id`,
        `coupon_user_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `program_id`,
        `order_sn`,
        `coupon_nos`,
        `channel`,
        `create_time`,
        `update_time`,
        `bi_time`,
        `config_id`
    </sql>

    <update id="updateMemberCouponZshlPo"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponZshlPo">
        UPDATE member_coupon_zshl
        SET
        <if test="memberCouponZshl.memberId != null">`member_id`= #{memberCouponZshl.memberId},</if>
        <if test="memberCouponZshl.couponId != null">`coupon_id`= #{memberCouponZshl.couponId},</if>
        <if test="memberCouponZshl.couponUserId != null">`coupon_user_id`=
            #{memberCouponZshl.couponUserId},
        </if>
        <if test="memberCouponZshl.receiveStartTime != null">`receive_start_time`=
            #{memberCouponZshl.receiveStartTime},
        </if>
        <if test="memberCouponZshl.receiveEndTime != null">`receive_end_time`=
            #{memberCouponZshl.receiveEndTime},
        </if>
        <if test="memberCouponZshl.receiveStatus != null">`receive_status`=
            #{memberCouponZshl.receiveStatus},
        </if>
        <if test="memberCouponZshl.programId != null">`program_id`= #{memberCouponZshl.programId},
        </if>
        <if test="memberCouponZshl.orderSn != null">`order_sn`= #{memberCouponZshl.orderSn},</if>
        <if test="memberCouponZshl.couponNos != null">`coupon_nos`= #{memberCouponZshl.couponNos},
        </if>
        <if test="memberCouponZshl.channel != null">`channel`= #{memberCouponZshl.channel},</if>
        <if test="memberCouponZshl.biTime != null">`bi_time`= #{memberCouponZshl.biTime},</if>
        <if test="memberCouponZshl.configId != null">`config_id`= #{memberCouponZshl.configId},</if>
        update_time = now()
        WHERE `id` = #{memberCouponZshl.id}
    </update>

    <select id="getById" parameterType="java.lang.Integer"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_coupon_zshl
        WHERE `id` = #{id}
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_zshl (
        `member_id`,
        `coupon_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `program_id`,
        `order_sn`,
        `channel`,
        `config_id`,
        `bi_time`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="zshl" separator=",">
            (
            #{zshl.memberId},
            #{zshl.couponId},
            #{zshl.couponUserId},
            #{zshl.couponNos},
            #{zshl.receiveStartTime},
            #{zshl.receiveEndTime},
            #{zshl.receiveStatus},
            #{zshl.programId},
            #{zshl.orderSn},
            #{zshl.channel},
            #{zshl.configId},
            NOW(),
            NOW(),
            NOW()
            )
        </foreach>

    </insert>

    <select id="getCurrentCoupon" resultMap="MemberCouponZshl">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_zshl
        where member_id = #{memberId}
        and `receive_start_time` &lt; NOW()
        and `receive_end_time` &gt; NOW()
        and receive_status != 2
    </select>

    <update id="receiveCouponZshl">
        UPDATE member_coupon_zshl
        SET
        <if test="receiveStatus != null">`receive_status`= #{receiveStatus},</if>
        <if test="couponUserId != null">`coupon_user_id`= #{couponUserId},</if>
        update_time = NOW()
        WHERE `id` = #{id}
    </update>

    <update id="receiveRepaymentCouponZshl">
        UPDATE member_coupon_zshl
        SET
        <if test="receiveStatus != null">`receive_status`= #{receiveStatus},</if>
        <if test="couponNos != null">`coupon_nos`= #{couponNos},</if>
        update_time = NOW()
        WHERE `id` = #{id}
    </update>

    <select id="getReceivedCouponByOrderSnForMember" resultMap="MemberCouponZshl">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_zshl
        where member_id = #{memberId}
        and `order_sn` = #{orderSn}
        and receive_status = 1
        order by `receive_start_time` asc
    </select>

    <update id="updateByCancel">
      UPDATE member_coupon_zshl
      SET receive_status = 2, update_time = now()
      WHERE member_id = #{memberId}
      AND order_sn = #{orderSn}
  </update>

    <select id="getLastForUser" resultMap="MemberCouponZshl"
            parameterType="java.lang.Integer">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_zshl
        where member_id = #{memberId}
        and receive_status != 2
        order by receive_end_time desc
        limit 1
    </select>

    <select id="getExpireCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select *
        from member_coupon_zshl
        where `order_sn` in (
        <foreach collection="list" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <select id="getMemberCouponByOrderSn" resultMap="MemberCouponZshl">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_zshl
        where member_id = #{memberId}
        and `order_sn` = #{orderSn}
        and `config_id` = #{configId}
    </select>

    <select id="getMemberCouponByOrderSns" resultMap="MemberCouponZshl">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_zshl
        where order_sn in
        <foreach collection="orders" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
    </select>

    <delete id="delExpireCoupon">
        delete from member_coupon_zshl where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>
</mapper>
