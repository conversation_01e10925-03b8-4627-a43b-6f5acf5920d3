<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberPlusCouponRecordMapper">

    <resultMap id="MemberPlusCouponRecord" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponRecordPo" >
        <result column="id" property="id" />
        <result column="member_id" property="memberId" />
        <result column="channel_id" property="channelId" />
        <result column="order_sn" property="orderSn" />
        <result column="send_plan_id" property="sendPlanId" />
        <result column="coupon_nos" property="couponNos" />
        <result column="coupon_user_id" property="couponUserId" />
        <result column="member_coupon_id" property="memberCouponId" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `coupon_nos`,
        `coupon_user_id`,
        `channel_id`,
        `order_sn`,
        `send_plan_id`,
        `member_coupon_id`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusCouponRecord" parameterType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponRecordPo" useGeneratedKeys="true" keyProperty="id">
      INSERT INTO member_plus_coupon_record (
              `member_id`,
              `channel_id`,
              `order_sn`,
              `send_plan_id`,
              `member_coupon_id`,
              `remark`,
              `create_time`,
              `update_time`,
              `coupon_nos`,
              `coupon_user_id`
      )
      VALUES(
                #{memberId},
                #{channelId},
                #{orderSn},
                #{sendPlanId},
                #{memberCouponId},
                #{remark},
                  NOW(),
                #{updateTime},
                #{couponNos},
                #{couponUserId}
      )
    </insert>


    <insert id="insertMemberPlusCouponRecord" parameterType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponRecordPo">
        INSERT INTO member_plus_coupon_record (
            `member_id`,
            `channel_id`,
            `order_sn`,
            `send_plan_id`,
            `member_coupon_id`,
            `remark`,
            `create_time`,
            `update_time`,
            `coupon_nos`,
            `coupon_user_id`
        )
        VALUES(
            #{memberPlusCouponRecordPo.memberId},
            #{memberPlusCouponRecordPo.channelId},
            #{memberPlusCouponRecordPo.orderSn},
            #{memberPlusCouponRecordPo.sendPlanId},
            #{memberPlusCouponRecordPo.memberCouponId},
            #{memberPlusCouponRecordPo.remark},
            NOW(),
            #{memberPlusCouponRecordPo.updateTime},
            #{memberPlusCouponRecordPo.couponNos},
            #{memberPlusCouponRecordPo.couponUserId}
        )
    </insert>



    <delete id="deleteMemberPlusCouponRecord" parameterType="java.lang.Integer">
        DELETE FROM member_plus_coupon_record
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusCouponRecord" parameterType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponRecordPo" >
        UPDATE member_plus_coupon_record
        SET
        <if test="memberPlusCouponRecordPo.memberId != null">`member_id`= #{memberPlusCouponRecordPo.memberId},</if>
        <if test="memberPlusCouponRecordPo.channelId != null">`channel_id`= #{memberPlusCouponRecordPo.channelId},</if>
        <if test="memberPlusCouponRecordPo.orderSn != null">`order_sn`= #{memberPlusCouponRecordPo.orderSn},</if>
        <if test="memberPlusCouponRecordPo.sendPlanId != null">`send_plan_id`= #{memberPlusCouponRecordPo.sendPlanId},</if>
        <if test="memberPlusCouponRecordPo.memberCouponId != null">`member_coupon_id`= #{memberPlusCouponRecordPo.memberCouponId},</if>
        <if test="memberPlusCouponRecordPo.remark != null">`remark`= #{memberPlusCouponRecordPo.remark},</if>
        update_time = now()
        WHERE `id` = #{memberPlusCouponRecordPo.id}
    </update>


    <select id="loadMemberPlusCouponRecord" parameterType="java.lang.Integer" resultMap="MemberPlusCouponRecord">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_coupon_record
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusCouponRecord">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_coupon_record
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_coupon_record
    </select>

    <select id="getBySendPlanId" parameterType="java.lang.Integer" resultMap="MemberPlusCouponRecord">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_coupon_record
        WHERE send_plan_id = #{sendPlanId} order by id desc limit 1
    </select>

    <select id="getBySendPlanIds" resultMap="MemberPlusCouponRecord">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_coupon_record
        WHERE send_plan_id in
        <foreach collection="sendPlanIds" item="sendPlanId" index="index" open="(" separator=","
            close=")">
        #{sendPlanId}
    </foreach>
    </select>
</mapper>
