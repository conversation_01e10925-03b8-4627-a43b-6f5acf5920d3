<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponPpzqMapper">
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponPpzqPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="coupon_plan" property="couponPlan"/>
        <result column="send_date" property="sendDate"/>
        <result column="status" property="status"/>
        <result column="order_sn" property="orderSn"/>
        <result column="create_time" property="createTime"/>
        <result column="channel" property="channel"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        , member_id, program_id, coupon_id, coupon_user_id, coupon_nos, coupon_plan, send_date, status,
    order_sn, create_time, channel, update_time, bi_time, config_id
    </sql>

    <select id="getById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_ppzq
        where id = #{id}
    </select>

    <select id="grantCouponPpzq"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponPpzqPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_ppzq a
        where a.status=0 and a.coupon_plan &lt; NOW()
        limit 50;
    </select>

    <select id="grantCouponPpzqByOrder"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponPpzqPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_ppzq a
        <where>
            order_sn = #{orderSn,jdbcType=VARCHAR}
            <if test="null != status and status != ''">
                and a.status=#{status}
            </if>
        </where>
    </select>

    <insert id="batchInsetPpzqCoupon" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_ppzq (
        `member_id`,
        `program_id`,
        `coupon_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `coupon_plan`,
        `send_date`,
        `status`,
        `order_sn`,
        `create_time`,
        `channel`,
        `config_id`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.memberId},
            #{record.programId},
            #{record.couponId},
            #{record.couponUserId},
            #{record.couponNos},
            #{record.couponPlan},
            #{record.sendDate},
            #{record.status},
            #{record.orderSn},
            NOW(),
            #{record.channel},
            #{record.configId}
            )
        </foreach>

    </insert>

    <select id="getMemberCouponPpzqByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponPpzqPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_ppzq a
        <where>
            member_id = #{memberId}
            <if test="orderSn != null and orderSn != ''">
                and a.order_sn = #{orderSn}
            </if>
        </where>
    </select>

    <select id="getFeatureData"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponPpzqPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_ppzq
        where config_id = #{configId}
        and status in (0,1)
        and member_id = #{memberId}
        order by coupon_plan desc;
    </select>

    <update id="updateStateByCancel">
        update member_coupon_ppzq
        set status = 2, update_time = now()
        where member_id = #{memberId}
        <if test="orderSn != null and orderSn !=''">
            and order_sn = #{orderSn}
        </if>
    </update>

    <update id="updateStateByState">
        update member_coupon_ppzq
        set status = #{status}, update_time = now()
        <if test="status != null and status != '' and status == 2">
            , send_date = now()
        </if>
        <if test="couponUserId != null and couponUserId != ''">
            , coupon_user_id = #{couponUserId}
        </if>
        <if test="couponNos != null and couponNos != ''">
            , coupon_nos = #{couponNos}
        </if>
        where id = #{id}
    </update>

    <select id="getExpireCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select *
        from member_coupon_ppzq
        where `order_sn` in (
        <foreach collection="list" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <delete id="delExpireCoupon">
        delete from member_coupon_ppzq where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <select id="getMemberCouponPpzqByOrderSns"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponPpzqPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_ppzq
        where order_sn in
        <foreach collection="orders" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
    </select>

    <select id="selectById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponPpzqPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_ppzq
        where id = #{id}
    </select>
</mapper>