<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponKklMapper">
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponKklPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="coupon_plan" property="couponPlan"/>
        <result column="send_date" property="sendDate"/>
        <result column="status" property="status"/>
        <result column="order_sn" property="orderSn"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="reach_status"  property="reachStatus" />
        <result column="send_type" property="sendType"/>
        <result column="create_time" property="createTime"/>
        <result column="channel" property="channel"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>

    <resultMap id="MemberPlusCouponResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponKklPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="coupon_plan" property="couponPlan"/>
        <result column="status" property="status"/>
        <result column="order_sn" property="orderSn"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="reach_status"  property="reachStatus" />
        <result column="create_time" property="createTime"/>
        <result column="channel" property="channel"/>
        <result column="update_time" property="updateTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>


    <sql id="Base_Column_List">
        id, member_id, program_id, coupon_id, coupon_user_id, coupon_nos, coupon_plan, send_date, status,
    order_sn,receive_start_time,receive_end_time,reach_status,send_type, create_time, channel, update_time, bi_time, config_id
    </sql>

    <insert id="batchInsertCouponKkl" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_kkl (
        `member_id`,
        `program_id`,
        `coupon_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `coupon_plan`,
        `send_date`,
        `status`,
        `order_sn`,
        `receive_start_time`,
        `receive_end_time`,
        `reach_status`,
        `send_type`,
        `create_time`,
        `channel`,
        `config_id`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.memberId},
            #{record.programId},
            #{record.couponId},
            #{record.couponUserId},
            #{record.couponNos},
            #{record.couponPlan},
            #{record.sendDate},
            #{record.status},
            #{record.orderSn},
            #{record.receiveStartTime},
            #{record.receiveEndTime},
            #{record.reachStatus},
            #{record.sendType},
            NOW(),
            #{record.channel},
            #{record.configId}
            )
        </foreach>
    </insert>

    <update id="updateCouponKklByCancel">
        update member_coupon_kkl
        set status = 2, update_time = now()
        where member_id = #{memberId} and order_sn = #{orderSn}
    </update>

    <select id="getById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_kkl
        where id = #{id}
    </select>

    <select id="getMemberCouponKklPoByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponKklPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_kkl a
        <where>
            member_id = #{memberId}
            <if test="orderSn != null and orderSn != ''">
                and a.order_sn = #{orderSn}
            </if>
            <if test="configId != null and configId != ''">
                and a.config_id = #{configId}
            </if>
            order by create_time desc
        </where>
    </select>

    <select id="getKklCouponByConfig" resultMap="MemberPlusCouponResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_kkl
        where member_id = #{memberId}
        and config_id = #{configId}
        and status in (0,1)
        order by `create_time` asc
    </select>

    <select id="getExpireCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select *
        from member_coupon_kkl
        where `order_sn` in (
        <foreach collection="list" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <delete id="delExpireCoupon">
        delete from member_coupon_kkl where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <update id="receiveKklCoupon">
        UPDATE member_coupon_kkl
        SET
        <if test="couponNos != null and couponNos != ''">`coupon_nos`= #{couponNos},</if>
        <if test="couponUserId != null">`coupon_user_id`= #{couponUserId},</if>
        `status`= 1,
        `update_time` = NOW()
        WHERE `id` = #{id}
    </update>

    <select id="getByProgramIdAndCouponId" resultMap="MemberPlusCouponResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_kkl
        where order_sn = #{orderSn} and program_id = #{programId} and coupon_id = #{couponId} order by id desc limit 1
    </select>

    <select id="getByProgramIdAndCouponIds" resultMap="MemberPlusCouponResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_kkl
        where order_sn = #{orderSn} and coupon_id in
        <foreach collection="couponIds" item="couponId" index="index" open="(" separator=","
                close=")">
            #{couponId}
        </foreach>
        order by id desc limit 1
    </select>
</mapper>