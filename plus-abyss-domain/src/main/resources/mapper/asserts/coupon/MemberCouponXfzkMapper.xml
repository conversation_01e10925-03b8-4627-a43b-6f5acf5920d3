<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponXfzkMapper">
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponXfzkPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_plan" property="couponPlan"/>
        <result column="status" property="status"/>
        <result column="order_sn" property="orderSn"/>
        <result column="create_time" property="createTime"/>
        <result column="channel" property="channel"/>
        <result column="update_time" property="updateTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, member_id, program_id, coupon_id, coupon_user_id, coupon_plan, status, order_sn, create_time,
    channel, update_time, config_id
    </sql>

    <select id="getById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_xfzk
        where id = #{id}
    </select>

    <insert id="batchInsertCouponXfzk" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_xfzk (
        `member_id`,
        `program_id`,
        `coupon_id`,
        `coupon_user_id`,
        `coupon_plan`,
        `status`,
        `order_sn`,
        `create_time`,
        `channel`,
        `config_id`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.memberId},
            #{record.programId},
            #{record.couponId},
            #{record.couponUserId},
            #{record.couponPlan},
            #{record.status},
            #{record.orderSn},
            NOW(),
            #{record.channel},
            #{record.configId}
            )
        </foreach>
    </insert>

    <update id="updateCouponXfzkByCancel">
        update member_coupon_xfzk
        set status = 2, update_time = now()
        where member_id = #{memberId}
        <if test="orderSn != null and orderSn !=''">
            and order_sn = #{orderSn}
        </if>
    </update>

    <select id="getMemberCouponXfzkPoByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponXfzkPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_xfzk a
        <where>
            member_id = #{memberId}
            <if test="null != status and status != ''">
                and a.status=#{status}
            </if>
            <if test="orderSn != null and orderSn != ''">
                and a.order_sn = #{orderSn}
            </if>
        </where>
    </select>

    <update id="updateByCancel">
        update member_coupon_xfzk
        set status = 2, update_time = now()
        where member_id = #{memberId} and order_sn = #{orderSn}
    </update>

    <select id="getXfzkCouponByConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_xfzk
        where member_id = #{memberId}
        and config_id = #{configId}
        and status = 1
        order by `coupon_plan` asc
    </select>

    <select id="getExpireCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select *
        from member_coupon_xfzk
        where `order_sn` in (
        <foreach collection="list" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <select id="getMemberCouponByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponXfzkPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_xfzk
        where
            member_id = #{memberId}
            and order_sn = #{orderSn}
    </select>

    <delete id="delExpireCoupon">
        delete from member_coupon_xfzk where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_xfzk
        where id = #{id}
    </select>

    <update id="updateStateByState">
        update member_coupon_xfzk
        set status = #{status}, update_time = now()
        <if test="status != null and status != '' and status == 1">
            , coupon_plan = now()
        </if>
        <if test="couponUserId != null and couponUserId != ''">
            , coupon_user_id = #{couponUserId}
        </if>
        where id = #{id}
    </update>
</mapper>