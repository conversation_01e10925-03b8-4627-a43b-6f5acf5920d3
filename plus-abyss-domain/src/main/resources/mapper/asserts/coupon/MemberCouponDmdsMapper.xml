<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponDmdsMapper">
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="receive_status" property="receiveStatus"/>
        <result column="reach_status" property="reachStatus"/>
        <result column="program_id" property="programId"/>
        <result column="task_id" property="taskId"/>
        <result column="order_money" property="orderMoney"/>
        <result column="orders_number" property="ordersNumber"/>
        <result column="order_sn" property="orderSn"/>
        <result column="channel" property="channel"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
        <result column="config_id" property="configId"/>
        <result column="bak" property="bak"/>
    </resultMap>
    <sql id="Base_Column_List">
        id , member_id, coupon_id, coupon_user_id, receive_start_time, receive_end_time, receive_status,reach_status,
    program_id, task_id, order_money, orders_number, order_sn, channel, create_time, 
    update_time, bi_time,config_id
    </sql>

    <select id="getById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_dmds
        where id = #{id}
    </select>

    <select id="getMinOrdersNumOfCurrentCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_dmds
        where member_id = #{memberId}
        and `receive_start_time` &lt; NOW()
        and `receive_end_time` &gt; NOW()
        and receive_status = 0
        and reach_status = 0
        order by `orders_number` asc
        limit 1
    </select>

    <select id="getCurrentCouponDmdsByType"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_dmds
        where
        member_id = #{memberId}
        and `receive_start_time` &lt; NOW()
        and `receive_end_time` &gt; NOW()
        and order_sn = #{orderSn}
        order by orders_number
    </select>

    <select id="getMinOrdersNumOfCurrentCouponList"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_dmds
        where member_id = #{memberId}
        and `receive_start_time` &lt; NOW()
        and `receive_end_time` &gt; NOW()
        and receive_status = 0
        and reach_status = 0
        and config_id =#{confIgId}
        order by `orders_number` asc
        limit 1
    </select>

    <select id="getCouponByMidAndOrder"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_dmds
        where member_id = #{memberId}
        and `order_sn` = #{orderSn}
        and receive_status = 1
        and reach_status = 1
        order by id
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_dmds (
        `member_id`,
        `coupon_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `reach_status`,
        `program_id`,
        `order_money`,
        `orders_number`,
        `task_id`,
        `coupon_user_id`,
        `order_sn`,
        `create_time`,
        `update_time`,
        config_id
        )
        VALUES
        <foreach collection="list" index="index" item="mpc" separator=",">
            (
            #{mpc.memberId},
            #{mpc.couponId},
            #{mpc.receiveStartTime},
            #{mpc.receiveEndTime},
            #{mpc.receiveStatus},
            #{mpc.reachStatus},
            #{mpc.programId},
            #{mpc.orderMoney},
            #{mpc.ordersNumber},
            #{mpc.taskId},
            #{mpc.couponUserId},
            #{mpc.orderSn},
            NOW(),
            NOW(),
            #{mpc.configId}
            )
        </foreach>

    </insert>

    <update id="changeReachStatusById">
        UPDATE member_coupon_dmds
        SET reach_status = 1, update_time = NOW()
        where id in (
        <foreach collection="idList" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <update id="update" parameterType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        UPDATE member_coupon_dmds
        SET
        <if test="receiveStatus != null">`receive_status`=
            #{receiveStatus},
        </if>
        <if test="reachStatus != null">`reach_status`=
            #{reachStatus},
        </if>
        <if test="couponUserId != null">`coupon_user_id`=
            #{couponUserId},
        </if>
        <if test="orderMoney != null">`order_money`=
            #{orderMoney},
        </if>
        <if test="ordersNumber != null">`orders_number`=
            #{ordersNumber},
        </if>
        <if test="bak != null">`bak`= #{bak},</if>
        update_time = now()
        WHERE `id` = #{id}
    </update>

    <update id="updateRepaymentCouponDmds">
        UPDATE member_coupon_dmds
        SET
        <if test="receiveStatus != null">`receive_status`= #{receiveStatus},</if>
        <if test="couponUserId != null">`coupon_user_id`= #{couponUserId},</if>
        update_time = NOW()
        WHERE `id` = #{id}
    </update>

    <update id="updateByCancel">
        UPDATE member_coupon_dmds
        SET receive_status = 2, update_time = now()
        WHERE member_id = #{memberId}
          AND order_sn = #{orderSn}
    </update>

    <select id="getExpireCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select *
        from member_coupon_dmds
        where `order_sn` in (
        <foreach collection="list" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <delete id="delExpireCoupon">
        delete from member_coupon_dmds where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <select id="getByOrderSns"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_dmds
        where order_sn in
        <foreach collection="orders" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
    </select>

    <select id="getMemberCouponDmdsByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponDmdsPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_dmds
        where member_id = #{memberId}
        and order_sn = #{orderSn}
        order by orders_number
    </select>
</mapper>