<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponQjhkMapper">
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponQjhkPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_plan" property="couponPlan"/>
        <result column="status" property="status"/>
        <result column="order_sn" property="orderSn"/>
        <result column="create_time" property="createTime"/>
        <result column="channel" property="channel"/>
        <result column="update_time" property="updateTime"/>
        <result column="config_id" property="configId"/>
        <result column="coupon_nos" property="couponNos"/>
    </resultMap>
    <sql id="Base_Column_List">
        id, member_id, program_id, coupon_id, coupon_user_id, coupon_plan, status, order_sn, create_time,
    channel, update_time, config_id,coupon_nos
    </sql>

    <insert id="batchInsertCouponQjhk" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_qjhk (
        `member_id`,
        `program_id`,
        `coupon_id`,
        `coupon_user_id`,
        `coupon_plan`,
        `status`,
        `order_sn`,
        `create_time`,
        `channel`,
        `config_id`,
        `coupon_nos`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.memberId},
            #{record.programId},
            #{record.couponId},
            #{record.couponUserId},
            #{record.couponPlan},
            #{record.status},
            #{record.orderSn},
            NOW(),
            #{record.channel},
            #{record.configId},
            #{record.couponNos}
            )
        </foreach>
    </insert>

    <update id="updateCouponQjhkkByCancel">
        update member_coupon_qjhk
        set status = 2, update_time = now()
        where member_id = #{memberId}
        <if test="orderSn != null and orderSn !=''">
            and order_sn = #{orderSn}
        </if>
    </update>


    <select id="getById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_qjhk
        where id = #{id}
    </select>

    <select id="getMemberCouponQjhkByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponQjhkPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_qjhk a
        <where>
            member_id = #{memberId}
            <if test="null != status and status != ''">
                and a.status=#{status}
            </if>
            <if test="orderSn != null and orderSn != ''">
                and a.order_sn = #{orderSn}
            </if>
        </where>
    </select>

    <update id="updateByCancel">
        update member_coupon_qjhk
        set status = 2, update_time = now()
        where member_id = #{memberId}
        <if test="orderSn != null and orderSn !=''">
            and order_sn = #{orderSn}
        </if>
    </update>

    <select id="getQjhkCouponByConfig" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_qjhk
        where member_id = #{memberId}
        and config_id = #{configId}
        and status = 1
        order by `coupon_plan` asc
    </select>

    <select id="getExpireCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponQjhkPo">
        select *
        from member_coupon_qjhk
        where `order_sn` in (
        <foreach collection="list" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <select id="getMemberCouponByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponQjhkPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_qjhk
        where
            member_id = #{memberId}
            and order_sn = #{orderSn}
    </select>

    <delete id="delExpireCoupon">
        delete from member_coupon_qjhk where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <select id="selectById" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_qjhk
        where id = #{id}
    </select>

    <update id="updateStateByState">
        update member_coupon_qjhk
        set status = #{status}, update_time = now()
        <if test="status != null and status != '' and status == 1">
            , coupon_plan = now()
        </if>
        <if test="couponUserId != null and couponUserId != ''">
            , coupon_user_id = #{couponUserId}
        </if>
        <if test="couponNos != null and couponNos != ''">
            , coupon_nos = #{couponNos}
        </if>
        where id = #{id}
    </update>
</mapper>