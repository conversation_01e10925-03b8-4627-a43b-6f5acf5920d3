<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponReceiveDetailMapper">
    <resultMap id="MemberCouponReceiveDetail"
            type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponReceiveDetailPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="receive_status" property="receiveStatus"/>
        <result column="type" property="type"/>
        <result column="program_id" property="programId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="order_sn" property="orderSn"/>
        <result column="channel" property="channel"/>
        <result column="bak" property="bak"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `member_id`,
        `coupon_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `type`,
        `program_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `order_sn`,
        `channel`,
        `bak`,
        `create_time`,
        `update_time`,
        `bi_time`
    </sql>

    <insert id="batchSaveDetails" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_receive_detail
        (`member_id`,
        `coupon_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `type`,
        `program_id`,
        `coupon_user_id`,
        `coupon_nos`,
        `order_sn`,
        `channel`,
        `bak`,
        `create_time`,
        `update_time`,
        `bi_time`)
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (#{record.memberId},
            #{record.couponId},
            #{record.receiveStartTime},
            #{record.receiveEndTime},
            #{record.receiveStatus},
            #{record.type},
            #{record.programId},
            #{record.couponUserId},
            #{record.couponNos},
            #{record.orderSn},
            #{record.channel},
            #{record.bak},
            NOW(), NOW(), NOW())
        </foreach>
    </insert>

    <update id="updateCouopnStateByOrderSn">
        UPDATE member_coupon_receive_detail
        set receive_status=2, update_time = now()
        where order_sn = #{orderSn}
    </update>

    <select id="getUserReceivedCouponList" resultMap="MemberCouponReceiveDetail">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_receive_detail
        where member_id = #{memberId}
        and `order_sn` = #{orderSn}
        and receive_status = 1
        <if test="type != null and type != 0">
            and `type` = #{type}
        </if>
        order by `receive_start_time` asc
    </select>

    <delete id="deleteByOrderSn">
        DELETE FROM
        member_coupon_receive_detail
        WHERE
        `order_sn` in
        <foreach collection="orders" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
    </delete>

    <select id="getByOrderSnAndCouponId" resultMap="MemberCouponReceiveDetail">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_receive_detail
        where `order_sn` = #{orderSn}
        and `coupon_id` = #{couponId}
        and `type` = #{type}
        order by id desc limit 1
    </select>

    <update id="updateById">
        UPDATE member_coupon_receive_detail
        set receive_status=#{receiveStatus},
            coupon_user_id = #{couponUserId},
            coupon_nos = #{couponNos},
            update_time = now()
        where id = #{id}
    </update>

    <select id="getByOrderSns" resultMap="MemberCouponReceiveDetail">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_receive_detail
        where `order_sn` in
        <foreach collection="orderSns" item="orderSn" index="index" open="(" separator="," close=")">
            #{orderSn}
        </foreach>
    </select>

    <select id="getByCouponUserIdOrCouponNos" resultMap="MemberCouponReceiveDetail">
        select <include refid="Base_Column_List"/>
        from member_coupon_receive_detail
        where member_id = #{userId}
        and (
        <if test="couponUserIds != null and couponUserIds.size > 0">
            coupon_user_id in
            <foreach collection="couponUserIds" item="couponUserId" open="(" separator="," close=")">
                #{couponUserId}
            </foreach>
        </if>
        <if test="couponNos != null and couponNos.size > 0">
            <if test="couponUserIds != null and couponUserIds.size > 0">
                or
            </if>
            coupon_nos in
            <foreach collection="couponNos" item="couponNo" open="(" separator="," close=")">
                #{couponNo}
            </foreach>
        </if>
        )
    </select>

</mapper>
