<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponJjpMapper">
    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        <id column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="receive_status" property="receiveStatus"/>
        <result column="reach_status" property="reachStatus"/>
        <result column="program_id" property="programId"/>
        <result column="rejection_id" property="rejectionId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="channel" property="channel"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="bi_time" property="biTime"/>
        <result column="config_id" property="configId"/>
    </resultMap>
    <sql id="Base_Column_List">
    id , member_id, coupon_id, coupon_user_id, receive_start_time, receive_end_time, receive_status,reach_status,
    program_id, rejection_id, order_sn, channel, create_time, update_time, bi_time,config_id
    </sql>
    <delete id="delExpireCoupon">
        delete from member_coupon_jjp where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <select id="getLastMemberCouponJjpsForUser"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_jjp
        where member_id = #{memberId}
        and config_id=#{configId}
        and receive_status in (0,1)
        order by receive_end_time desc
        limit 1
    </select>

    <select id="getById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_jjp
        where id = #{id}
    </select>

    <select id="getCurrentCouponJjpByType"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_jjp
        where
        member_id = #{memberId}
        and `receive_start_time` &lt; NOW()
        and `receive_end_time` &gt; NOW()
        and order_sn = #{orderSn}
    </select>

    <select id="loadMemberCouponJJPPoJjp"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        select j.*,
               ppr.type as type
        from member_coupon_jjp j
                 left join plus_program_rejection ppr on ppr.id = j.rejection_id
        where j.`id` = #{id}
    </select>

    <select id="getCurrentCouponJjpById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        select
        j.*, ppr.type as type
        from member_coupon_jjp j
        left join plus_program_rejection ppr on ppr.id = j.rejection_id
        where j.member_id = #{memberId}
        and j.receive_status = 0
        and j.reach_status = 0
        and j.`receive_start_time` &lt; NOW()
        and j.`receive_end_time` &gt; NOW()
        and j.config_id in (
        <foreach collection="configList" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
        and ppr.type=#{type}
        order by j.id
    </select>

    <select id="getCouponByMidAndOrder"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_jjp
        where member_id = #{memberId}
        and `order_sn` = #{orderSn}
        and receive_status = 1
        and reach_status = 1
        order by id
    </select>

    <select id="getExpireCoupon"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
        select *
        from member_coupon_jjp
        where `order_sn` in (
        <foreach collection="list" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>

    <select id="getMemberCouponJjpByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        select
        <include refid="Base_Column_List"/>
        from member_coupon_jjp
        where
        member_id = #{memberId}
        and order_sn = #{orderSn}
    </select>

    <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_coupon_jjp (
        `member_id`,
        `coupon_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `reach_status`,
        `program_id`,
        `rejection_id`,
        `coupon_user_id`,
        `order_sn`,
        `create_time`,
        `update_time`,
        config_id
        )
        VALUES
        <foreach collection="list" index="index" item="mpc" separator=",">
            (
            #{mpc.memberId},
            #{mpc.couponId},
            #{mpc.receiveStartTime},
            #{mpc.receiveEndTime},
            #{mpc.receiveStatus},
            #{mpc.reachStatus},
            #{mpc.programId},
            #{mpc.rejectionId},
            #{mpc.couponUserId},
            #{mpc.orderSn},
            NOW(),
            NOW(),
            #{mpc.configId}
            )
        </foreach>
    </insert>

    <update id="changeReachStatusById">
        UPDATE member_coupon_jjp
        SET reach_status = 1, update_time = NOW()
        where id in (
        <foreach collection="idList" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </update>


    <update id="updateRepaymentCouponJjp">
        UPDATE member_coupon_jjp
        SET
        <if test="receiveStatus != null">`receive_status`= #{receiveStatus},</if>
        <if test="couponUserId != null">`coupon_user_id`= #{couponUserId},</if>
        update_time = NOW()
        WHERE `id` = #{id}
    </update>

    <update id="updateByCancel">
        UPDATE member_coupon_jjp
        SET receive_status = 2, update_time = now()
        WHERE member_id = #{memberId}
          AND order_sn = #{orderSn}
    </update>

    <select id="getByOrderSns"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponJjpPo">
        select  <include refid="Base_Column_List"/>
        from member_coupon_jjp
        where `order_sn` in (
        <foreach collection="orders" item="orderSn" index="index" separator=",">
            #{orderSn}
        </foreach>
        )
    </select>
</mapper>