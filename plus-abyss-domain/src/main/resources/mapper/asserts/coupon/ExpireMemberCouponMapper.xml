<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IExpireMemberCouponMapper">

    <resultMap id="expireMemberCoupon"
            type="com.juzifenqi.plus.module.asserts.repository.po.coupon.ExpireMemberCouponPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="coupon_id" property="couponId"/>
        <result column="coupon_user_id" property="couponUserId"/>
        <result column="receive_start_time" property="receiveStartTime"/>
        <result column="receive_end_time" property="receiveEndTime"/>
        <result column="receive_status" property="receiveStatus"/>
        <result column="reach_status" property="reachStatus"/>
        <result column="program_id" property="programId"/>
        <result column="task_id" property="taskId"/>
        <result column="rejection_id" property="rejectionId"/>
        <result column="order_money" property="orderMoney"/>
        <result column="orders_number" property="ordersNumber"/>
        <result column="order_sn" property="orderSn"/>
        <result column="channel" property="channel"/>
        <result column="bi_time" property="biTime"/>
        <result column="coupon_nos" property="couponNos"/>
        <result column="config_id" property="configId"/>
        <result column="coupon_plan" property="couponPlan"/>
        <result column="send_date" property="sendDate"/>
        <result column="status" property="status"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="type" property="type"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `coupon_id`,
        `coupon_user_id`,
        `receive_start_time`,
        `receive_end_time`,
        `receive_status`,
        `reach_status`,
        `program_id`,
        `task_id`,
        `rejection_id`,
        `order_money`,
        `orders_number`,
        `order_sn`,
        `channel`,
        `bi_time`,
        `coupon_nos`,
        `config_id`,
        `coupon_plan`,
        `send_date`,
        `status`,
        `create_time`,
        `update_time`,
        `type`
    </sql>

    <insert id="bathExpireMemberCoupon"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.coupon.ExpireMemberCouponPo">
        INSERT INTO expire_member_coupon (
            `member_id`,
            `coupon_id`,
            `coupon_user_id`,
            `receive_start_time`,
            `receive_end_time`,
            `receive_status`,
            `reach_status`,
            `program_id`,
            `task_id`,
            `rejection_id`,
            `order_money`,
            `orders_number`,
            `order_sn`,
            `channel`,
            `bi_time`,
            `coupon_nos`,
            `config_id`,
            `coupon_plan`,
            `send_date`,
            `status`,
            `create_time`,
            `update_time`,
            `type`
        )
        VALUES
        <foreach collection="list" index="index" item="expireMemberCoupon" separator=",">
        ( #{expireMemberCoupon.memberId},
            #{expireMemberCoupon.couponId},
            #{expireMemberCoupon.couponUserId},
            #{expireMemberCoupon.receiveStartTime},
            #{expireMemberCoupon.receiveEndTime},
            #{expireMemberCoupon.receiveStatus},
            #{expireMemberCoupon.reachStatus},
            #{expireMemberCoupon.programId},
            #{expireMemberCoupon.taskId},
            #{expireMemberCoupon.rejectionId},
            #{expireMemberCoupon.orderMoney},
            #{expireMemberCoupon.ordersNumber},
            #{expireMemberCoupon.orderSn},
            #{expireMemberCoupon.channel},
            #{expireMemberCoupon.biTime},
            #{expireMemberCoupon.couponNos},
            #{expireMemberCoupon.configId},
            #{expireMemberCoupon.couponPlan},
            #{expireMemberCoupon.sendDate},
            #{expireMemberCoupon.status},
            NOW(),
            #{expireMemberCoupon.updateTime},
            #{expireMemberCoupon.type}
        )
        </foreach>
    </insert>

    <select id="getMemberCouponByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.ExpireMemberCouponPo">
        select
        <include refid="Base_Column_List"/>
        from expire_member_coupon
        where order_sn = #{orderSn}
        and type = #{type}
        order by orders_number
    </select>

    <select id="getMemberCouponByOrderSnAndCouponId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.ExpireMemberCouponPo">
        select
        <include refid="Base_Column_List"/>
        from expire_member_coupon
        where order_sn = #{orderSn}
        and type = #{type}
        and coupon_id = #{couponId}
        order by id desc limit 1
    </select>
</mapper>
