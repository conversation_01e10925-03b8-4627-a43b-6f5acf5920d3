<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.coupon.IMemberCouponHkyhMapper">
  <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponHkyhPo">
    <id column="id"  property="id" />
    <result column="member_id"  property="memberId" />
    <result column="coupon_id"  property="couponId" />
    <result column="coupon_user_id"  property="couponUserId" />
    <result column="receive_start_time"  property="receiveStartTime" />
    <result column="receive_end_time"  property="receiveEndTime" />
    <result column="receive_status" property="receiveStatus"/>
    <result column="reach_status"  property="reachStatus" />
    <result column="program_id"  property="programId" />
    <result column="task_id"  property="taskId" />
    <result column="order_sn"  property="orderSn" />
    <result column="channel"  property="channel" />
    <result column="create_time"  property="createTime" />
    <result column="update_time"  property="updateTime" />
    <result column="bi_time"  property="biTime" />
    <result column="config_id"  property="configId" />
    <result column="coupon_nos" property="couponNos"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, member_id, coupon_id, coupon_user_id, receive_start_time, receive_end_time, receive_status,reach_status,
    program_id, task_id, order_sn, channel, create_time, update_time, bi_time,config_id,coupon_nos
  </sql>

  <select id="getLastMemberCouponHkyhPoForUser"
          resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponHkyhPo">
    select
    <include refid="Base_Column_List"/>
    from member_coupon_hkyh
    where member_id = #{memberId}
    and config_id=#{configId}
    and receive_status in (0,1)
    order by receive_end_time desc
    limit 1
  </select>

  <select id="getById"
          resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
    select
    <include refid="Base_Column_List"/>
    from member_coupon_hkyh
    where `id` = #{id}
  </select>


  <insert id="batchInsert" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO member_coupon_hkyh (
    `member_id`,
    `coupon_id`,
    `receive_start_time`,
    `receive_end_time`,
    `receive_status`,
    `reach_status`,
    `program_id`,
    `task_id`,
    `coupon_user_id`,
    `order_sn`,
    `create_time`,
    `update_time`,
     config_id,
     coupon_nos
    )
    VALUES
    <foreach collection="list" index="index" item="mpc" separator=",">
      (
      #{mpc.memberId},
      #{mpc.couponId},
      #{mpc.receiveStartTime},
      #{mpc.receiveEndTime},
      #{mpc.receiveStatus},
      #{mpc.reachStatus},
      #{mpc.programId},
      #{mpc.taskId},
      #{mpc.couponUserId},
      #{mpc.orderSn},
      NOW(),
      NOW(),
      #{mpc.configId},
      #{mpc.couponNos}
      )
    </foreach>

  </insert>

  <update id="updateRepaymentCouponHkyh">
    UPDATE member_coupon_hkyh
    SET
    <if test="receiveStatus != null">`receive_status`= #{receiveStatus},</if>
    <if test="couponNos != null">`coupon_nos`= #{couponNos},</if>
    <if test="couponUserId != null">`coupon_user_id`= #{couponUserId},</if>
    update_time = NOW()
    WHERE `id` = #{id}
  </update>

  <update id="updateByCancel">
    UPDATE member_coupon_hkyh
    SET receive_status = 2, update_time = now()
    WHERE member_id = #{memberId}
    AND order_sn = #{orderSn}
  </update>

  <select id="getHkyhCouponByConfig"
          resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponHkyhPo">
    select
    <include refid="Base_Column_List"/>
    from member_coupon_hkyh
    where member_id = #{memberId}
    and config_id = #{configId}
    and receive_status in (0,1)
    order by `create_time` asc
  </select>

  <select id="getExpireCoupon"
          resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberPlusCouponPo">
    select *
    from member_coupon_hkyh
    where `order_sn` in (
    <foreach collection="list" item="orderSn" index="index" separator=",">
      #{orderSn}
    </foreach>
    )
  </select>

  <select id="getMemberCouponHkyhPoByOrderSn"
          resultType="com.juzifenqi.plus.module.asserts.repository.po.coupon.MemberCouponHkyhPo">
    select
    <include refid="Base_Column_List"/>
    from member_coupon_hkyh
    where member_id = #{memberId}
    and config_id=#{configId}
    and order_sn = #{orderSn}
  </select>

  <delete id="delExpireCoupon">
    delete from member_coupon_hkyh where id in (
    <foreach collection="list" index="index" item="id" separator=",">
      #{id}
    </foreach>
    )
  </delete>
</mapper>