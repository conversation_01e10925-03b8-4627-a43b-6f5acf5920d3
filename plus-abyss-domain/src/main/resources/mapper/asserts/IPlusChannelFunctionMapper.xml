<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusSendPlanExtMapper">

    <resultMap id="MemberPlusSendPlanExt"
            type="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusSendPlanExtPo">
        <result column="id" property="id"/>
        <result column="member_plus_send_plan_id" property="memberPlusSendPlanId"/>
        <result column="dmds_task_id" property="dmdsTaskId"/>
        <result column="reject_task_id" property="rejectTaskId"/>
        <result column="reject_type" property="rejectType"/>
        <result column="order_num" property="orderNum"/>
        <result column="cashback_type" property="cashbackType"/>
        <result column="loan_order_sn" property="loanOrderSn"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_plus_send_plan_id`,
        `dmds_task_id`,
        `reject_task_id`,
        `order_num`,
        `cashback_type`,
        `reject_type`,
        `loan_order_sn`,
        `create_time`
    </sql>

    <insert id="batchInsert"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanExtPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO expire_member_plus_send_plan_ext (
        `id`,
        `member_plus_send_plan_id`,
        `dmds_task_id`,
        `reject_task_id`,
        `order_num`,
        `cashback_type`,
        `reject_type`,
        `loan_order_sn`,
        `create_time`
        )
        VALUES
        <foreach collection="list" index="index" item="l" separator=",">
            (
            #{l.id},
            #{l.memberPlusSendPlanId},
            #{l.dmdsTaskId},
            #{l.rejectTaskId},
            #{l.orderNum},
            #{l.cashbackType},
            #{l.rejectType},
            #{l.loanOrderSn},
            NOW()
            )
        </foreach>
    </insert>
</mapper>
