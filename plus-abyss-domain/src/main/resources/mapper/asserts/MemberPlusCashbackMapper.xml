<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusCashbackMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackPo">
        <id column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_num" property="orderNum"/>
        <result column="order_amount" property="orderAmount"/>
        <result column="product_id" property="productId"/>
        <result column="cashback_amount" property="cashbackAmount"/>
        <result column="back_status" property="backStatus"/>
        <result column="drz_msg_id" property="drzMsgId"/>
        <result column="rz_msg_id" property="rzMsgId"/>
        <result column="qxrz_msg_id" property="qxrzMsgId"/>
        <result column="remark" property="remark"/>
        <result column="order_time" property="orderTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , order_sn, plus_order_sn, user_id, channel_id,order_num, order_amount, product_id, cashback_amount, back_status, drz_msg_id, rz_msg_id, qxrz_msg_id, remark, order_time, create_time, update_time
    </sql>

    <select id="queryCashbackListAll"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_cashback where user_id = #{userId} and plus_order_sn = #{plusOrderSn}
    </select>

    <select id="queryCashbackList"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_cashback where user_id = #{userId} and
        plus_order_sn =
        #{plusOrderSn} and back_status in (1,3) order by id desc
    </select>

    <select id="queryDrzCashbackList"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_cashback where user_id = #{userId} and
        plus_order_sn =
        #{plusOrderSn} and back_status = 1 order by id
    </select>

    <select id="queryLastOrderNum" resultType="java.lang.Integer">
        SELECT order_num
        FROM `member_plus_cashback`
        WHERE plus_order_sn = #{plusOrderSn}
          and back_status IN (1, 3)
        ORDER BY order_num DESC LIMIT 1
    </select>

    <insert id="insertSelective"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_cashback (order_sn,
                                          plus_order_sn,
                                          user_id,
                                          channel_id,
                                          order_num,
                                          order_amount,
                                          product_id,
                                          cashback_amount,
                                          back_status,
                                          drz_msg_id,
                                          rz_msg_id,
                                          qxrz_msg_id,
                                          remark,
                                          order_time,
                                          create_time,
                                          update_time)
        VALUES (#{orderSn}, #{plusOrderSn}, #{userId}, #{channelId}, #{orderNum},
                #{orderAmount}, #{productId}, #{cashbackAmount}, #{backStatus}, #{drzMsgId},
                #{rzMsgId}, #{qxrzMsgId}, #{remark}, NOW(), NOW(), NOW())
    </insert>

    <insert id="reInsertSelectiveBatch"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_cashback (
        order_sn,
        plus_order_sn,
        user_id,
        channel_id,
        order_num,
        order_amount,
        product_id,
        cashback_amount,
        back_status,
        drz_msg_id,
        rz_msg_id,
        qxrz_msg_id,
        remark,
        order_time,
        create_time,
        update_time)
        VALUES
        <foreach collection="cashbacks" item="item" separator=",">(
            #{item.orderSn},#{item.plusOrderSn},#{item.userId}, #{item.channelId},
            #{item.orderNum},#{item.orderAmount}, #{item.productId}, #{item.cashbackAmount},
            #{item.backStatus},#{item.drzMsgId},#{item.rzMsgId}, #{item.qxrzMsgId},
            #{item.remark}, NOW(), NOW(), NOW())
        </foreach>
    </insert>

    <select id="queryInfoByOrderSn"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        `member_plus_cashback`
        WHERE order_sn =#{orderSn} AND back_status=1 limit 1
    </select>

    <update id="updateBackStatus">
        <foreach collection="cashbacks" item="item" separator=";">
            UPDATE `member_plus_cashback`
            SET back_status = #{item.backStatus}, remark =#{item.remark}, rz_msg_id =#{item.rzMsgId},
            qxrz_msg_id =#{item.qxrzMsgId},update_time=now()
            WHERE id=#{item.id}
            AND back_status=1
        </foreach>
    </update>

    <update id="update">
        update member_plus_cashback set
        `back_status`= #{cashback.backStatus},
        <if test="cashback.remark != null">remark =#{cashback.remark},
        </if>
        <if test="cashback.qxrzMsgId != null">qxrz_msg_id =#{cashback.qxrzMsgId},
        </if>
         update_time=now()
        WHERE id=#{cashback.id} and back_status=1
    </update>
</mapper>
