<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusSendPlanConditionMapper">

    <resultMap id="MemberPlusSendPlanCondition"
            type="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusSendPlanConditionPo">
        <result column="id" property="id"/>
        <result column="member_plus_send_plan_id" property="memberPlusSendPlanId"/>
        <result column="condition_field" property="conditionField"/>
        <result column="reach_condition" property="reachCondition"/>
        <result column="reach_value" property="reachValue"/>
        <result column="is_reached" property="isReached"/>
        <result column="is_task_trigger" property="isTaskTrigger"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_plus_send_plan_id`,
        `condition_field`,
        `reach_condition`,
        `reach_value`,
        `is_reached`,
        `is_task_trigger`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="batchInsert"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO expire_member_plus_send_plan_condition (
        `id`,
        `member_plus_send_plan_id`,
        `condition_field`,
        `reach_condition`,
        `reach_value`,
        `is_reached`,
        `is_task_trigger`,
        `remark`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="l" separator=",">
            (
            #{l.id},
            #{l.memberPlusSendPlanId},
            #{l.conditionField},
            #{l.reachCondition},
            #{l.reachValue},
            #{l.isReached},
            #{l.isTaskTrigger},
            #{remark},
            NOW(),
            #{l.updateTime}
            )
        </foreach>
    </insert>

    <select id="listByMemberPlusSendPlanIds"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusSendPlanConditionPo">
        select
        <include refid="Base_Column_List"/>
        FROM expire_member_plus_send_plan_condition
        WHERE member_plus_send_plan_id in (
        <foreach collection="memberPlusSendPlanIds" index="index" item="memberPlusSendPlanId"
                separator=",">
            #{memberPlusSendPlanId}
        </foreach>
        )
        <if test="conditionField != null">
            and condition_field = #{conditionField}
        </if>
    </select>
</mapper>
