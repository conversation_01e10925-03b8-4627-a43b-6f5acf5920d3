<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberUseProductRecordMapper">

    <resultMap id="MemberUseProductRecord"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberUseProductRecordPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="program_id" property="programId"/>
        <result column="config_id" property="configId"/>
        <result column="type_id" property="typeId"/>
        <result column="product_id" property="productId"/>
        <result column="product_sku" property="productSku"/>
        <result column="use_state" property="useState"/>
        <result column="product_spu" property="productSpu"/>
        <result column="product_name" property="productName"/>
        <result column="order_money" property="orderMoney"/>
        <result column="remark" property="remark"/>
        <result column="order_time" property="orderTime"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `product_sku`,
        `channel_id`,
        `order_sn`,
        `plus_order_sn`,
        `type_id`,
        `model_id`,
        `program_id`,
        `config_id`,
        `product_id`,
        `use_state`,
        `product_spu`,
        `product_name`,
        `order_money`,
        `remark`,
        `order_time`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberUseProductRecord"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberUseProductRecordPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_use_product_record (`user_id`,
                                               `channel_id`,
                                               `order_sn`,
                                               `plus_order_sn`,
                                               `model_id`,
                                               `type_id`,
                                               `program_id`,
                                               `config_id`,
                                               `product_id`,
                                               `product_spu`,
                                               `product_sku`,
                                               `product_name`,
                                               `order_money`,
                                               `remark`,
                                               `order_time`,
                                               `create_time`,
                                               `update_time`)
        VALUES (#{userId},
                #{channelId},
                #{orderSn},
                #{plusOrderSn},
                #{modelId},
                #{typeId},
                #{programId},
                #{configId},
                #{productId},
                #{productSpu},
                #{productSku},
                #{productName},
                #{orderMoney},
                #{remark},
                #{orderTime},
                NOW(),
                #{updateTime})
    </insert>

    <update id="updateUseState"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberUseProductRecordPo">
        UPDATE member_use_product_record
        SET `use_state`= #{useState},
            `remark`= #{remark},
            `update_time` = now()
        WHERE `id` = #{id}
    </update>

    <select id="getRelationRecord" resultMap="MemberUseProductRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_use_product_record
        WHERE `order_sn` = #{orderSn} and `plus_order_sn` = #{plusOrderSn} and use_state = 0
        and model_id = #{modelId}
    </select>

    <select id="getLastUserRecord" resultMap="MemberUseProductRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_use_product_record
        WHERE `user_id` = #{userId} and use_state = 0 and model_id = #{modelId} order by id desc limit 1
    </select>

    <select id="getAllRecordByUser" resultMap="MemberUseProductRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_use_product_record
        WHERE `user_id` = #{userId} and `config_id` = #{configId} and order_time &gt; #{startTime}
        and order_time &lt; #{endTime} and use_state = 0 and model_id = #{modelId}
    </select>

    <select id="getAllRecordByPlusOrder" resultMap="MemberUseProductRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_use_product_record
        WHERE `plus_order_sn` = #{plusOrderSn} and use_state = 0 and model_id = #{modelId}
    </select>


    <select id="countRecordByOrder" resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM member_use_product_record
        WHERE `order_sn` = #{orderSn} and model_id = #{modelId}
    </select>
</mapper>
