<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusVirtualRecordMapper">

    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusVirtualRecordPo">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <result property="orderSn" column="order_sn" jdbcType="VARCHAR"/>
            <result property="sendPlanId" column="send_plan_id" jdbcType="INTEGER"/>
            <result property="userId" column="user_id" jdbcType="INTEGER"/>
            <result property="programId" column="program_id" jdbcType="INTEGER"/>
            <result property="modelId" column="model_id" jdbcType="INTEGER"/>
            <result property="productId" column="product_id" jdbcType="INTEGER"/>
            <result property="sku" column="sku" jdbcType="VARCHAR"/>
            <result property="discountRate" column="discount_rate" jdbcType="DECIMAL"/>
            <result property="minAmount" column="min_amount" jdbcType="DECIMAL"/>
            <result property="maxAmount" column="max_amount" jdbcType="DECIMAL"/>
            <result property="imgUrl" column="img_url" jdbcType="VARCHAR"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
            <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,order_sn,send_plan_id,user_id,
        program_id,model_id,
        product_id,sku,discount_rate,
        min_amount,max_amount,img_url,
        create_time,update_time
    </sql>

    <insert id="save"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusVirtualRecordPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_virtual_record (
            `order_sn`,
            `send_plan_id`,
            `user_id`,
            `program_id`,
            `model_id`,
            `product_id`,
            `sku`,
            `discount_rate`,
            `min_amount`,
            `max_amount`,
            `img_url`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{orderSn},
                  #{sendPlanId},
                  #{userId},
                  #{programId},
                  #{modelId},
                  #{productId},
                  #{sku},
                  #{discountRate},
                  #{minAmount},
                  #{maxAmount},
                  #{imgUrl},
                  NOW(),
                  NOW()
              )
    </insert>

    <select id="getVirtualRecord" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
            from member_plus_virtual_record
        where order_sn = #{orderSn} and user_id = #{userId} and model_id = #{modelId}
    </select>

</mapper>
