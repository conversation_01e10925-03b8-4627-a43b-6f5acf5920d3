<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusSendPlanConditionMapper">

    <resultMap id="MemberPlusSendPlanCondition"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo">
        <result column="id" property="id"/>
        <result column="member_plus_send_plan_id" property="memberPlusSendPlanId"/>
        <result column="condition_field" property="conditionField"/>
        <result column="reach_condition" property="reachCondition"/>
        <result column="reach_value" property="reachValue"/>
        <result column="is_reached" property="isReached"/>
        <result column="is_task_trigger" property="isTaskTrigger"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_plus_send_plan_id`,
        `condition_field`,
        `reach_condition`,
        `reach_value`,
        `is_reached`,
        `is_task_trigger`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusSendPlanCondition"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO member_plus_send_plan_condition (
              `member_plus_send_plan_id`,
              `condition_field`,
              `reach_condition`,
              `reach_value`,
              `is_reached`,
              `is_task_trigger`,
              `remark`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{memberPlusSendPlanId},
                #{conditionField},
                #{reachCondition},
                #{reachValue},
                #{isReached},
                #{isTaskTrigger},
                #{remark},
                  NOW(),
                #{updateTime}
      )
    </insert>


    <insert id="insertMemberPlusSendPlanCondition"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo">
        INSERT INTO member_plus_send_plan_condition (
            `member_plus_send_plan_id`,
            `condition_field`,
            `reach_condition`,
            `reach_value`,
            `is_reached`,
            `is_task_trigger`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
            #{memberPlusSendPlanCondition.memberPlusSendPlanId},
            #{memberPlusSendPlanCondition.conditionField},
            #{memberPlusSendPlanCondition.reachCondition},
            #{memberPlusSendPlanCondition.reachValue},
            #{memberPlusSendPlanCondition.isReached},
            #{memberPlusSendPlanCondition.isTaskTrigger},
            #{memberPlusSendPlanCondition.remark},
            NOW(),
            #{memberPlusSendPlanCondition.updateTime}
        )
    </insert>


    <delete id="deleteMemberPlusSendPlanCondition" parameterType="java.lang.Integer">
        DELETE FROM member_plus_send_plan_condition
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusSendPlanCondition"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo">
        UPDATE member_plus_send_plan_condition
        SET
        <if test="memberPlusSendPlanCondition.memberPlusSendPlanId != null">
            `member_plus_send_plan_id`= #{memberPlusSendPlanCondition.memberPlusSendPlanId},
        </if>
        <if test="memberPlusSendPlanCondition.conditionField != null">`condition_field`=
            #{memberPlusSendPlanCondition.conditionField},
        </if>
        <if test="memberPlusSendPlanCondition.reachCondition != null">`reach_condition`=
            #{memberPlusSendPlanCondition.reachCondition},
        </if>
        <if test="memberPlusSendPlanCondition.reachValue != null">`reach_value`=
            #{memberPlusSendPlanCondition.reachValue},
        </if>
        <if test="memberPlusSendPlanCondition.isReached != null">`is_reached`=
            #{memberPlusSendPlanCondition.isReached},
        </if>
        <if test="memberPlusSendPlanCondition.isTaskTrigger != null">`is_task_trigger`=
            #{memberPlusSendPlanCondition.isTaskTrigger},
        </if>
        <if test="memberPlusSendPlanCondition.remark != null">`remark`=
            #{memberPlusSendPlanCondition.remark},
        </if>
        update_time = now()
        WHERE `id` = #{memberPlusSendPlanCondition.id}
    </update>


    <select id="loadMemberPlusSendPlanCondition" parameterType="java.lang.Integer"
            resultMap="MemberPlusSendPlanCondition">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan_condition
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusSendPlanCondition">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan_condition
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_send_plan_condition
    </select>

    <select id="listByMemberPlusSendPlanIds"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo">
        select
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan_condition
        WHERE member_plus_send_plan_id in (
        <foreach collection="memberPlusSendPlanIds" index="index" item="memberPlusSendPlanId"
                separator=",">
            #{memberPlusSendPlanId}
        </foreach>
        )
        <if test="conditionField != null">
            and condition_field = #{conditionField}
        </if>
    </select>

    <select id="listBySendPlanId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo">
        select
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan_condition
        WHERE member_plus_send_plan_id = #{sendPlanId}
    </select>


    <select id="listBySendPlanIds"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanConditionPo">
        select
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan_condition
        WHERE member_plus_send_plan_id in (
        <foreach collection="sendPlanIds" item="sendPlanId" index="index" separator=",">
            #{sendPlanId}
        </foreach>
        )
    </select>

    <delete id="deleteByIds">
        DELETE FROM member_plus_send_plan_condition
        WHERE id in (
        <foreach collection="sendPlanIds" item="sendPlanId" index="index" separator=",">
            #{sendPlanId}
        </foreach>
        )
    </delete>
</mapper>
