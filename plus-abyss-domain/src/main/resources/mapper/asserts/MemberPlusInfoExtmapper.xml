<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusInfoExtMapper">

    <resultMap id="MemberPlusInfoExt" type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoExtPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="config_id" property="configId" />
        <result column="member_plus_id" property="memberPlusId" />
        <result column="renew_state" property="renewState" />
        <result column="remark" property="remark" />
        <result column="cancel_renew_time" property="cancelRenewTime" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `config_id`,
        `member_plus_id`,
        `renew_state`,
        `remark`,
        `cancel_renew_time`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusInfoExt" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoExtPo" useGeneratedKeys="true" keyProperty="id">
      INSERT INTO member_plus_info_ext (
             `user_id`,
             `config_id`,
              `member_plus_id`,
              `renew_state`,
              `remark`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{userId},
                #{configId},
                #{memberPlusId},
                #{renewState},
                #{remark},
                  NOW(),
                #{updateTime}
      )
    </insert>


    <insert id="insertMemberPlusInfoExt" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoExtPo">
        INSERT INTO member_plus_info_ext (
            `user_id`,
            `config_id`,
            `member_plus_id`,
            `renew_state`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
            #{memberPlusInfoExt.userId},
            #{memberPlusInfoExt.configId},
            #{memberPlusInfoExt.memberPlusId},
            #{memberPlusInfoExt.renewState},
            #{memberPlusInfoExt.remark},
            NOW(),
            #{memberPlusInfoExt.updateTime}
        )
    </insert>


    <delete id="deleteMemberPlusInfoExt" parameterType="java.lang.Integer">
        DELETE FROM member_plus_info_ext
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusInfoExt" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoExtPo" >
        UPDATE member_plus_info_ext
        SET
        <if test="memberPlusInfoExt.renewState != null">`renew_state`= #{memberPlusInfoExt.renewState},</if>
        <if test="memberPlusInfoExt.cancelRenewTime != null">`cancel_renew_time`= #{memberPlusInfoExt.cancelRenewTime},</if>
        <if test="memberPlusInfoExt.remark != null">`remark`= #{memberPlusInfoExt.remark},</if>
        update_time = now()
        WHERE `id` = #{memberPlusInfoExt.id}
    </update>


    <select id="loadMemberPlusInfoExt" parameterType="java.lang.Integer" resultMap="MemberPlusInfoExt">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_info_ext
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusInfoExt">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_info_ext
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_info_ext
    </select>

</mapper>