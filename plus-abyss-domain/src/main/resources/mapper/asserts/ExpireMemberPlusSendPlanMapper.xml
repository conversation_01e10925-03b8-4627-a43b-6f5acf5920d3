<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IExpireIMemberPlusSendPlanMapper">

    <resultMap id="MemberPlusSendPlan"
            type="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusSendPlanPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="program_id" property="programId"/>
        <result column="package_id" property="packageId"/>
        <result column="config_id" property="configId"/>
        <result column="model_id" property="modelId"/>
        <result column="member_plus_id" property="memberPlusId"/>
        <result column="periods_type" property="periodsType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="give_type" property="giveType"/>
        <result column="profit_type" property="profitType"/>
        <result column="profit_value" property="profitValue"/>
        <result column="send_status" property="sendStatus"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="max_send_time" property="maxSendTime"/>
        <result column="send_times" property="sendTimes"/>

    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `channel_id`,
        `order_sn`,
        `program_id`,
        `package_id`,
        `config_id`,
        `model_id`,
        `member_plus_id`,
        `periods_type`,
        `start_time`,
        `end_time`,
        `give_type`,
        `profit_type`,
        `profit_value`,
        `send_status`,
        `remark`,
        `create_time`,
        `update_time`,
        `max_send_time`,
        `send_times`
    </sql>

    <sql id="Base_Column_List_B">
        p.`id`,
        p.`member_id`,
        p.`channel_id`,
        p.`order_sn`,
        p.`program_id`,
        p.`package_id`,
        p.`config_id`,
        p.`model_id`,
        p.`member_plus_id`,
        p.`periods_type`,
        p.`start_time`,
        p.`end_time`,
        p.`give_type`,
        p.`profit_type`,
        p.`profit_value`,
        p.`send_status`,
        p.`remark`,
        p.`create_time`,
        p.`update_time`,
        p.`max_send_time`,
        p.`send_times`
    </sql>

    <insert id="batchInsert"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO expire_member_plus_send_plan (
        `id`,
        `member_id`,
        `channel_id`,
        `order_sn`,
        `program_id`,
        `package_id`,
        `config_id`,
        `model_id`,
        `member_plus_id`,
        `periods_type`,
        `start_time`,
        `end_time`,
        `give_type`,
        `profit_type`,
        `profit_value`,
        `send_status`,
        `remark`,
        `create_time`,
        `update_time`,
        `max_send_time`,
        `send_times`
        )
        VALUES
        <foreach collection="list" index="index" item="l" separator=",">
            (
            #{l.id},
            #{l.memberId},
            #{l.channelId},
            #{l.orderSn},
            #{l.programId},
            #{l.packageId},
            #{l.configId},
            #{l.modelId},
            #{l.memberPlusId},
            #{l.periodsType},
            #{l.startTime},
            #{l.endTime},
            #{l.giveType},
            #{l.profitType},
            #{l.profitValue},
            #{l.sendStatus},
            #{remark},
            NOW(),
            #{l.updateTime},
            #{l.maxSendTime},
            #{l.sendTimes}
            )
        </foreach>
    </insert>

    <select id="listByOrderSn" resultMap="MemberPlusSendPlan">
        select <include refid="Base_Column_List"/> from expire_member_plus_send_plan
        where order_sn = #{orderSn}
    </select>
</mapper>