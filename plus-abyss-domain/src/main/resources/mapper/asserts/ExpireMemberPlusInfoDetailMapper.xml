<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusInfoDetailMapper">

    <resultMap id="ExpireMemberPlusInfoDetail"
            type="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusInfoDetailPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="plus_info_id" property="plusInfoId"/>
        <result column="config_id" property="configId"/>
        <result column="config_name" property="configName"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="periods" property="periods"/>
        <result column="period_type" property="periodType"/>
        <result column="jx_status" property="jxStatus"/>
        <result column="jx_start_time" property="jxStartTime"/>
        <result column="jx_end_time" property="jxEndTime"/>
        <result column="program_id" property="programId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_type" property="plusType"/>
        <result column="channel_id" property="channelId"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `plus_info_id`,
        `config_id`,
        `config_name`,
        `create_time`,
        `update_time`,
        `periods`,
        `period_type`,
        `jx_status`,
        `jx_start_time`,
        `jx_end_time`,
        `program_id`,
        `order_sn`,
        `plus_type`,
        `channel_id`
    </sql>

    <sql id="getConditionNew">
        <where>
            <if test="queryParam.jxStartTime != null and '' != queryParam.jxStartTime">
                and p.jx_start_time &gt;= #{queryParam.jxStartTime}
            </if>
            <if test="queryParam.jxStartEndTime != null and '' !=queryParam.jxStartEndTime">
                and p.jx_start_time &lt;= #{queryParam.jxStartEndTime}
            </if>
            <if test="queryParam.jxStartEndTime != null and '' != queryParam.jxEndStartTime">
                and p.jx_end_time &gt;= #{queryParam.jxEndStartTime}
            </if>
            <if test="queryParam.jxStartEndTime != null and '' !=queryParam.jxStartEndTime">
                and p.jx_end_time &lt;= #{queryParam.jxStartEndTime}
            </if>
            <if test="queryParam.jxStatus != null ">
                and p.`jx_status` = #{queryParam.jxStatus}
            </if>
            <if test="queryParam.configId != null ">
                and p.`config_id` = #{queryParam.configId}
            </if>
            <if test="queryParam.userId != null ">
                and p.`user_id` = #{queryParam.userId}
            </if>
            <if test="queryParam.channelId != null">
                and p.`channel_id` = #{queryParam.channelId}
            </if>
        </where>
    </sql>

    <insert id="batchInsertInfo">
        INSERT INTO expire_member_plus_info_detail (
        `user_id`,
        `channel_id`,
        `plus_info_id`,
        `config_id`,
        `config_name`,
        `create_time`,
        `update_time`,
        `periods`,
        `period_type`,
        `jx_status`,
        `jx_start_time`,
        `jx_end_time`,
        `program_id`,
        `order_sn`,
        `plus_type`
        )
        VALUES
        <foreach collection="list" index="index" item="detail" separator=",">
            (
            #{detail.userId},
            #{detail.channelId},
            #{detail.plusInfoId},
            #{detail.configId},
            #{detail.configName},
            #{detail.createTime},
            #{detail.updateTime},
            #{detail.periods},
            #{detail.periodType},
            #{detail.jxStatus},
            #{detail.jxStartTime},
            #{detail.jxEndTime},
            #{detail.programId},
            #{detail.orderSn},
            #{detail.plusType}
            )
        </foreach>
    </insert>

    <select id="getExpireInfoByOrders"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusInfoDetailPo">
        SELECT jx_status,order_sn
        FROM expire_member_plus_info_detail
        <where>
            <foreach collection="orderSns" item="orderSn" open=" order_sn  in  (" close=")" separator=",">
                #{orderSn}
            </foreach>
        </where>
    </select>

    <select id="getExpireMemberPlusInfoById"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM expire_member_plus_info_detail
        WHERE id=#{id}
        and user_id=#{userId}
        and channel_id=#{channel}
    </select>

    <select id="getExpireMemberPlusInfoDetail"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusInfoDetailPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM expire_member_plus_info_detail
        WHERE user_id=#{memberId}
        and order_sn=#{orderSn}
    </select>

</mapper>
