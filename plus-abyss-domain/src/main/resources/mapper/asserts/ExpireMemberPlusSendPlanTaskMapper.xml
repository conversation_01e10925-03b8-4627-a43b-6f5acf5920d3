<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusSendPlanTaskMapper">

    <resultMap id="ExpireMemberPlusSendPlanTask"
            type="com.juzifenqi.plus.module.asserts.repository.po.ExpireMemberPlusSendPlanTaskPo">
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="model_id" property="modelId" />
        <result column="plan_time" property="planTime" />
        <result column="profit_type" property="profitType" />
        <result column="profit_value" property="profitValue" />
        <result column="member_plus_send_plan_id" property="memberPlusSendPlanId" />
        <result column="task_state" property="taskState" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `model_id`,
        `plan_time`,
        `profit_type`,
        `profit_value`,
        `member_plus_send_plan_id`,
        `task_state`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="batchInsert">
        INSERT INTO expire_member_plus_send_plan_task (
        `id`,
        `order_sn`,
        `model_id`,
        `plan_time`,
        `profit_type`,
        `profit_value`,
        `member_plus_send_plan_id`,
        `task_state`,
        `remark`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="detail" separator=",">
            (
            #{detail.id},
            #{detail.orderSn},
            #{detail.modelId},
            #{detail.planTime},
            #{detail.profitType},
            #{detail.profitValue},
            #{detail.memberPlusSendPlanId},
            #{detail.taskState},
            #{detail.remark},
            #{detail.createTime},
            #{detail.updateTime}
            )
        </foreach>
    </insert>
</mapper>