<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IExpireMemberPlusInfoMapper">

    <resultMap id="ExpireMemberPlusInfo"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="buy_type" property="buyType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="periods" property="periods"/>
        <result column="period_type" property="periodType"/>
        <result column="jx_status" property="jxStatus"/>
        <result column="jx_start_time" property="jxStartTime"/>
        <result column="jx_end_time" property="jxEndTime"/>
        <result column="config_id" property="configId"/>
        <result column="channel_id" property="channelId"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `buy_type`,
        `create_time`,
        `update_time`,
        `periods`,
        `period_type`,
        `jx_status`,
        `jx_start_time`,
        `jx_end_time`,
        `config_id`
    </sql>

    <insert id="batchExpireMemberPlusInfo"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        INSERT INTO expire_member_plus_info (
            `user_id`,
            `channel_id`,
            `buy_type`,
            `create_time`,
            `update_time`,
            `periods`,
            `period_type`,
            `jx_status`,
            `jx_start_time`,
            `jx_end_time`,
            `config_id`
        )
        VALUES
        <foreach collection="list" index="index" item="expireMemberPlusInfo" separator=",">
        ( #{expireMemberPlusInfo.userId},
            #{expireMemberPlusInfo.channelId},
            #{expireMemberPlusInfo.buyType},
            #{expireMemberPlusInfo.createTime},
            #{expireMemberPlusInfo.updateTime},
            #{expireMemberPlusInfo.periods},
            #{expireMemberPlusInfo.periodType},
            #{expireMemberPlusInfo.jxStatus},
            #{expireMemberPlusInfo.jxStartTime},
            #{expireMemberPlusInfo.jxEndTime},
            #{expireMemberPlusInfo.configId}
        )
        </foreach>
    </insert>

</mapper>
