<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusCashbackRecordMapper">

    <resultMap id="MemberPlusCashbackRecord"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackRecordPo">
        <result column="id" property="id"/>
        <result column="order_sn" property="orderSn"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="model_id" property="modelId"/>
        <result column="cashback_bill_id" property="cashbackBillId"/>
        <result column="cashback_amount" property="cashbackAmount"/>
        <result column="category" property="category"/>
        <result column="pay_status" property="payStatus"/>
        <result column="pay_fail_msg" property="payFailMsg"/>
        <result column="transaction_id" property="transactionId"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="config_id" property="configId"/>
        <result column="periods" property="periods"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `plus_order_sn`,
        `user_id`,
        `channel_id`,
        `model_id`,
        `cashback_bill_id`,
        `cashback_amount`,
        `category`,
        `pay_status`,
        `pay_fail_msg`,
        `transaction_id`,
        `remark`,
        `create_time`,
        `update_time`,
        `config_id`,
        `periods`
    </sql>

    <insert id="saveMemberPlusCashbackRecord"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusCashbackRecordPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_cashback_record (`order_sn`,
                                                 `plus_order_sn`,
                                                 `user_id`,
                                                 `channel_id`,
                                                 `model_id`,
                                                 `cashback_bill_id`,
                                                 `cashback_amount`,
                                                 `category`,
                                                 `pay_status`,
                                                 `pay_fail_msg`,
                                                 `transaction_id`,
                                                 `remark`,
                                                 `create_time`,
                                                 `update_time`,
                                                 `config_id`,
                                                 `periods`)
        VALUES (#{orderSn},
                #{plusOrderSn},
                #{userId},
                #{channelId},
                #{modelId},
                #{cashbackBillId},
                #{cashbackAmount},
                #{category},
                #{payStatus},
                #{payFailMsg},
                #{transactionId},
                #{remark},
                NOW(),
                #{updateTime},
                #{configId},
                #{periods})
    </insert>

    <select id="loadMemberPlusCashbackRecord" parameterType="java.lang.Integer"
            resultMap="MemberPlusCashbackRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_cashback_record
        WHERE `id` = #{id}
    </select>

    <select id="getRecordListByOrderSn" parameterType="java.util.Map"
            resultMap="MemberPlusCashbackRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_cashback_record
        where `plus_order_sn` = #{plusOrderSn} and model_id = #{modelId}
    </select>


    <sql id="whereConditions">
        <where>
            <if test="queryParam.loanOrderSn != null and queryParam.loanOrderSn != ''">
                and `order_sn` = #{queryParam.loanOrderSn}
            </if>
            <if test="queryParam.plusOrderSn != null and queryParam.plusOrderSn != ''">
                and `plus_order_sn` = #{queryParam.plusOrderSn}
            </if>
            <if test="queryParam.userId != null">
                and `user_id` = #{queryParam.userId}
            </if>
        </where>
    </sql>

    <select id="queryCashBackRecordList" parameterType="java.util.Map"
            resultMap="MemberPlusCashbackRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_cashback_record
        <include refid="whereConditions"/>
        order by create_time desc
        LIMIT #{pageStart}, #{pagesize}
    </select>

    <select id="queryCashBackRecordCount" parameterType="java.util.Map"
            resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_cashback_record
        <include refid="whereConditions"/>
    </select>

    <select id="sumByPlusOrderList" resultType="java.math.BigDecimal">
        SELECT sum(cashback_amount) as cashback_amount
        FROM member_plus_cashback_record
        where
        plus_order_sn in
        <foreach collection="plusOrderList" item="plusOrderSn" index="index" open="(" separator=","
                close=")">
            #{plusOrderSn}
        </foreach>
        and category = #{category}
    </select>

    <update id="updateRecordPayStatus">
        UPDATE member_plus_cashback_record
        SET `pay_status`= #{payStatus},
            pay_fail_msg = #{payFailMsg},
            update_time = now()
        WHERE `id` = #{id}
    </update>

    <select id="getRecordByTransactionId" parameterType="java.util.Map" resultMap="MemberPlusCashbackRecord">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_cashback_record
        where `transaction_id` = #{transactionId} order by id desc limit 1
    </select>
</mapper>
