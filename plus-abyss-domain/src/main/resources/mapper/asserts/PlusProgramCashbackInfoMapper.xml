<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IPlusProgramCashbackInfoMapper">

    <!-- 通用查询映射结果 -->
    <resultMap id="BaseResultMap"
            type="com.juzifenqi.plus.module.asserts.repository.po.PlusProgramCashbackInfoPo">
        <id column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="program_id" property="programId"/>
        <result column="plus_order_sn" property="plusOrderSn"/>
        <result column="open_store_fail_msg" property="openStoreFailMsg"/>
        <result column="open_store_retry_count" property="openStoreRetryCount"/>
        <result column="open_store" property="openStore"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <!-- 通用查询结果列 -->
    <sql id="Base_Column_List">
        id
        , user_id, channel_id, config_id, program_id, plus_order_sn, open_store_fail_msg, open_store,open_store_retry_count, create_time, update_time
    </sql>


    <insert id="insert"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.PlusProgramCashbackInfoPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_program_cashback_info (`user_id`,
                                                `channel_id`,
                                                `config_id`,
                                                `program_id`,
                                                `plus_order_sn`,
                                                `open_store`,
                                                `open_store_fail_msg`,
                                                `open_store_retry_count`,
                                                `create_time`,
                                                `update_time`)
        VALUES (#{userId},
                #{channelId},
                #{configId},
                #{programId},
                #{plusOrderSn},
                #{openStore},
                #{openStoreFailMsg},
                #{openStoreRetryCount},
                NOW(),
                #{updateTime})
    </insert>

    <select id="queryByPlusOrderSn" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `plus_program_cashback_info`
        WHERE plus_order_sn = #{plusOrderSn}
        AND user_id = #{userId} order by id desc limit 1
    </select>

    <select id="selectJobList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `plus_program_cashback_info`
        WHERE open_store = 2 and create_time > #{time} and open_store_retry_count &lt;3 order by id
        limit 20
    </select>

    <update id="update"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.PlusProgramCashbackInfoPo">
        update plus_program_cashback_info
        set open_store            = #{openStore},
            open_store_retry_count=#{openStoreRetryCount},
            update_time           = now()
        where id = #{id}
    </update>
</mapper>
