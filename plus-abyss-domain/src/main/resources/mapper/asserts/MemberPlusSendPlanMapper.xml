<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusSendPlanMapper">

    <resultMap id="MemberPlusSendPlan"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="channel_id" property="channelId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="program_id" property="programId"/>
        <result column="package_id" property="packageId"/>
        <result column="config_id" property="configId"/>
        <result column="model_id" property="modelId"/>
        <result column="member_plus_id" property="memberPlusId"/>
        <result column="periods_type" property="periodsType"/>
        <result column="start_time" property="startTime"/>
        <result column="end_time" property="endTime"/>
        <result column="give_type" property="giveType"/>
        <result column="profit_type" property="profitType"/>
        <result column="profit_value" property="profitValue"/>
        <result column="send_status" property="sendStatus"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <result column="max_send_time" property="maxSendTime"/>
        <result column="send_times" property="sendTimes"/>

    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `channel_id`,
        `order_sn`,
        `program_id`,
        `package_id`,
        `config_id`,
        `model_id`,
        `member_plus_id`,
        `periods_type`,
        `start_time`,
        `end_time`,
        `give_type`,
        `profit_type`,
        `profit_value`,
        `send_status`,
        `remark`,
        `create_time`,
        `update_time`,
        `max_send_time`,
        `send_times`
    </sql>

    <sql id="Base_Column_List_B">
        p.`id`,
        p.`member_id`,
        p.`channel_id`,
        p.`order_sn`,
        p.`program_id`,
        p.`package_id`,
        p.`config_id`,
        p.`model_id`,
        p.`member_plus_id`,
        p.`periods_type`,
        p.`start_time`,
        p.`end_time`,
        p.`give_type`,
        p.`profit_type`,
        p.`profit_value`,
        p.`send_status`,
        p.`remark`,
        p.`create_time`,
        p.`update_time`,
        p.`max_send_time`,
        p.`send_times`
    </sql>

    <insert id="saveMemberPlusSendPlan"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_send_plan (
        `member_id`,
        `channel_id`,
        `order_sn`,
        `program_id`,
        `package_id`,
        `config_id`,
        `model_id`,
        `member_plus_id`,
        `periods_type`,
        `start_time`,
        `end_time`,
        `give_type`,
        `profit_type`,
        `profit_value`,
        `send_status`,
        `remark`,
        `create_time`,
        `update_time`,
        `max_send_time`,
        `send_times`
        )
        VALUES(
        #{memberId},
        #{channelId},
        #{orderSn},
        #{programId},
        #{packageId},
        #{configId},
        #{modelId},
        #{memberPlusId},
        #{periodsType},
        #{startTime},
        #{endTime},
        #{giveType},
        #{profitType},
        #{profitValue},
        #{sendStatus},
        #{remark},
        NOW(),
        #{updateTime},
        #{maxSendTime},
        #{sendTimes}
        )
    </insert>


    <insert id="insertMemberPlusSendPlan"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        INSERT INTO member_plus_send_plan (
        `member_id`,
        `channel_id`,
        `order_sn`,
        `program_id`,
        `package_id`,
        `config_id`,
        `model_id`,
        `member_plus_id`,
        `periods_type`,
        `start_time`,
        `end_time`,
        `give_type`,
        `profit_type`,
        `profit_value`,
        `send_status`,
        `remark`,
        `create_time`,
        `update_time`,
        `max_send_time`,
        `send_times`
        )
        VALUES(
        #{memberPlusSendPlan.memberId},
        #{memberPlusSendPlan.channelId},
        #{memberPlusSendPlan.orderSn},
        #{memberPlusSendPlan.programId},
        #{memberPlusSendPlan.packageId},
        #{memberPlusSendPlan.configId},
        #{memberPlusSendPlan.modelId},
        #{memberPlusSendPlan.memberPlusId},
        #{memberPlusSendPlan.periodsType},
        #{memberPlusSendPlan.startTime},
        #{memberPlusSendPlan.endTime},
        #{memberPlusSendPlan.giveType},
        #{memberPlusSendPlan.profitType},
        #{memberPlusSendPlan.profitValue},
        #{memberPlusSendPlan.sendStatus},
        #{memberPlusSendPlan.remark},
        NOW(),
        #{memberPlusSendPlan.updateTime},
        #{memberPlusSendPlan.maxSendTime},
        #{memberPlusSendPlan.sendTimes}
        )
    </insert>


    <delete id="deleteMemberPlusSendPlan" parameterType="java.lang.Integer">
        DELETE FROM member_plus_send_plan
        WHERE `id` = #{id}
    </delete>

    <delete id="deleteByIds">
        DELETE FROM member_plus_send_plan
        WHERE id in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <update id="updateMemberPlusSendPlan"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        UPDATE member_plus_send_plan
        SET
        <if test="memberPlusSendPlan.memberId != null">`member_id`=
            #{memberPlusSendPlan.memberId},
        </if>
        <if test="memberPlusSendPlan.channelId != null">`channel_id`=
            #{memberPlusSendPlan.channelId},
        </if>
        <if test="memberPlusSendPlan.orderSn != null">`order_sn`= #{memberPlusSendPlan.orderSn},
        </if>
        <if test="memberPlusSendPlan.programId != null">`program_id`=
            #{memberPlusSendPlan.programId},
        </if>
        <if test="memberPlusSendPlan.packageId != null">`package_id`=
            #{memberPlusSendPlan.packageId},
        </if>
        <if test="memberPlusSendPlan.configId != null">`config_id`=
            #{memberPlusSendPlan.configId},
        </if>
        <if test="memberPlusSendPlan.modelId != null">`model_id`= #{memberPlusSendPlan.modelId},
        </if>
        <if test="memberPlusSendPlan.memberPlusId != null">`member_plus_id`=
            #{memberPlusSendPlan.memberPlusId},
        </if>
        <if test="memberPlusSendPlan.periodsType != null">`periods_type`=
            #{memberPlusSendPlan.periodsType},
        </if>
        <if test="memberPlusSendPlan.startTime != null">`start_time`=
            #{memberPlusSendPlan.startTime},
        </if>
        <if test="memberPlusSendPlan.endTime != null">`end_time`= #{memberPlusSendPlan.endTime},
        </if>
        <if test="memberPlusSendPlan.giveType != null">`give_type`=
            #{memberPlusSendPlan.giveType},
        </if>
        <if test="memberPlusSendPlan.profitType != null">`profit_type`=
            #{memberPlusSendPlan.profitType},
        </if>
        <if test="memberPlusSendPlan.profitValue != null">`profit_value`=
            #{memberPlusSendPlan.profitValue},
        </if>
        <if test="memberPlusSendPlan.sendStatus != null">`send_status`=
            #{memberPlusSendPlan.sendStatus},
        </if>
        <if test="memberPlusSendPlan.remark != null">`remark`= #{memberPlusSendPlan.remark},</if>
        <if test="memberPlusSendPlan.maxSendTime != null">`max_send_time`=
            #{memberPlusSendPlan.maxSendTime},
        </if>
        <if test="memberPlusSendPlan.sendTimes != null">`send_times`=
            #{memberPlusSendPlan.sendTimes},
        </if>

        update_time = now()
        WHERE `id` = #{memberPlusSendPlan.id}
    </update>


    <select id="loadMemberPlusSendPlan" parameterType="java.lang.Integer"
            resultMap="MemberPlusSendPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        WHERE `id` = #{id}
    </select>

    <select id="getMemberPlusSendPlanByUser" parameterType="java.lang.Integer"
            resultMap="MemberPlusSendPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        WHERE `id` = #{id} and `member_id` = #{userId}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusSendPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_send_plan
    </select>

    <select id="listByOrderSnAndSendStatus"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        WHERE order_sn in (
        <foreach collection="orderList" index="index" item="orderSn"
                separator=",">
            #{orderSn}
        </foreach>
        )
        <if test="sendStatus != null">
            and send_status = #{sendStatus}
        </if>
        and model_id = #{modelId}
    </select>


    <select id="listCurrentDmdsByOrderSnAndSendStatus"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        SELECT
        <include refid="Base_Column_List_B"/>
        FROM member_plus_send_plan p left join member_plus_send_plan_ext e on p.id =
        e.member_plus_send_plan_id
        WHERE p.order_sn in (
        <foreach collection="orderList" index="index" item="orderSn"
                separator=",">
            #{orderSn}
        </foreach>
        )
        and p.send_status = #{sendStatus}
        and p.model_id = #{modelId}
        and p.start_time &lt; now()
        and p.end_time &gt;= now()
        order by e.order_num asc limit 1
    </select>

    <select id="listCurrentJjpByOrderSnAndSendStatus"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        SELECT
        <include refid="Base_Column_List_B"/>
        FROM member_plus_send_plan p left join member_plus_send_plan_ext e on p.id =
        e.member_plus_send_plan_id
        WHERE p.order_sn in (
        <foreach collection="orderList" index="index" item="orderSn"
                separator=",">
            #{orderSn}
        </foreach>
        )
        and p.send_status = #{sendStatus}
        and p.model_id = #{modelId}
        and e.reject_type = #{rejectType}
        and p.start_time &lt; now()
        and p.end_time &gt;= now()
        order by p.id
    </select>


    <select id="listGwByOrderSnAndSendStatus"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        SELECT
        <include refid="Base_Column_List_B"/>
        FROM member_plus_send_plan p left join member_plus_send_plan_ext e on p.id =
        e.member_plus_send_plan_id
        WHERE p.order_sn in (
        <foreach collection="orderList" index="index" item="orderSn"
                separator=",">
            #{orderSn}
        </foreach>
        )
        and p.send_status = #{sendStatus}
        and p.model_id = #{modelId}
        order by e.order_num limit 1
    </select>

    <select id="listByPlanIds"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        WHERE id in (
        <foreach collection="planIds" index="index" item="id"
                separator=",">
            #{id}
        </foreach>
        )
        and send_status = #{sendStatus}
    </select>

    <select id="listByIds"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        WHERE id in (
        <foreach collection="ids" index="index" item="id"
                separator=",">
            #{id}
        </foreach>
        )
    </select>

    <update id="batchUpdateSendStatus">
        update member_plus_send_plan set send_status = #{sendStatus},update_time=now() where id in
        (
        <foreach collection="ids" index="index" item="id"
                separator=",">
            #{id}
        </foreach>
        )
    </update>

    <select id="listByOrderSnAndModelId" resultMap="MemberPlusSendPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        where order_sn = #{orderSn} and model_id = #{modelId}
    </select>

    <select id="querySendPlanList" resultMap="MemberPlusSendPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        where member_id = #{req.userId}
          and model_id = #{req.modelId}
        <if test="req.orderSn != null and req.orderSn != '' ">
            and order_sn = #{req.orderSn}
        </if>
        <if test="req.configId != null and req.configId > 0">
            and config_id = #{req.configId}
        </if>
        <if test="req.sendStatuses != null and req.sendStatuses.size > 0">
            and send_status in (
            <foreach collection="req.sendStatuses" index="index" item="sendStatus"
                    separator=",">
                #{sendStatus}
            </foreach>
            )
        </if>
        order by id asc
    </select>

    <select id="listCurrentHkfx"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanPo">
        SELECT
        <include refid="Base_Column_List_B"/>
        FROM member_plus_send_plan p left join member_plus_send_plan_ext e on p.id =
        e.member_plus_send_plan_id
        WHERE p.order_sn in (
        <foreach collection="orderList" index="index" item="orderSn"
                separator=",">
            #{orderSn}
        </foreach>
        )
        and p.send_status = #{sendStatus}
        and p.model_id = #{modelId}
        and p.start_time &lt; now()
        and p.end_time &gt;= now()
        and e.order_num = #{periods}
    </select>

    <select id="listByOrderSnExcludeModelIds" resultMap="MemberPlusSendPlan">
        select
        <include refid="Base_Column_List"/>
        from member_plus_send_plan where order_sn = #{orderSn} and model_id not in
        (
        <foreach collection="modelIds" index="index" item="modelId"
                separator=",">
            #{modelId}
        </foreach>
        )
    </select>

    <select id="listByOrderSnsExcludeModelIds" resultMap="MemberPlusSendPlan">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_send_plan
        where order_sn in
        (
        <foreach collection="orderSns" index="index" item="orderSn"
                separator=",">
            #{orderSn}
        </foreach>
        ) and model_id not in
        (
        <foreach collection="modelIds" index="index" item="modelId"
                separator=",">
            #{modelId}
        </foreach>
        )
    </select>

    <select id="listByOrderSnAndModelIds" resultMap="MemberPlusSendPlan">
        select <include refid="Base_Column_List"/>
        from member_plus_send_plan
        where order_sn = #{orderSn}
        <if test="modelIdList != null and modelIdList.size > 0">
            and model_id in
            <foreach collection="modelIdList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
    </select>

</mapper>