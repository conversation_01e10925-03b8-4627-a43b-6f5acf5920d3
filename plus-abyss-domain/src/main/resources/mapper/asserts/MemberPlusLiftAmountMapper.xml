<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusLiftAmountMapper">

    <resultMap id="MemberPlusLiftAmount" type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLiftAmountPo" >
        <result column="id" property="id" />
        <result column="member_id" property="memberId" />
        <result column="channel_id" property="channelId" />
        <result column="plus_order_sn" property="plusOrderSn" />
        <result column="order_type" property="orderType" />
        <result column="config_id" property="configId" />
        <result column="program_id" property="programId" />
        <result column="send_plan_id" property="sendPlanId" />
        <result column="model_id" property="modelId" />
        <result column="plus_lift_id" property="plusLiftId" />
        <result column="grade" property="grade" />
        <result column="periods" property="periods" />
        <result column="start_time" property="startTime" />
        <result column="end_time" property="endTime" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `member_id`,
        `channel_id`,
        `plus_order_sn`,
        `order_type`,
        `config_id`,
        `program_id`,
        `send_plan_id`,
        `model_id`,
        `plus_lift_id`,
        `grade`,
        `periods`,
        `start_time`,
        `end_time`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusLiftAmount" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLiftAmountPo" useGeneratedKeys="true" keyProperty="id">
      INSERT INTO member_plus_lift_amount (
              `member_id`,
              `channel_id`,
              `plus_order_sn`,
              `order_type`,
              `config_id`,
              `program_id`,
              `send_plan_id`,
              `model_id`,
              `plus_lift_id`,
              `grade`,
              `periods`,
              `start_time`,
              `end_time`,
              `remark`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{memberId},
                #{channelId},
                #{plusOrderSn},
                #{orderType},
                #{configId},
                #{programId},
                #{sendPlanId},
                #{modelId},
                #{plusLiftId},
                #{grade},
                #{periods},
                #{startTime},
                #{endTime},
                #{remark},
                  NOW(),
                #{updateTime}
      )
    </insert>


    <insert id="insertMemberPlusLiftAmount" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLiftAmountPo">
        INSERT INTO member_plus_lift_amount (
            `member_id`,
            `channel_id`,
            `plus_order_sn`,
            `order_type`,
            `config_id`,
            `program_id`,
            `send_plan_id`,
            `model_id`,
            `plus_lift_id`,
            `grade`,
            `periods`,
            `start_time`,
            `end_time`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
            #{memberPlusLiftAmount.memberId},
            #{memberPlusLiftAmount.channelId},
            #{memberPlusLiftAmount.plusOrderSn},
            #{memberPlusLiftAmount.orderType},
            #{memberPlusLiftAmount.configId},
            #{memberPlusLiftAmount.programId},
            #{memberPlusLiftAmount.sendPlanId},
            #{memberPlusLiftAmount.modelId},
            #{memberPlusLiftAmount.plusLiftId},
            #{memberPlusLiftAmount.grade},
            #{memberPlusLiftAmount.periods},
            #{memberPlusLiftAmount.startTime},
            #{memberPlusLiftAmount.endTime},
            #{memberPlusLiftAmount.remark},
            NOW(),
            #{memberPlusLiftAmount.updateTime}
        )
    </insert>


    <delete id="deleteMemberPlusLiftAmount" parameterType="java.lang.Integer">
        DELETE FROM member_plus_lift_amount
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusLiftAmount" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusLiftAmountPo" >
        UPDATE member_plus_lift_amount
        SET
        <if test="memberPlusLiftAmount.memberId != null">`member_id`= #{memberPlusLiftAmount.memberId},</if>
        <if test="memberPlusLiftAmount.channelId != null">`channel_id`= #{memberPlusLiftAmount.channelId},</if>
        <if test="memberPlusLiftAmount.plusOrderSn != null">`plus_order_sn`= #{memberPlusLiftAmount.plusOrderSn},</if>
        <if test="memberPlusLiftAmount.orderType != null">`order_type`= #{memberPlusLiftAmount.orderType},</if>
        <if test="memberPlusLiftAmount.configId != null">`config_id`= #{memberPlusLiftAmount.configId},</if>
        <if test="memberPlusLiftAmount.programId != null">`program_id`= #{memberPlusLiftAmount.programId},</if>
        <if test="memberPlusLiftAmount.sendPlanId != null">`send_plan_id`= #{memberPlusLiftAmount.sendPlanId},</if>
        <if test="memberPlusLiftAmount.modelId != null">`model_id`= #{memberPlusLiftAmount.modelId},</if>
        <if test="memberPlusLiftAmount.plusLiftId != null">`plus_lift_id`= #{memberPlusLiftAmount.plusLiftId},</if>
        <if test="memberPlusLiftAmount.grade != null">`grade`= #{memberPlusLiftAmount.grade},</if>
        <if test="memberPlusLiftAmount.periods != null">`periods`= #{memberPlusLiftAmount.periods},</if>
        <if test="memberPlusLiftAmount.startTime != null">`start_time`= #{memberPlusLiftAmount.startTime},</if>
        <if test="memberPlusLiftAmount.endTime != null">`end_time`= #{memberPlusLiftAmount.endTime},</if>
        <if test="memberPlusLiftAmount.remark != null">`remark`= #{memberPlusLiftAmount.remark},</if>
        update_time = now()
        WHERE `id` = #{memberPlusLiftAmount.id}
    </update>


    <select id="loadMemberPlusLiftAmount" parameterType="java.lang.Integer" resultMap="MemberPlusLiftAmount">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_lift_amount
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusLiftAmount">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_lift_amount
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_lift_amount
    </select>

</mapper>
