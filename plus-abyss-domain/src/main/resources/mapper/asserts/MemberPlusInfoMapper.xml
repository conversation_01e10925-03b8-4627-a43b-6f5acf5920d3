<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusInfoMapper">
    <resultMap id="BaseResultMap"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        <id column="id" property="id" jdbcType="INTEGER"/>
        <result column="user_id" property="userId" jdbcType="INTEGER"/>
        <result column="channel_id" property="channelId" jdbcType="INTEGER"/>
        <result column="config_id" property="configId" jdbcType="INTEGER"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <result column="periods" property="periods" jdbcType="VARCHAR"/>
        <result column="period_type" property="periodType" jdbcType="VARCHAR"/>
        <result column="memberPlusType" property="memberPlusType" jdbcType="VARCHAR"/>
        <result column="buy_type" property="buyType" jdbcType="INTEGER"/>
        <result column="jx_start_time" property="jxStartTime" jdbcType="TIMESTAMP"/>
        <result column="jx_end_time" property="jxEndTime" jdbcType="TIMESTAMP"/>
        <result column="jx_status" property="jxStatus" jdbcType="INTEGER"/>
    </resultMap>
    <sql id="Base_Column_List">
        id
        ,user_id, channel_id, config_id, create_time,
        update_time,periods,period_type,buy_type,jx_status,
        jx_start_time,jx_end_time
    </sql>

    <select id="selectByMemberId" resultMap="BaseResultMap"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info
        where user_id = #{userId}
        and channel_id = #{channelId}
        and jx_status = 1
        order by id desc limit 1
    </select>

    <select id="selectById" resultMap="BaseResultMap" parameterType="Integer">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info
        where id = #{id}
    </select>

    <select id="getJxExpiredMemberIds" resultType="integer">
        select
        id
        from member_plus_info
        where jx_status = 1 and jx_end_time &lt; now()
        limit 100;
    </select>

    <select id="getListByIds" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info
        where id in
        <foreach collection="ids" item="id" index="index" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <insert id="insertSelective"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo" useGeneratedKeys="true" keyProperty="id">
        insert into member_plus_info
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">
                user_id,
            </if>
            <if test="channelId != null">
                channel_id,
            </if>
            <if test="configId != null">
                config_id,
            </if>

            <if test="periods != null">
                periods,
            </if>
            <if test="periodType != null">
                period_type,
            </if>
            <if test="buyType != null">
                buy_type,
            </if>
            <if test="jxStatus != null">
                jx_status,
            </if>
            <if test="jxStartTime != null">
                jx_start_time,
            </if>
            <if test="jxEndTime != null">
                jx_end_time,
            </if>
            create_time,
            update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">

            <if test="userId != null">
                #{userId},
            </if>
            <if test="channelId != null">
                #{channelId},
            </if>
            <if test="configId != null">
                #{configId},
            </if>

            <if test="periods != null">
                #{periods},
            </if>
            <if test="periodType != null">
                #{periodType},
            </if>
            <if test="buyType != null">
                #{buyType},
            </if>
            <if test="jxStatus != null">
                #{jxStatus},
            </if>
            <if test="jxStartTime != null">
                #{jxStartTime},
            </if>
            <if test="jxEndTime != null">
                #{jxEndTime},
            </if>
            now(),now()
        </trim>
    </insert>

    <update id="updateByMemberId"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        update member_plus_info
        set
        <if test="jxStatus != null">
            jx_status = #{jxStatus},
        </if>
        <if test="jxEndTime != null">
            jx_end_time = #{jxEndTime},
        </if>
        <if test="periods != null">
            periods = periods+#{periods},
        </if>
        <if test="periodType != null">
            period_type = #{periodType},
        </if>
        <if test="buyType != null">
            buy_type = #{buyType},
        </if>
        update_time = now()
        where user_id = #{userId} and config_id = #{configId} and jx_status=1
    </update>

    <update id="updateById"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        update member_plus_info
        set
        <if test="jxStatus != null">
            jx_status = #{jxStatus},
        </if>
        <if test="jxEndTime != null">
            jx_end_time = #{jxEndTime},
        </if>
        <if test="periods != null">
            periods = periods+#{periods},
        </if>
        <if test="periodType != null">
            period_type = #{periodType},
        </if>
        <if test="buyType != null">
            buy_type = #{buyType},
        </if>
        update_time = now()
        where id = #{id} and jx_status=1
    </update>


    <update id="updatePeriodsByMemberId"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        update member_plus_info
        set
        <if test="jxStartTime != null">
            jx_start_time = #{jxStartTime},
        </if>
        <if test="jxEndTime != null">
            jx_end_time = #{jxEndTime},
        </if>
        <if test="periods != null">
            periods = #{periods},
        </if>
        update_time = now()
        where user_id = #{userId} and config_id = #{configId} and jx_status=1 and jx_end_time>=now()
    </update>

    <update id="updateTimeAndInfoByMemberId"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        update member_plus_info
        set
        <if test="configId != null">
            config_id = #{configId},
        </if>

        <if test="periods != null">
            periods = periods+#{periods},
        </if>
        <if test="periodType != null">
            period_type = #{periodType},
        </if>
        <if test="buyType != null">
            buy_type = #{buyType},
        </if>
        <if test="jxStatus != null">
            jx_status = #{jxStatus},
        </if>
        <if test="jxEndTime != null">
            jx_end_time = #{jxEndTime},
        </if>
        update_time = now()
        where user_id = #{userId} and channel_id = #{channelId}
        and id = #{id}
    </update>


    <update id="updateTimeAndInfoByMemberIdNew"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        update member_plus_info
        set
        <if test="periods != null">
            periods = #{periods},
        </if>
        <if test="periodType != null">
            period_type = #{periodType},
        </if>
        <if test="buyType != null">
            buy_type = #{buyType},
        </if>
        <if test="jxStatus != null">
            jx_status = #{jxStatus},
        </if>
        <if test="jxEndTime != null">
            jx_end_time = #{jxEndTime},
        </if>
        <if test="jxStartTime != null">
            jx_start_time = #{jxStartTime},
        </if>
        update_time = now()
        where user_id = #{userId} and channel_id = #{channelId}
        and id = #{id}
    </update>


    <select id="getMemberByConfigId"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info
        where user_id = #{userId}
        and channel_id = #{channelId}
        and config_id=#{configId}
        and jx_start_time &lt;=now()
        and jx_end_time &gt;=now()
        limit 1
    </select>

    <select id="getMemberByAll" resultType="java.lang.Integer">
        select count(*)
        from member_plus_info
        where user_id = #{userId}
          and channel_id = #{channelId}
          and jx_start_time &lt;= now()
          and jx_end_time &gt;= now()
          and jx_status = 1
          and config_id != 10
    </select>

    <select id="getAllMemberInfo"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusInfoPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_info
        where user_id = #{userId}
        and channel_id = #{channelId}
        and jx_start_time &lt;=now()
        and jx_end_time &gt;=now()
        and jx_status=1
        and config_id != 10
    </select>

    <select id="getExpireIdList"
            resultType="integer">
        select id
        from member_plus_info
        where jx_end_time &lt;=now()
        and jx_status=0
        order by id desc
        limit 300;
    </select>

    <select id="getPlusComingToEnd"
            resultType="com.juzifenqi.plus.module.asserts.model.contract.entity.RepayRenewRemindEntity">
        select a.*,b.money as renewPrice,b.program_id programId
        from member_plus_info a
                 left join plus_renew_info b on a.config_id=b.config_id
        where a.config_id=3 and b.state=1
          and DATEDIFF(DATE_FORMAT(a.jx_end_time,'%Y-%m-%d'),DATE_FORMAT(NOW(),'%Y-%m-%d'))=5
          and a.user_id=b.user_id
    </select>

    <sql id="Where_Clause">
        <where>
            <trim suffixOverrides=",">
                <if test="userId != null and userId != ''">
                    and user_id = #{userId, jdbcType=INTEGER}
                </if>
                <if test="configId != null and configId != '' and configId == 10000">
                    and config_id <![CDATA[ >=  ]]> #{configId, jdbcType=INTEGER}
                </if>
                <if test="configId != null and configId != '' and configId &lt; 10000">
                    and config_id = #{configId, jdbcType=INTEGER}
                </if>
            </trim>
        </where>
    </sql>

    <update id="cancelRenewState">
        update member_plus_info_ext set renew_state = 2, remark = #{remark}, update_time = now(), cancel_renew_time = now() where
        member_plus_id = #{memberPlusInfoId}
    </update>

    <update id="batchCancelRenewState">
        update member_plus_info_ext set renew_state = 2, remark = #{remark}, update_time = now(), cancel_renew_time = now() where
        member_plus_id in (
        <foreach collection="ids" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </update>

    <delete id="delExpireList">
        delete from  member_plus_info where id in (
        <foreach collection="list" index="index" item="id" separator=",">
            #{id}
        </foreach>
        )
    </delete>

    <select id="getByUserAndConfigIds" resultMap="BaseResultMap">
        select <include refid="Base_Column_List"/>
        from member_plus_info
        where user_id =#{userId}
        and jx_status = 1
        and jx_end_time > now()
        and config_id in
        (
        <foreach collection="configIds" index="index" item="configId" separator=",">
            #{configId}
        </foreach>
        )
    </select>

</mapper>
