<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusOrderInfoMapper">
    <resultMap id="MemberPlusOrderInfo" type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusOrderInfoPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel" property="channel"/>
        <result column="orderNo" property="orderNo"/>
        <result column="program_id" property="programId"/>
        <result column="type" property="type"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel`,
        `orderNo`,
        `msg_id`,
        `program_id`,
        `type`,
        `content`,
        `create_time`
    </sql>

    <insert id="insertMemberPlusOrderInfo"
            parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusOrderInfoPo">
        INSERT INTO member_plus_order_info (
        `user_id`,
        `channel`,
        `orderNo`,
        `msg_id`,
        `program_id`,
        `type`,
        `content`,
        `create_time`
        )
        VALUES(
        #{memberPlusOrderInfo.userId},
        #{memberPlusOrderInfo.channel},
        #{memberPlusOrderInfo.orderNo},
        #{memberPlusOrderInfo.msgId},
        #{memberPlusOrderInfo.programId},
        #{memberPlusOrderInfo.type},
        #{memberPlusOrderInfo.content},
        NOW()
        )
    </insert>

    <select id="getMemberPlusOrderInfo" resultType="java.lang.Integer">
        SELECT count(*)
        FROM member_plus_order_info
        WHERE `orderNo` = #{orderNo} and `program_id` = #{programId}
    </select>

    <select id="getInfoByOrderNo"
            resultType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusOrderInfoPo">
        select
        <include refid="Base_Column_List"/>
        from member_plus_order_info
        where orderNo=#{orderNo}
    </select>

</mapper>
