<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusProfitsLogMapper">

    <resultMap id="MemberPlusProfitsLog" type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusProfitsLogPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="channel_id" property="channelId" />
        <result column="plus_order_sn" property="plusOrderSn" />
        <result column="order_sn" property="orderSn" />
        <result column="log_type" property="logType" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `channel_id`,
        `plus_order_sn`,
        `order_sn`,
        `log_type`,
        `remark`,
        `create_time`
    </sql>

    <insert id="insert" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusProfitsLogPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_profits_log (
            `user_id`,
            `channel_id`,
            `plus_order_sn`,
            `order_sn`,
            `log_type`,
            `remark`,
            `create_time`
        )
        VALUES(
                  #{userId},
                  #{channelId},
                  #{plusOrderSn},
                  #{orderSn},
                  #{logType},
                  #{remark},
                  NOW()
              )
    </insert>

</mapper>
