<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.asserts.repository.dao.IMemberPlusSendPlanTaskMapper">

    <resultMap id="MemberPlusSendPlanTask"
            type="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanTaskPo">
        <result column="id" property="id" />
        <result column="order_sn" property="orderSn" />
        <result column="model_id" property="modelId" />
        <result column="plan_time" property="planTime" />
        <result column="profit_type" property="profitType" />
        <result column="profit_value" property="profitValue" />
        <result column="member_plus_send_plan_id" property="memberPlusSendPlanId" />
        <result column="task_state" property="taskState" />
        <result column="remark" property="remark" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `order_sn`,
        `model_id`,
        `plan_time`,
        `profit_type`,
        `profit_value`,
        `member_plus_send_plan_id`,
        `task_state`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusSendPlanTask" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanTaskPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_send_plan_task (
            `order_sn`,
            `model_id`,
            `plan_time`,
            `profit_type`,
            `profit_value`,
            `member_plus_send_plan_id`,
            `task_state`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{orderSn},
                  #{modelId},
                  #{planTime},
                  #{profitType},
                  #{profitValue},
                  #{memberPlusSendPlanId},
                  #{taskState},
                  #{remark},
                  NOW(),
                  #{updateTime}
              )
    </insert>

    <insert id="insertMemberPlusSendPlanTask" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanTaskPo">
        INSERT INTO member_plus_send_plan_task (
            `order_sn`,
            `model_id`,
            `plan_time`,
            `profit_type`,
            `profit_value`,
            `member_plus_send_plan_id`,
            `task_state`,
            `remark`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{memberPlusSendPlanTask.orderSn},
                  #{memberPlusSendPlanTask.modelId},
                  #{memberPlusSendPlanTask.planTime},
                  #{memberPlusSendPlanTask.profitType},
                  #{memberPlusSendPlanTask.profitValue},
                  #{memberPlusSendPlanTask.memberPlusSendPlanId},
                  #{memberPlusSendPlanTask.taskState},
                  #{memberPlusSendPlanTask.remark},
                  NOW(),
                  #{memberPlusSendPlanTask.updateTime}
              )
    </insert>

    <delete id="deleteMemberPlusSendPlanTask" parameterType="java.lang.Integer">
        DELETE FROM member_plus_send_plan_task
        WHERE `id` = #{id}
    </delete>

    <update id="updateMemberPlusSendPlanTask" parameterType="com.juzifenqi.plus.module.asserts.repository.po.MemberPlusSendPlanTaskPo" >
        UPDATE member_plus_send_plan_task
        SET
        <if test="memberPlusSendPlanTask.orderSn != null">`order_sn`= #{memberPlusSendPlanTask.orderSn},</if>
        <if test="memberPlusSendPlanTask.modelId != null">`model_id`= #{memberPlusSendPlanTask.modelId},</if>
        <if test="memberPlusSendPlanTask.planTime != null">`plan_time`= #{memberPlusSendPlanTask.planTime},</if>
        <if test="memberPlusSendPlanTask.profitType != null">`profit_type`= #{memberPlusSendPlanTask.profitType},</if>
        <if test="memberPlusSendPlanTask.profitValue != null">`profit_value`= #{memberPlusSendPlanTask.profitValue},</if>
        <if test="memberPlusSendPlanTask.memberPlusSendPlanId != null">`member_plus_send_plan_id`= #{memberPlusSendPlanTask.memberPlusSendPlanId},</if>
        <if test="memberPlusSendPlanTask.taskState != null">`task_state`= #{memberPlusSendPlanTask.taskState},</if>
        <if test="memberPlusSendPlanTask.remark != null">`remark`= #{memberPlusSendPlanTask.remark},</if>
        update_time = now()
        WHERE `id` = #{memberPlusSendPlanTask.id}
    </update>


    <select id="loadMemberPlusSendPlanTask" parameterType="java.lang.Integer" resultMap="MemberPlusSendPlanTask">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_send_plan_task
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="MemberPlusSendPlanTask">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_send_plan_task
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM member_plus_send_plan_task
    </select>

    <select id="getNeedSendPlanTasks" parameterType="java.util.Map" resultMap="MemberPlusSendPlanTask">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_send_plan_task
        WHERE task_state = #{taskStatus}
          AND plan_time <![CDATA[ <= ]]> NOW()
        <if test="modelId != null and modelId > 0">
            AND model_id = #{modelId}
        </if>
        ORDER BY plan_time
        LIMIT #{pageStart}, #{pageSize}
    </select>

    <select id="getNeedMoveSendPlanTasks" parameterType="java.util.Map" resultMap="MemberPlusSendPlanTask">
        SELECT <include refid="Base_Column_List" />
          FROM member_plus_send_plan_task
         WHERE task_state IN (
        <foreach collection="statuses" item="status" index="index" separator=",">
            #{status}
        </foreach>
        )
        LIMIT #{pageStart}, #{pageSize}
    </select>

    <delete id="deleteByIds">
        DELETE FROM member_plus_send_plan_task
        WHERE id in (
        <foreach collection="ids" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </delete>


    <select id="listBySendPlanIds"  resultMap="MemberPlusSendPlanTask">
        SELECT <include refid="Base_Column_List" />
        FROM member_plus_send_plan_task
        WHERE member_plus_send_plan_id in
        (
        <foreach collection="sendPlanIds" item="id" index="index" separator=",">
            #{id}
        </foreach>
        )
    </select>

    <update id="batchUpdateSendStatus">
        update member_plus_send_plan_task set task_state = #{taskState},update_time=now() where member_plus_send_plan_id in
        (
        <foreach collection="sendPlanIds" index="index" item="id"
                separator=",">
            #{id}
        </foreach>
        )
    </update>

    <insert id="batchInsertSendPlanTask" useGeneratedKeys="true" keyProperty="id" keyColumn="id">
        INSERT INTO member_plus_send_plan_task (
            `order_sn`,
            `model_id`,
            `plan_time`,
            `profit_type`,
            `profit_value`,
            `member_plus_send_plan_id`,
            `task_state`,
            `remark`,
            `create_time`,
            `update_time`
        )VALUES
        <foreach collection="list" index="index" item="item" separator=",">
            (
            #{item.orderSn},
            #{item.modelId},
            #{item.planTime},
            #{item.profitType},
            #{item.profitValue},
            #{item.memberPlusSendPlanId},
            #{item.taskState},
            #{item.remark},
            NOW(),
            #{item.updateTime}
            )
        </foreach>
    </insert>
</mapper>