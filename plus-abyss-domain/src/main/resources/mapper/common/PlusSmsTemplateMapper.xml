<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusSmsTemplateMapper">

    <resultMap id="PlusSmsTemplate"
               type="com.juzifenqi.plus.module.common.repository.po.PlusSmsTemplatePo">
        <result column="id" property="id"/>
        <result column="template_code" property="templateCode"/>
        <result column="sms_param" property="smsParam"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `template_code`,
        `sms_param`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusSmsTemplate"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsTemplatePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_sms_template (`template_code`,
                                       `sms_param`,
                                       `create_time`,
                                       `update_time`)
        VALUES (#{templateCode},
                #{smsParam},
                NOW(),
                NOW())
    </insert>


    <insert id="insertPlusSmsTemplate"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsTemplatePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_sms_template (`template_code`,
                                       `sms_param`,
                                       `create_time`,
                                       `update_time`)
        VALUES (#{plusSmsTemplate.templateCode},
                #{plusSmsTemplate.smsParam},
                NOW(),
                NOW())
    </insert>


    <delete id="deletePlusSmsTemplate" parameterType="java.lang.Integer">
        DELETE
        FROM plus_sms_template
        WHERE `id` = #{id}
    </delete>

    <update id="updateByTemplateCode"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsTemplatePo">
        UPDATE plus_sms_template
        SET
        <if test="plusSmsTemplate.templateCode != null">`template_code`=
            #{plusSmsTemplate.templateCode},
        </if>
        `sms_param`= #{plusSmsTemplate.smsParam},
        update_time = now()
        WHERE `id` = #{plusSmsTemplate.id}
    </update>


    <select id="loadPlusSmsTemplate" parameterType="java.lang.Integer" resultMap="PlusSmsTemplate">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_template
        WHERE `id` = #{id}
    </select>

    <select id="loadByTemplateCode"
            resultType="com.juzifenqi.plus.module.common.repository.po.PlusSmsTemplatePo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_template
        WHERE `template_code` = #{templateCode}
    </select>

    <select id="loadTemplateCodes" resultMap="PlusSmsTemplate">
        SELECT <include refid="Base_Column_List"/>
        FROM plus_sms_template order by id
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusSmsTemplate">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_template
        order by `id` desc
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_sms_template
    </select>


</mapper>
