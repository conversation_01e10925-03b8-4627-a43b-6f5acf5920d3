<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusBlackLogMapper">

    <resultMap id="PlusPayBlackListLog"
            type="com.juzifenqi.plus.module.common.repository.po.PlusPayBlackListLogPo">
        <result column="id" property="id"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="black_type" property="blackType"/>
        <result column="opt_user_id" property="optUserId"/>
        <result column="opt_user_name" property="optUserName"/>
        <result column="opt_event" property="optEvent"/>
        <result column="event_name" property="eventName"/>
        <result column="content" property="content"/>
        <result column="opt_time" property="optTime"/>
        <result column="bi_time" property="biTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `channel_id`,
        `config_id`,
        `black_type`,
        `opt_user_id`,
        `opt_user_name`,
        `opt_event`,
        `event_name`,
        `content`,
        `opt_time`
    </sql>

    <insert id="saveBlackRecordLog"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusPayBlackListLogPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_pay_black_list_log (`channel_id`,
                                             `config_id`,
                                             `black_type`,
                                             `opt_user_id`,
                                             `opt_user_name`,
                                             `opt_event`,
                                             `event_name`,
                                             `content`,
                                             `opt_time`)
        VALUES (#{channelId},
                #{configId},
                #{blackType},
                #{optUserId},
                #{optUserName},
                #{optEvent},
                #{eventName},
                #{content},
                NOW())
    </insert>

    <select id="getPlusPayBlackListLog" resultMap="PlusPayBlackListLog">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_pay_black_list_log where black_type= #{blackType} order by id desc limit 30;
    </select>
</mapper>
