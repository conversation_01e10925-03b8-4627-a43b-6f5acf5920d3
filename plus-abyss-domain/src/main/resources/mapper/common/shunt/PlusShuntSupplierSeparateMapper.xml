<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierSeparateMapper">

    <resultMap id="PlusShuntSupplierSeparate"
            type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierSeparatePo">
        <result column="id" property="id"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="separate_supplier_id" property="separateSupplierId"/>
        <result column="separate_type" property="separateType"/>
        <result column="separate_rate" property="separateRate"/>
        <result column="separate_amount" property="separateAmount"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `supplier_id`,
        `separate_supplier_id`,
        `separate_type`,
        `separate_rate`,
        `separate_amount`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="batchSave"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierSeparatePo">
        INSERT INTO plus_shunt_supplier_separate (
        `supplier_id`,
        `separate_supplier_id`,
        `separate_type`,
        `separate_rate`,
        `separate_amount`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.supplierId},
            #{record.separateSupplierId},
            #{record.separateType},
            #{record.separateRate},
            #{record.separateAmount},
            NOW(),
            #{record.updateTime}
            )
        </foreach>
    </insert>

    <delete id="deleteBySupplierId" parameterType="java.lang.Integer">
        DELETE
        FROM plus_shunt_supplier_separate
        WHERE `supplier_id` = #{supplierId}
    </delete>

    <select id="getBySupplierId" parameterType="java.lang.Integer"
            resultType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierSeparateEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_supplier_separate
        WHERE `supplier_id` = #{supplierId}
    </select>

    <select id="getBySeparateSupplierId" parameterType="java.lang.Integer"
            resultType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierSeparateEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_supplier_separate
        WHERE `separate_supplier_id` = #{separateSupplierId}
    </select>

</mapper>
