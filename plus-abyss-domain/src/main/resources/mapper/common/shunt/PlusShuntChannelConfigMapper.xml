<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntChannelConfigMapper">

    <resultMap id="plusShuntChannelConfig"
            type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntChannelConfigPo">
        <result column="id" property="id"/>
        <result column="route_id" property="routeId"/>
        <result column="channel_id" property="channelId"/>
        <result column="min_order_amount" property="minOrderAmount"/>
        <result column="max_order_amount" property="maxOrderAmount"/>
        <result column="resubmit_user" property="resubmitUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `route_id`,
        `channel_id`,
        `min_order_amount`,
        `max_order_amount`,
        `resubmit_user`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntChannelConfig"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntChannelConfigPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_channel_config (`route_id`,
                                       `channel_id`,
                                       `min_order_amount`,
                                       `max_order_amount`,
                                       `resubmit_user`,
                                       `create_time`,
                                       `update_time`)
        VALUES (#{routeId},
                #{channelId},
                #{minOrderAmount},
                #{maxOrderAmount},
                #{resubmitUser},
                NOW(),
                NOW())
    </insert>
    <insert id="batchSave"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntChannelConfigPo">
        INSERT INTO plus_shunt_channel_config (
        `route_id`,
        `channel_id`,
        `min_order_amount`,
        `max_order_amount`,
        `resubmit_user`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.routeId},
            #{record.channelId},
            #{record.minOrderAmount},
            #{record.maxOrderAmount},
            #{record.resubmitUser},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <update id="updatePlusShuntChannelConfig"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntChannelConfigPo">
        UPDATE plus_shunt_channel_config
        SET `route_id`=
                #{plusShuntChannelConfig.routeId},
            `channel_id`=
                #{plusShuntChannelConfig.channelId},
            `max_order_amount`=
                #{plusShuntChannelConfig.maxOrderAmount},
            `resubmit_user`=
                #{plusShuntChannelConfig.resubmitUser},
            update_time = now()
        WHERE `route_id` = #{plusShuntChannelConfig.routeId}
    </update>
    <delete id="deleteByRouteId">
        DELETE FROM plus_shunt_channel_config WHERE route_id = #{routeId}
    </delete>
    <select id="selectByRouteId"
            resultType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntChannelConfigPo">
            select <include refid="Base_Column_List"/> from plus_shunt_channel_config where route_id = #{routeId}
    </select>
</mapper>
