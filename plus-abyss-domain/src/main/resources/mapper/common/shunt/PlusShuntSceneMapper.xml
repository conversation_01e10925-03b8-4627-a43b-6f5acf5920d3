<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSceneMapper">

    <resultMap id="PlusShuntScene"
            type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntScenePo">
        <result column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="business_scene" property="businessScene"/>
        <result column="max_order_amount" property="maxOrderAmount"/>
        <result column="priority_order" property="priorityOrder"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `config_id`,
        `business_scene`,
        `max_order_amount`,
        `priority_order`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntScene"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntScenePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_scene (`config_id`,
                                      `business_scene`,
                                      `max_order_amount`,
                                      `priority_order`,
                                      `create_time`,
                                      `update_time`)
        VALUES (#{configId},
                #{businessScene},
                #{maxOrderAmount},
                #{priorityOrder},
                NOW(),
                NOW())
    </insert>

    <insert id="batchSave"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntScenePo">
        INSERT INTO plus_shunt_scene (`config_id`,
        `business_scene`,
        `max_order_amount`,
        `priority_order`,
        `create_time`,
        `update_time`)
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.configId},
            #{record.businessScene},
            #{record.maxOrderAmount},
            #{record.priorityOrder},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>

    <update id="updatePlusShuntScene"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntScenePo">
        UPDATE plus_shunt_scene
        SET `business_scene`=
                #{plusShuntScenePo.businessScene},
            `max_order_amount`=
                #{plusShuntScenePo.maxOrderAmount},
            `priority_order`=
                #{plusShuntScenePo.priorityOrder},
            update_time = now()
        WHERE `config_id` = #{plusShuntScenePo.configId}
    </update>
    <delete id="deleteByConfigIds">
        DELETE FROM plus_shunt_scene WHERE `config_id` IN
        <foreach item="item" collection="configIds" index="index" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="selectByConfigId" resultType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSceneEntity">
        select <include refid="Base_Column_List"/> from plus_shunt_scene where config_id = #{configId}
    </select>
</mapper>
