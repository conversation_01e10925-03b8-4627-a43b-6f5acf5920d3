<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntPaySceneMapper">

    <resultMap id="plusShuntPayScene"
               type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntPayScenePo">
        <result column="id" property="id"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="business_scene" property="businessScene"/>
        <result column="use_flag" property="useFlag"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `supplier_id`,
        `business_scene`,
        `use_flag`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntPayScene"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntPayScenePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_pay_scene (`supplier_id`,
                                          `business_scene`,
                                          `use_flag`,
                                          `create_time`,
                                          `update_time`)
        VALUES (#{supplierId},
                #{businessScene},
                #{useFlag},
                NOW(),
                NOW())
    </insert>
    <insert id="batchSave" parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntPayScenePo">
        INSERT INTO plus_shunt_pay_scene (
        `supplier_id`,
        `business_scene`,
        `use_flag`,
        `create_time`,
        `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.supplierId},
            #{record.businessScene},
            #{record.useFlag},
            NOW(),
            NOW()
            )
        </foreach>
    </insert>
    <delete id="deleteBySupplierId">
        DELETE
        FROM plus_shunt_pay_scene
        WHERE `supplier_id` = #{supplierId}
    </delete>
    <select id="getBySupplierId"
            resultType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierBusinessSceneEntity">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_pay_scene
        WHERE `supplier_id` = #{supplierId}
    </select>
</mapper>
