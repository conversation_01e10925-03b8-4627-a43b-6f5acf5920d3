<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntConfigMapper">

    <resultMap id="PlusShuntConfig"
            type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntConfigPo">
        <result column="id" property="id"/>
        <result column="route_id" property="routeId"/>
        <result column="min_order_amount" property="minOrderAmount"/>
        <result column="max_order_amount" property="maxOrderAmount"/>
        <result column="total_order_amount" property="totalOrderAmount"/>
        <result column="total_order_num" property="totalOrderNum"/>
        <result column="resubmit_user" property="resubmitUser"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `route_id`,
        `min_order_amount`,
        `max_order_amount`,
        `total_order_amount`,
        `total_order_num`,
        `resubmit_user`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntConfig"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntConfigPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_config (`route_id`,
                                       `min_order_amount`,
                                       `max_order_amount`,
                                       `total_order_amount`,
                                       `total_order_num`,
                                       `resubmit_user`,
                                       `create_time`,
                                       `update_time`)
        VALUES (#{routeId},
                #{minOrderAmount},
                #{maxOrderAmount},
                #{totalOrderAmount},
                #{totalOrderNum},
                #{resubmitUser},
                NOW(),
                #{updateTime})
    </insert>

    <update id="updatePlusShuntConfig"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntConfigPo">
        UPDATE plus_shunt_config
        <set>
            <if test="plusShuntConfig.minOrderAmount != null">
                `min_order_amount` = #{plusShuntConfig.minOrderAmount},
            </if>
            <if test="plusShuntConfig.maxOrderAmount != null">
                `max_order_amount` = #{plusShuntConfig.maxOrderAmount},
            </if>
            <if test="plusShuntConfig.totalOrderAmount != null">
                `total_order_amount` = #{plusShuntConfig.totalOrderAmount},
            </if>
            <if test="plusShuntConfig.totalOrderNum != null">
                `total_order_num` = #{plusShuntConfig.totalOrderNum},
            </if>
            <if test="plusShuntConfig.resubmitUser != null">
                `resubmit_user` = #{plusShuntConfig.resubmitUser},
            </if>
            update_time = now()
        </set>
        WHERE `route_id` = #{plusShuntConfig.routeId}
    </update>

    <select id="selectByRouteId" resultMap="PlusShuntConfig">
        select <include refid="Base_Column_List"/> from plus_shunt_config where route_id = #{routeId} limit 1
    </select>
</mapper>
