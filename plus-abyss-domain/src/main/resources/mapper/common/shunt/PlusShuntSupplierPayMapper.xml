<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierPayMapper">

    <resultMap id="PlusShuntSupplierPay" type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPayPo" >
        <result column="id" property="id" />
        <result column="supplier_id" property="supplierId" />
        <result column="deduct_pay_channel" property="deductPayChannel" />
        <result column="deduct_pay_source" property="deductPaySource" />
        <result column="defray_merchant_id" property="defrayMerchantId" />
        <result column="merchant_id" property="merchantId"/>
        <result column="business_scene" property="businessScene"/>
        <result column="bank_account_no" property="bankAccountNo"/>
        <result column="bank_account_no_uuid" property="bankAccountNoUuid"/>
        <result column="bank_account_name" property="bankAccountName"/>
        <result column="bank_account_type" property="bankAccountType"/>
        <result column="bank_branch_no" property="bankBranchNo"/>
        <result column="bank_name" property="bankName"/>
        <result column="bank_province_name" property="bankProvinceName"/>
        <result column="bank_province_code" property="bankProvinceCode"/>
        <result column="bank_city_name" property="bankCityName"/>
        <result column="bank_city_code" property="bankCityCode"/>
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `supplier_id`,
        `deduct_pay_channel`,
        `deduct_pay_source`,
        `defray_merchant_id`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntSupplierPay" parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPayPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_supplier_pay (`supplier_id`,
                                             `deduct_pay_channel`,
                                             `deduct_pay_source`,
                                             `defray_merchant_id`,
                                             `merchant_id`,
                                             `business_scene`,
                                             `bank_account_no`,
                                             `bank_account_no_uuid`,
                                             `bank_account_name`,
                                             `bank_account_type`,
                                             `bank_branch_no`,
                                             `bank_name`,
                                             `bank_province_name`,
                                             `bank_province_code`,
                                             `bank_city_code`,
                                             `bank_city_name`,
                                             `create_time`,
                                             `update_time`)
        VALUES (#{supplierId},
                #{deductPayChannel},
                #{deductPaySource},
                #{defrayMerchantId},
                #{merchantId},
                #{businessScene},
                #{bankAccountNo},
                #{bankAccountNoUuid},
                #{bankAccountName},
                #{bankAccountType},
                #{bankBranchNo},
                #{bankName},
                #{bankProvinceName},
                #{bankProvinceCode},
                #{bankCityCode},
                #{bankCityName},
                NOW(),
                #{updateTime})
    </insert>

    <update id="updateBySupplierId" parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPayPo" >
        UPDATE plus_shunt_supplier_pay
        SET
        <if test="plusShuntSupplierPay.deductPayChannel != null and plusShuntSupplierPay.deductPayChannel !=''">
            `deduct_pay_channel`= #{plusShuntSupplierPay.deductPayChannel},
        </if>
        <if test="plusShuntSupplierPay.deductPaySource != null and plusShuntSupplierPay.deductPaySource !=''">
            `deduct_pay_source`= #{plusShuntSupplierPay.deductPaySource},
        </if>
        <if test="plusShuntSupplierPay.defrayMerchantId != null and plusShuntSupplierPay.defrayMerchantId !=''">
            `defray_merchant_id`= #{plusShuntSupplierPay.defrayMerchantId},
        </if>
        `merchant_id`= #{plusShuntSupplierPay.merchantId},
        `business_scene` = #{plusShuntSupplierPay.businessScene},
        `bank_account_no` = #{plusShuntSupplierPay.bankAccountNo},
        `bank_account_no_uuid` = #{plusShuntSupplierPay.bankAccountNoUuid},
        `bank_account_name` = #{plusShuntSupplierPay.bankAccountName},
        `bank_account_type` = #{plusShuntSupplierPay.bankAccountType},
        `bank_branch_no` = #{plusShuntSupplierPay.bankBranchNo},
        `bank_name` = #{plusShuntSupplierPay.bankName},
        `bank_province_name` = #{plusShuntSupplierPay.bankProvinceName},
        `bank_province_code` = #{plusShuntSupplierPay.bankProvinceCode},
        `bank_city_name` = #{plusShuntSupplierPay.bankCityName},
        `bank_city_code` = #{plusShuntSupplierPay.bankCityCode},
        update_time = now()
        WHERE `supplier_id` = #{plusShuntSupplierPay.supplierId}
    </update>

    <select id="getByMerchantId" resultMap="PlusShuntSupplierPay">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_supplier_pay
        WHERE `merchant_id` = #{merchantId} limit 1
    </select>
    <select id="getWithEmptyBankAccountNoUuid"
            resultType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPayPo">
        select id, bank_account_no
        from plus_shunt_supplier_pay
        where bank_account_no is not null
          and bank_account_no_uuid is null
          and id > #{lastId}
        ORDER BY id ASC
            limit #{size}
    </select>
    <select id="getWithBankAccountNoUuid"
            resultType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPayPo">
        select id, bank_account_no, bank_account_no_uuid
        from plus_shunt_supplier_pay
        where bank_account_no is not null
          and bank_account_no_uuid is not null
          and id > #{lastId}
        ORDER BY id ASC
            limit #{size}
    </select>
    <update id="updateBatchBankAccountNoUuid">
        <foreach collection="list" item="item" separator=";">
            update
            `plus_shunt_supplier_pay`
            set
            `bank_account_no_uuid` = #{item.bankAccountNoUuid},
            `update_time` = now()
            where
            `id` = #{item.id}
        </foreach>
    </update>
</mapper>
