<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierContractMapper">

    <resultMap id="PlusShuntSupplierContract" type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierContractPo" >
        <result column="id" property="id" />
        <result column="supplier_id" property="supplierId" />
        <result column="contract_no" property="contractNo" />
        <result column="contract_name" property="contractName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `supplier_id`,
        `contract_no`,
        `contract_name`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="batchSave" parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierContractPo">
        INSERT INTO plus_shunt_supplier_contract (
            `supplier_id`,
            `contract_no`,
            `contract_name`,
            `create_time`,
            `update_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
                #{record.supplierId},
                #{record.contractNo},
                #{record.contractName},
                NOW(),
                #{record.updateTime}
            )
        </foreach>
    </insert>


    <delete id="deleteBySupplierId" parameterType="java.lang.Integer">
        DELETE FROM plus_shunt_supplier_contract
        WHERE `supplier_id` = #{supplierId}
    </delete>

    <select id="getBySupplierId" resultType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierContractEntity">
        select
        <include refid="Base_Column_List"/>
        from plus_shunt_supplier_contract where supplier_id = #{supplierId}
    </select>
</mapper>
