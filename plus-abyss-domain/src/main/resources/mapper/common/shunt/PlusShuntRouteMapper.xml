<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntRouteMapper">

    <resultMap id="PlusShuntRoute"
            type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntRoutePo">
        <result column="id" property="id"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="is_default" property="isDefault"/>
        <result column="priority_order" property="priorityOrder"/>
        <result column="enable_state" property="enableState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="PlusShuntRouteCache"
               type="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntRouteEntity">
        <result column="id" property="id"/>
        <result column="supplier_id" property="supplierId"/>
        <result column="priority_order" property="priorityOrder"/>
        <result column="enable_state" property="enableState"/>
        <association property="config" javaType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntConfigEntity">
            <result column="min_order_amount" property="minOrderAmount"/>
            <result column="max_order_amount" property="maxOrderAmount"/>
            <result column="total_order_amount" property="totalOrderAmount"/>
            <result column="total_order_num" property="totalOrderNum"/>
            <result column="resubmit_user" property="resubmitUser"/>
        </association>
        <collection property="configList"
                    ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntChannelConfigEntity">
            <result column="channel_id" property="channelId"/>
            <result column="cc_min_order_amount" property="minOrderAmount"/>
            <result column="cc_max_order_amount" property="maxOrderAmount"/>
            <result column="cc_resubmit_user" property="resubmitUser"/>
            <collection property="sceneList"
                        ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSceneEntity">
                <result column="config_id" property="configId"/>
                <result column="business_scene" property="businessScene"/>
                <result column="sc_max_order_amount" property="maxOrderAmount"/>
                <result column="sc_priority_order" property="priorityOrder"/>
            </collection>
        </collection>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `supplier_id`,
        `is_default`,
        `priority_order`,
        `enable_state`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntRoute"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntRoutePo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_route (`supplier_id`,
                                      `is_default`,
                                      `priority_order`,
                                      `enable_state`,
                                      `create_time`,
                                      `update_time`)
        VALUES (#{supplierId},
                #{isDefault},
                #{priorityOrder},
                #{enableState},
                NOW(),
                #{updateTime})
    </insert>


    <update id="updatePlusShuntRoute"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntRoutePo">
        UPDATE plus_shunt_route
        SET
        <if test="plusShuntRoute.supplierId != null">`supplier_id`=
            #{plusShuntRoute.supplierId},
        </if>
        <if test="plusShuntRoute.priorityOrder != null">`priority_order`=
            #{plusShuntRoute.priorityOrder},
        </if>
        <if test="plusShuntRoute.enableState != null">`enable_state`=
            #{plusShuntRoute.enableState},
        </if>
        update_time = now()
        WHERE `id` = #{plusShuntRoute.id}
    </update>

    <select id="selectList" resultMap="PlusShuntRoute">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_route
    </select>

    <select id="selectDefaultSupplier" resultMap="PlusShuntRoute">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_route where is_default = 2 limit 1
    </select>

    <select id="selectById" resultMap="PlusShuntRoute">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_route where id = #{id}
    </select>

    <select id="selectEnableNotDefaultList" resultMap="PlusShuntRouteCache">
        SELECT
            r.supplier_id,
            r.priority_order,
            c.min_order_amount,
            c.max_order_amount,
            c.total_order_amount,
            c.total_order_num,
            c.resubmit_user,
            cc.channel_id,
            cc.min_order_amount as cc_min_order_amount,
            cc.max_order_amount as cc_max_order_amount,
            cc.resubmit_user as cc_resubmit_user,
            sc.config_id,
            sc.business_scene,
            sc.max_order_amount as sc_max_order_amount,
            sc.priority_order as sc_priority_order
        FROM plus_shunt_route r
                 left join plus_shunt_config c on r.id = c.route_id
                 left join plus_shunt_channel_config cc on r.id = cc.route_id
                 left join plus_shunt_scene sc on cc.id = sc.config_id
        where r.is_default = 1
          and r.enable_state = 2
        order by r.priority_order
    </select>

    <select id="selectNotDefaultList" resultMap="PlusShuntRouteCache">
        SELECT
            r.id,
            r.supplier_id,
            r.enable_state,
            r.priority_order,
            c.min_order_amount,
            c.max_order_amount,
            c.total_order_amount,
            c.total_order_num,
            c.resubmit_user,
            cc.channel_id,
            cc.min_order_amount as cc_min_order_amount,
            cc.max_order_amount as cc_max_order_amount,
            cc.resubmit_user as cc_resubmit_user,
            sc.config_id,
            sc.business_scene,
            sc.max_order_amount as sc_max_order_amount,
            sc.priority_order as sc_priority_order
        FROM plus_shunt_route r
                 left join plus_shunt_config c on r.id = c.route_id
                 left join plus_shunt_channel_config cc on r.id = cc.route_id
                 left join plus_shunt_scene sc on cc.id = sc.config_id
        where r.is_default = 1
        order by r.id
    </select>
</mapper>
