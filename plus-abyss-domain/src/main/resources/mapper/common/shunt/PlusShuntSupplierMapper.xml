<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierMapper">

    <resultMap id="PlusShuntSupplier"
            type="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPo">
        <result column="id" property="id"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="separate_enable_state" property="separateEnableState"/>
        <result column="settle_enable_state" property="settleEnableState"/>
        <result column="enable_state" property="enableState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <resultMap id="PlusShuntSupplierEntityMap"
            type="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity">
        <result column="id" property="id"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="separate_enable_state" property="separateEnableState"/>
        <result column="settle_enable_state" property="settleEnableState"/>
        <result column="enable_state" property="enableState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <association property="pay"
                javaType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierPayEntity">
            <result column="deduct_pay_channel" property="deductPayChannel"/>
            <result column="deduct_pay_source" property="deductPaySource"/>
            <result column="defray_merchant_id" property="defrayMerchantId"/>
            <result column="merchant_id" property="merchantId"/>
            <result column="business_scene" property="businessScene"/>
            <result column="bank_account_no" property="bankAccountNo"/>
            <result column="bank_account_name" property="bankAccountName"/>
            <result column="bank_account_type" property="bankAccountType"/>
            <result column="bank_branch_no" property="bankBranchNo"/>
            <result column="bank_name" property="bankName"/>
            <result column="bank_province_name" property="bankProvinceName"/>
            <result column="bank_province_code" property="bankProvinceCode"/>
            <result column="bank_city_name" property="bankCityName"/>
            <result column="bank_city_code" property="bankCityCode"/>
        </association>
        <collection property="contractList"
                ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierContractEntity">
            <result column="contract_no" property="contractNo"/>
            <result column="contract_name" property="contractName"/>
        </collection>
        <collection property="separateList"
                    ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierSeparateEntity">
            <result column="separate_type" property="separateType"/>
            <result column="separate_supplier_id" property="separateSupplierId"/>
            <result column="separate_rate" property="separateRate"/>
            <result column="separate_amount" property="separateAmount"/>
        </collection>
        <collection property="separateChannelList"
                    ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierChannelSeparateEntity"
                    select="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierChannelSeparateMapper.getBySupplierId"
                    column="id"/>
        <collection property="businessSceneList"
                    ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierBusinessSceneEntity"
                    select="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntPaySceneMapper.getBySupplierId"
                    column="id">
        </collection>
    </resultMap>

    <resultMap id="PlusShuntSupplierEntityList"
            type="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity">
        <result column="id" property="id"/>
        <result column="supplier_name" property="supplierName"/>
        <result column="separate_enable_state" property="separateEnableState"/>
        <result column="settle_enable_state" property="settleEnableState"/>
        <result column="enable_state" property="enableState"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
        <association property="pay"
                javaType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierPayEntity">
            <result column="deduct_pay_channel" property="deductPayChannel"/>
            <result column="deduct_pay_source" property="deductPaySource"/>
            <result column="defray_merchant_id" property="defrayMerchantId"/>
            <result column="merchant_id" property="merchantId"/>
            <result column="business_scene" property="businessScene"/>
            <result column="bank_account_no" property="bankAccountNo"/>
            <result column="bank_account_name" property="bankAccountName"/>
            <result column="bank_account_type" property="bankAccountType"/>
            <result column="bank_branch_no" property="bankBranchNo"/>
            <result column="bank_name" property="bankName"/>
            <result column="bank_province_name" property="bankProvinceName"/>
            <result column="bank_province_code" property="bankProvinceCode"/>
            <result column="bank_city_name" property="bankCityName"/>
            <result column="bank_city_code" property="bankCityCode"/>
        </association>
        <collection property="contractList"
                ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierContractEntity"
                select="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierContractMapper.getBySupplierId"
                column="id">
        </collection>
        <collection property="separateList"
                    ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierSeparateEntity">
            <result column="separate_type" property="separateType"/>
            <result column="separate_supplier_id" property="separateSupplierId"/>
            <result column="separate_rate" property="separateRate"/>
            <result column="separate_amount" property="separateAmount"/>
        </collection>
        <collection property="separateChannelList"
                ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierChannelSeparateEntity"
                select="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntSupplierChannelSeparateMapper.getBySupplierId"
                column="id">
        </collection>
        <collection property="businessSceneList"
                    ofType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntPaySceneEntity"
                    select="com.juzifenqi.plus.module.common.repository.dao.shunt.IPlusShuntPaySceneMapper.getBySupplierId"
                    column="id">
        </collection>
    </resultMap>



    <sql id="Base_Column_List">
        `id`
        ,
        `supplier_name`,
        `enable_state`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntSupplier"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_supplier (`supplier_name`,
                                         `supplier_type`,
                                         `separate_enable_state`,
                                         `settle_enable_state`,
                                         `enable_state`,
                                         `create_time`,
                                         `update_time`)
        VALUES (#{supplierName},
                #{supplierType},
                #{separateEnableState},
                #{settleEnableState},
                #{enableState},
                NOW(),
                #{updateTime})
    </insert>


    <update id="updatePlusShuntSupplier"
            parameterType="com.juzifenqi.plus.module.common.repository.po.shunt.PlusShuntSupplierPo">
        UPDATE plus_shunt_supplier
        SET
        <if test="plusShuntSupplier.supplierName != null">`supplier_name`=
            #{plusShuntSupplier.supplierName},
        </if>
        <if test="plusShuntSupplier.separateEnableState != null">`separate_enable_state`=
            #{plusShuntSupplier.separateEnableState},
        </if>
        <if test="plusShuntSupplier.settleEnableState != null">`settle_enable_state`=
            #{plusShuntSupplier.settleEnableState},
        </if>
        <if test="plusShuntSupplier.enableState != null">`enable_state`=
            #{plusShuntSupplier.enableState},
        </if>
        update_time = now()
        WHERE `id` = #{plusShuntSupplier.id}
    </update>


    <select id="getBySupplierName" resultMap="PlusShuntSupplier">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_supplier
        WHERE supplier_name = #{supplierName} and supplier_type = #{supplierType}
    </select>

    <select id="getByJzSupplier" resultMap="PlusShuntSupplier">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_supplier
        WHERE supplier_name like '%桔子数科%' and supplier_type = #{supplierType}
    </select>

    <select id="loadPlusShuntSupplier" parameterType="java.lang.Integer"
            resultMap="PlusShuntSupplier">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_shunt_supplier
        WHERE `id` = #{id}
    </select>

    <select id="pageList" resultMap="PlusShuntSupplierEntityList">
        SELECT s.`id`,
               s.`supplier_name`,
               s.`separate_enable_state`,
               s.`settle_enable_state`,
               s.`enable_state`,
               s.`create_time`,
               s.`update_time`,
               p.`deduct_pay_channel`,
               p.`deduct_pay_source`,
               p.`defray_merchant_id`,
               p.`merchant_id`,
               p.`business_scene`,
               p.`bank_account_no`,
               p.`bank_account_name`,
               p.`bank_account_type`,
               p.`bank_branch_no`,
               p.`bank_name`,
               p.`bank_province_name`,
               p.`bank_province_code`,
               p.`bank_city_name`,
               p.`bank_city_code`
        from plus_shunt_supplier s
                 left join plus_shunt_supplier_pay p on s.id = p.supplier_id
        where s.supplier_type = #{supplierType}
            order by s.id
            LIMIT #{offset}
           , #{pagesize}
    </select>

    <select id="pageListCount" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_shunt_supplier
        where supplier_type = #{supplierType}
    </select>

    <select id="getDetailById" resultMap="PlusShuntSupplierEntityMap">
        SELECT s.`id`,
               s.`supplier_name`,
               s.`separate_enable_state`,
               s.`settle_enable_state`,
               s.`enable_state`,
               s.`create_time`,
               s.`update_time`,
               p.`deduct_pay_channel`,
               p.`deduct_pay_source`,
               p.`defray_merchant_id`,
               p.`merchant_id`,
               p.`business_scene`,
               p.`bank_account_no`,
               p.`bank_account_name`,
               p.`bank_account_type`,
               p.`bank_branch_no`,
               p.`bank_name`,
               p.`bank_province_name`,
               p.`bank_province_code`,
               p.`bank_city_name`,
               p.`bank_city_code`,
               c.`contract_no`,
               c.`contract_name`,
               ss.`separate_type`,
               ss.`separate_supplier_id`,
               ss.`separate_rate`,
               ss.`separate_amount`
        from plus_shunt_supplier s
                 left join plus_shunt_supplier_pay p on s.id = p.supplier_id
                 left join plus_shunt_supplier_contract c on s.id = c.supplier_id
                 left join plus_shunt_supplier_separate ss on s.id = ss.supplier_id
        where s.id = #{id}
    </select>

    <select id="getList" resultMap="PlusShuntSupplier">
        SELECT
        <include refid="Base_Column_List"/>
        from plus_shunt_supplier where supplier_type = #{supplierType}
        <if test="enableState != null">
            and enable_state = #{enableState}
        </if>
        order by id
    </select>

    <resultMap id="LimitCheckEntityList"
               type="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity">
        <result column="id" property="id"/>
        <result column="supplier_name" property="supplierName"/>
        <association property="pay"
                     javaType="com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierPayEntity">
            <result column="deduct_pay_channel" property="deductPayChannel"/>
            <result column="deduct_pay_source" property="deductPaySource"/>
            <result column="defray_merchant_id" property="defrayMerchantId"/>
            <result column="merchant_id" property="merchantId"/>
            <result column="business_scene" property="businessScene"/>
            <result column="limit_alarm_threshold" property="limitAlarmThreshold"/>
        </association>
    </resultMap>

    <select id="getLimitCheckList" resultMap="LimitCheckEntityList">
        SELECT s.`id`,
               s.`supplier_name`,
               p.`deduct_pay_channel`,
               p.`deduct_pay_source`,
               p.`defray_merchant_id`,
               p.`merchant_id`,
               p.`business_scene`,
               p.`limit_alarm_threshold`
        FROM
            plus_shunt_supplier s
            JOIN plus_shunt_supplier_pay p ON s.id = p.supplier_id
        WHERE
            s.enable_state = 2
            AND defray_merchant_id is not null
    </select>

</mapper>
