<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusMarketLogMapper">


    <resultMap id="BaseResultMap" type="com.juzifenqi.plus.module.common.repository.po.PlusMarketLogPo">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="BIGINT"/>
        <result column="channel_id" property="channelId" jdbcType="VARCHAR"/>
        <result column="biz_source" property="bizSource" jdbcType="INTEGER"/>
        <result column="scene_code" property="sceneCode" jdbcType="INTEGER"/>
        <result column="config_id" property="configId" jdbcType="INTEGER"/>
        <result column="program_id" property="programId" jdbcType="INTEGER"/>
        <result column="program_price" property="programPrice" jdbcType="DECIMAL"/>
        <result column="discount_rate" property="discountRate" jdbcType="DECIMAL"/>
        <result column="discount_price" property="discountPrice" jdbcType="DECIMAL"/>
        <result column="after_pay_state" property="afterPayState" jdbcType="INTEGER"/>
        <result column="shunt_supplier_id" property="shuntSupplierId" jdbcType="INTEGER"/>
        <result column="raise_amount" property="raiseAmount" jdbcType="DECIMAL"/>
        <result column="no_marketing_reason" property="noMarketingReason" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>

    </resultMap>

    <insert id="insert" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusMarketLogPo">
        INSERT INTO plus_market_log (user_id,
                                     channel_id,
                                     biz_source,
                                     scene_code,
                                     config_id,
                                     program_id,
                                     program_price,
                                     discount_rate,
                                     discount_price,
                                     raise_amount,
                                     after_pay_state,
                                     no_marketing_reason,
                                     shunt_supplier_id,
                                     create_time)
        VALUES (#{userId},
                #{channelId},
                #{bizSource},
                #{sceneCode},
                #{configId},
                #{programId},
                #{programPrice},
                #{discountRate},
                #{discountPrice},
                #{raiseAmount},
                #{afterPayState},
                #{noMarketingReason},
                #{shuntSupplierId},
                #{createTime})
    </insert>

    <delete id="deleteByIds">
        delete from plus_market_log where id in
        <foreach collection="ids" item="id" separator="," open="(" close=")">#{id}</foreach>
    </delete>

    <select id="getMarketLog" resultType="com.juzifenqi.plus.module.common.repository.po.PlusMarketLogPo">
        SELECT *
        FROM plus_market_log
        WHERE user_id = #{userId}
        AND create_time &lt; #{orderCreateTime}
        ORDER BY create_time DESC
        LIMIT 1
    </select>

    <select id="getMarketByTime" resultType="com.juzifenqi.plus.module.common.repository.po.PlusMarketLogPo">
        SELECT *
        FROM plus_market_log
        WHERE create_time &lt; #{time}
        ORDER BY id ASC
        limit #{limit}
    </select>

</mapper>
