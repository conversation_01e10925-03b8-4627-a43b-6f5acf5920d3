<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusShuntControlMapper">

    <resultMap id="PlusShuntControl" type="com.juzifenqi.plus.module.common.repository.po.PlusShuntControlPo" >
        <result column="id" property="id" />
        <result column="switch_state" property="switchState" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `switch_state`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusShuntControl" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusShuntControlPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_shunt_control (
        `switch_state`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{switchState},
        NOW(),
        #{updateTime}
        )
    </insert>


    <insert id="insertPlusShuntControl" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusShuntControlPo">
        INSERT INTO plus_shunt_control (
        `switch_state`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{plusShuntControl.switchState},
        NOW(),
        #{plusShuntControl.updateTime}
        )
    </insert>



    <delete id="deletePlusShuntControl" parameterType="java.lang.Integer">
        DELETE FROM plus_shunt_control
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusShuntControl" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusShuntControlPo" >
        UPDATE plus_shunt_control
        SET
        `switch_state`= #{plusShuntControl.switchState},
        update_time = now()
        WHERE `id` = #{plusShuntControl.id}
    </update>


    <select id="loadPlusShuntControl" parameterType="java.lang.Integer" resultMap="PlusShuntControl">
        SELECT <include refid="Base_Column_List" />
        FROM plus_shunt_control
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusShuntControl">
        SELECT <include refid="Base_Column_List" />
        FROM plus_shunt_control
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_shunt_control
    </select>

    <select id="getControl" parameterType="java.lang.Integer" resultMap="PlusShuntControl">
        SELECT <include refid="Base_Column_List" />
        FROM plus_shunt_control order by id desc limit 1
    </select>
</mapper>
