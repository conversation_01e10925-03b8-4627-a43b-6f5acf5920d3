<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusAiOutBoundInfoLogMapper">

    <resultMap id="PlusAiOutboundInfoLog" type="com.juzifenqi.plus.module.common.repository.po.PlusAiOutBoundInfoLogPo" >
        <result column="id" property="id" />
        <result column="user_id" property="userId" />
        <result column="config_id" property="configId" />
        <result column="task_id" property="taskId" />
        <result column="batch_id" property="batchId" />
        <result column="mobile_des" property="mobileDes" />
        <result column="mobile_des_uuid" property="mobileDesUuid" />
        <result column="result_code" property="resultCode" />
        <result column="result_msg" property="resultMsg" />
        <result column="callback_status" property="callbackStatus" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
        <result column="content" property="content" />
        <result column="bak" property="bak" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `user_id`,
        `config_id`,
        `task_id`,
        `batch_id`,
        `mobile_des`,
        `mobile_des_uuid`,
        `result_code`,
        `result_msg`,
        `callback_status`,
        `create_time`,
        `update_time`,
        `content`,
        `bak`
    </sql>

    <insert id="savePlusAiOutboundInfoLog" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusAiOutBoundInfoLogPo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_ai_outbound_info_log (
            `user_id`,
            `config_id`,
            `task_id`,
            `batch_id`,
            `mobile_des`,
            `mobile_des_uuid`,
            `result_code`,
            `result_msg`,
            `callback_status`,
            `create_time`,
            `update_time`,
            `content`,
            `bak`
        )
        VALUES(
                  #{userId},
                  #{configId},
                  #{taskId},
                  #{batchId},
                  #{mobileDes},
                  #{mobileDesUuid},
                  #{resultCode},
                  #{resultMsg},
                  #{callbackStatus},
                  NOW(),
                  #{updateTime},
                  #{content},
                  #{bak}
              )
    </insert>


    <insert id="insertPlusAiOutboundInfoLog" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusAiOutBoundInfoLogPo">
        INSERT INTO plus_ai_outbound_info_log (
            `user_id`,
            `config_id`,
            `task_id`,
            `batch_id`,
            `mobile_des`,
            `mobile_des_uuid`,
            `result_code`,
            `result_msg`,
            `callback_status`,
            `create_time`,
            `update_time`,
            `content`,
            `bak`
        )
        VALUES(
                  #{plusAiOutboundInfoLog.userId},
                  #{plusAiOutboundInfoLog.configId},
                  #{plusAiOutboundInfoLog.taskId},
                  #{plusAiOutboundInfoLog.batchId},
                  #{plusAiOutboundInfoLog.mobileDes},
                  #{plusAiOutboundInfoLog.mobileDesUuid},
                  #{plusAiOutboundInfoLog.resultCode},
                  #{plusAiOutboundInfoLog.resultMsg},
                  #{plusAiOutboundInfoLog.callbackStatus},
                  NOW(),
                  NOW(),
                  #{plusAiOutboundInfoLog.content},
                  #{plusAiOutboundInfoLog.bak}
              )
    </insert>



    <delete id="deletePlusAiOutboundInfoLog" parameterType="java.lang.Integer">
        DELETE FROM plus_ai_outbound_info_log
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusAiOutboundInfoLog" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusAiOutBoundInfoLogPo" >
        UPDATE plus_ai_outbound_info_log
        SET
        <if test="plusAiOutboundInfoLog.userId != null">`user_id`= #{plusAiOutboundInfoLog.userId},</if>
        <if test="plusAiOutboundInfoLog.configId != null">`config_id`= #{plusAiOutboundInfoLog.configId},</if>
        <if test="plusAiOutboundInfoLog.taskId != null">`task_id`= #{plusAiOutboundInfoLog.taskId},</if>
        <if test="plusAiOutboundInfoLog.batchId != null">`batch_id`= #{plusAiOutboundInfoLog.batchId},</if>
        <if test="plusAiOutboundInfoLog.mobileDes != null">`mobile_des`= #{plusAiOutboundInfoLog.mobileDes},</if>
        <if test="plusAiOutboundInfoLog.mobileDesUuid != null">`mobile_des_uuid`= #{plusAiOutboundInfoLog.mobileDesUuid},</if>
        <if test="plusAiOutboundInfoLog.resultCode != null">`result_code`= #{plusAiOutboundInfoLog.resultCode},</if>
        <if test="plusAiOutboundInfoLog.resultMsg != null">`result_msg`= #{plusAiOutboundInfoLog.resultMsg},</if>
        <if test="plusAiOutboundInfoLog.callbackStatus != null">`callback_status`= #{plusAiOutboundInfoLog.callbackStatus},</if>
        <if test="plusAiOutboundInfoLog.content != null">`content`= #{plusAiOutboundInfoLog.content},</if>
        <if test="plusAiOutboundInfoLog.bak != null">`bak`= #{plusAiOutboundInfoLog.bak},</if>
        update_time = now()
        WHERE `id` = #{plusAiOutboundInfoLog.id}
    </update>

    <update id="updateByBatchId" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusAiOutBoundInfoLogPo" >
        UPDATE plus_ai_outbound_info_log
        SET `callback_status`= 1,
            update_time      = now()
        WHERE `batch_id` = #{batchId}
    </update>
    <update id="updateBatchMobileDesUuid">
        <foreach collection="list" item="item" separator=";">
            update
            `plus_ai_outbound_info_log`
            set
            `mobile_des_uuid` = #{item.mobileDesUuid},
            `update_time` = now()
            where
            `id` = #{item.id}
        </foreach>
    </update>

    <select id="loadPlusAiOutboundInfoLog" parameterType="java.lang.Integer" resultMap="PlusAiOutboundInfoLog">
        SELECT <include refid="Base_Column_List" />
        FROM plus_ai_outbound_info_log
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusAiOutboundInfoLog">
        SELECT <include refid="Base_Column_List" />
        FROM plus_ai_outbound_info_log
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_ai_outbound_info_log
    </select>
    <select id="getWithEmptyMobileDesUuid" resultMap="PlusAiOutboundInfoLog">
        select id, mobile_des
        from plus_ai_outbound_info_log
        where mobile_des is not null
          and mobile_des_uuid is null
          and id > #{lastId}
        ORDER BY id ASC
            limit #{size}
    </select>

</mapper>
