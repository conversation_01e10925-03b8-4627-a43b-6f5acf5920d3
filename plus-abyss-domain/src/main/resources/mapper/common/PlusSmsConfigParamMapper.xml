<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusSmsConfigParamMapper">

    <resultMap id="PlusSmsConfigParam"
            type="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigParamPo">
        <result column="id" property="id"/>
        <result column="config_id" property="configId"/>
        <result column="config_name" property="configName"/>
        <result column="send_node" property="sendNode"/>
        <result column="node_name" property="nodeName"/>
        <result column="param_key" property="paramKey"/>
        <result column="state" property="state"/>
        <result column="param_val" property="paramVal"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `config_id`,
        `config_name`,
        `send_node`,
        `node_name`,
        `param_key`,
        `state`,
        `param_val`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusSmsConfigParam"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigParamPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_sms_config_param (
        `config_id`,
        `config_name`,
        `send_node`,
        `node_name`,
        `param_key`,
        `state`,
        `param_val`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{configId},
        #{configName},
        #{sendNode},
        #{nodeName},
        #{paramKey},
        #{state},
        #{paramVal},
        NOW(),
        #{updateTime}
        )
    </insert>


    <insert id="insertPlusSmsConfigParam"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigParamPo">
        INSERT INTO plus_sms_config_param (
        `config_id`,
        `config_name`,
        `send_node`,
        `node_name`,
        `param_key`,
        `state`,
        `param_val`,
        `create_time`,
        `update_time`
        )
        VALUES(
        #{plusSmsConfigParam.configId},
        #{plusSmsConfigParam.configName},
        #{plusSmsConfigParam.sendNode},
        #{plusSmsConfigParam.nodeName},
        #{plusSmsConfigParam.paramKey},
        #{plusSmsConfigParam.state},
        #{plusSmsConfigParam.paramVal},
        NOW(),
        #{plusSmsConfigParam.updateTime}
        )
    </insert>


    <delete id="deletePlusSmsConfigParam" parameterType="java.lang.Integer">
        DELETE FROM plus_sms_config_param
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusSmsConfigParam"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigParamPo">
        UPDATE plus_sms_config_param
        SET
        <if test="plusSmsConfigParam.configId != null">`config_id`=
            #{plusSmsConfigParam.configId},
        </if>
        <if test="plusSmsConfigParam.configName != null">`config_name`=
            #{plusSmsConfigParam.configName},
        </if>
        <if test="plusSmsConfigParam.sendNode != null">`send_node`=
            #{plusSmsConfigParam.sendNode},
        </if>
        <if test="plusSmsConfigParam.nodeName != null">`node_name`=
            #{plusSmsConfigParam.nodeName},
        </if>
        <if test="plusSmsConfigParam.paramKey != null">`param_key`=
            #{plusSmsConfigParam.paramKey},
        </if>
        <if test="plusSmsConfigParam.state != null">`state`= #{plusSmsConfigParam.state},</if>
        <if test="plusSmsConfigParam.paramVal != null">`param_val`=
            #{plusSmsConfigParam.paramVal},
        </if>
        update_time = now()
        WHERE `id` = #{plusSmsConfigParam.id}
    </update>


    <select id="loadPlusSmsConfigParam" parameterType="java.lang.Integer"
            resultMap="PlusSmsConfigParam">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_config_param
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusSmsConfigParam">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_config_param
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_sms_config_param
    </select>

    <select id="selectByConfigIdAndSendNode" resultMap="PlusSmsConfigParam">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_config_param
        where config_id = #{configId} and send_node = #{sendNode} and state = 1 order by id
    </select>
</mapper>
