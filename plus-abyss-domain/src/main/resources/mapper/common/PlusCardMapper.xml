<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusCardMapper">

    <resultMap id="PlusCard" type="com.juzifenqi.plus.module.common.repository.po.PlusCardPo" >
        <result column="id" property="id" />
        <result column="is_show" property="isShow" />
        <result column="card_content" property="cardContent" />
        <result column="create_time" property="createTime" />
        <result column="create_user_id" property="createUserId" />
        <result column="create_user" property="createUser" />
        <result column="update_time" property="updateTime" />
        <result column="update_user_id" property="updateUserId" />
        <result column="update_user" property="updateUser" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `is_show`,
        `card_content`,
        `create_time`,
        `create_user_id`,
        `create_user`,
        `update_time`,
        `update_user_id`,
        `update_user`
    </sql>

    <insert id="savePlusCard" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusCardPo" useGeneratedKeys="true" keyProperty="id">
      INSERT INTO plus_card (
              `is_show`,
              `card_content`,
              `create_time`,
              `create_user_id`,
              `create_user`,
              `update_time`,
              `update_user_id`,
              `update_user`
      )
      VALUES(
                #{isShow},
                #{cardContent},
                  NOW(),
                #{createUserId},
                #{createUser},
                #{updateTime},
                #{updateUserId},
                #{updateUser}
      )
    </insert>

    <update id="updatePlusCard" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusCardPo" >
        UPDATE plus_card
        SET
        <if test="PlusCard.isShow != null">`is_show`= #{PlusCard.isShow},</if>
        <if test="PlusCard.cardContent != null">`card_content`= #{PlusCard.cardContent},</if>
        <if test="PlusCard.createUserId != null">`create_user_id`= #{PlusCard.createUserId},</if>
        <if test="PlusCard.createUser != null">`create_user`= #{PlusCard.createUser},</if>
        <if test="PlusCard.updateUserId != null">`update_user_id`= #{PlusCard.updateUserId},</if>
        <if test="PlusCard.updateUser != null">`update_user`= #{PlusCard.updateUser},</if>
        update_time = now()
        WHERE `id` = #{PlusCard.id}
    </update>

    <select id="loadPlusCard" resultMap="PlusCard">
        SELECT <include refid="Base_Column_List" />
        FROM plus_card order by create_time desc limit 1;
    </select>

</mapper>
