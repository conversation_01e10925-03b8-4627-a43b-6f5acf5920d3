<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusSmsAssociateMapper">

    <resultMap id="PlusSmsAssociate" type="com.juzifenqi.plus.module.common.repository.po.PlusSmsAssociatePo" >
        <result column="id" property="id" />
        <result column="channel_id" property="channelId" />
        <result column="channel_name" property="channelName" />
        <result column="parent_code" property="parentCode" />
        <result column="config_code" property="configCode" />
        <result column="config_id" property="configId" />
        <result column="config_name" property="configName" />
        <result column="send_node" property="sendNode" />
        <result column="node_name" property="nodeName" />
        <result column="create_time" property="createTime" />
        <result column="update_time" property="updateTime" />
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `channel_id`,
        `channel_name`,
        `parent_code`,
        `config_code`,
        `config_id`,
        `config_name`,
        `send_node`,
        `node_name`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusSmsAssociate" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsAssociatePo" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_sms_associate (
            `channel_id`,
            `channel_name`,
            `parent_code`,
            `config_code`,
            `config_id`,
            `config_name`,
            `send_node`,
            `node_name`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{channelId},
                  #{channelName},
                  #{parentCode},
                  #{configCode},
                  #{configId},
                  #{configName},
                  #{sendNode},
                  #{nodeName},
                  NOW(),
                  #{updateTime}
              )
    </insert>


    <insert id="insertPlusSmsAssociate" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsAssociatePo">
        INSERT INTO plus_sms_associate (
            `channel_id`,
            `channel_name`,
            `parent_code`,
            `config_code`,
            `config_id`,
            `config_name`,
            `send_node`,
            `node_name`,
            `create_time`,
            `update_time`
        )
        VALUES(
                  #{plusSmsAssociate.channelId},
                  #{plusSmsAssociate.channelName},
                  #{plusSmsAssociate.parentCode},
                  #{plusSmsAssociate.configCode},
                  #{plusSmsAssociate.configId},
                  #{plusSmsAssociate.configName},
                  #{plusSmsAssociate.sendNode},
                  #{plusSmsAssociate.nodeName},
                  NOW(),
                  #{plusSmsAssociate.updateTime}
              )
    </insert>



    <delete id="deletePlusSmsAssociate" parameterType="java.lang.Integer">
        DELETE FROM plus_sms_associate
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusSmsAssociate" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsAssociatePo" >
        UPDATE plus_sms_associate
        SET
        <if test="plusSmsAssociate.channelId != null">`channel_id`= #{plusSmsAssociate.channelId},</if>
        <if test="plusSmsAssociate.channelName != null">`channel_name`= #{plusSmsAssociate.channelName},</if>
        <if test="plusSmsAssociate.parentCode != null">`parent_code`= #{plusSmsAssociate.parentCode},</if>
        <if test="plusSmsAssociate.configCode != null">`config_code`= #{plusSmsAssociate.configCode},</if>
        <if test="plusSmsAssociate.configId != null">`config_id`= #{plusSmsAssociate.configId},</if>
        <if test="plusSmsAssociate.configName != null">`config_name`= #{plusSmsAssociate.configName},</if>
        <if test="plusSmsAssociate.sendNode != null">`send_node`= #{plusSmsAssociate.sendNode},</if>
        <if test="plusSmsAssociate.nodeName != null">`node_name`= #{plusSmsAssociate.nodeName},</if>
        update_time = now()
        WHERE `id` = #{plusSmsAssociate.id}
    </update>


    <select id="loadPlusSmsAssociate" parameterType="java.lang.Integer" resultMap="PlusSmsAssociate">
        SELECT <include refid="Base_Column_List" />
        FROM plus_sms_associate
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusSmsAssociate">
        SELECT <include refid="Base_Column_List" />
        FROM plus_sms_associate
        LIMIT #{offset}, #{pagesize}
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_sms_associate
    </select>

    <select id="selectAll"  resultMap="PlusSmsAssociate">
        SELECT <include refid="Base_Column_List" />
        FROM plus_sms_associate
    </select>

</mapper>
