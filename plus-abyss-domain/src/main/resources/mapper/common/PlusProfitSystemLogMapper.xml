<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
  "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusProfitSystemLogMapper">

  <resultMap id="PlusProfitSystemLog"
    type="com.juzifenqi.plus.module.common.repository.po.PlusProfitSystemLogPo">
    <result column="id" property="id"/>
    <result column="record_id" property="recordId"/>
    <result column="log_type" property="logType"/>
    <result column="opt_event" property="optEvent"/>
    <result column="opt_time" property="optTime"/>
    <result column="opt_user_id" property="optUserId"/>
    <result column="opt_user_name" property="optUserName"/>
    <result column="remark" property="remark"/>
  </resultMap>

  <sql id="Base_Column_List">
    `id`,
        `record_id`,
        `log_type`,
        `opt_event`,
        `opt_time`,
        `opt_user_id`,
        `opt_user_name`,
        `remark`
  </sql>

  <insert id="save" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusProfitSystemLogPo"
    useGeneratedKeys="true" keyProperty="id">
    INSERT INTO plus_profit_system_log (
      `record_id`,
      `log_type`,
      `opt_event`,
      `opt_time`,
      `opt_user_id`,
      `opt_user_name`,
      `remark`
    )
    VALUES(
            #{recordId},
            #{logType},
            #{optEvent},
            NOW(),
            #{optUserId},
            #{optUserName},
            #{remark}
          )
  </insert>

  <insert id="saveBatch"
    parameterType="com.juzifenqi.plus.module.common.repository.po.PlusProfitSystemLogPo"
    useGeneratedKeys="true" keyProperty="id" keyColumn="id">
    INSERT INTO plus_profit_system_log (
    `record_id`,
    `log_type`,
    `opt_event`,
    `opt_time`,
    `opt_user_id`,
    `opt_user_name`,
    `remark`
    )
    VALUES
    <foreach collection="logList" index="index" item="record" separator=",">
      (
      #{record.recordId},
      #{record.logType},
      #{record.optEvent},
      NOW(),
      #{record.optUserId},
      #{record.optUserName},
      #{record.remark,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

  <select id="selectById" parameterType="java.lang.Integer" resultMap="PlusProfitSystemLog">
    SELECT
    <include refid="Base_Column_List"/>
    FROM plus_profit_system_log
    WHERE `id` = #{id}
  </select>

  <select id="selectListByRecord" resultMap="PlusProfitSystemLog">
    SELECT
    <include refid="Base_Column_List"/>
    FROM plus_profit_system_log
    WHERE `record_id` = #{recordId} and `log_type` = #{logType}
    ORDER BY opt_time desc
  </select>

</mapper>