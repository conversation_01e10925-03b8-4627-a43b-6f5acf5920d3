<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusProgramLogMapper">

    <resultMap id="PlusProgramLog"
            type="com.juzifenqi.plus.module.common.repository.po.PlusProgramLogPo">
        <result column="id" property="id"/>
        <result column="program_id" property="programId"/>
        <result column="operating_id" property="operatingId"/>
        <result column="operating_name" property="operatingName"/>
        <result column="event" property="event"/>
        <result column="content" property="content"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `program_id`,
        `operating_id`,
        `operating_name`,
        `event`,
        `content`,
        `create_time`
    </sql>

    <select id="loadPlusProgramLog" parameterType="java.lang.Integer"
            resultMap="PlusProgramLog">
        select
        <include refid="Base_Column_List"/>
        from plus_program_log
        where `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusProgramLog">
        select
        <include refid="Base_Column_List"/>
        from `plus_program_log`
        <include refid="getCondition"/>
        order by `create_time` desc
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="getPlusProgramLogList" parameterType="java.lang.Integer" resultMap="PlusProgramLog">
        select
        <include refid="Base_Column_List"/>
        from `plus_program_log`
        where program_id = #{id}
        order by `create_time` desc
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        select count(1)
        from plus_program_log
        <include refid="getCondition"/>
    </select>

    <sql id="getCondition">
        <where>
            <if test="queryMap.q_operatingName != null and '' != queryMap.q_operatingName">
                and `operating_name` like CONCAT('%', #{queryMap.q_operatingName}, '%')
            </if>
            <if test="queryMap.q_programId != null and queryMap.q_programId != ''">
                and `program_id` = #{queryMap.q_programId}
            </if>

            <if test="queryMap.q_startTime != null and '' != queryMap.q_startTime">
                and date_format(`create_time`,'%Y-%m-%d') &gt;= #{queryMap.q_startTime}
            </if>
            <if test="queryMap.q_endTime != null and '' != queryMap.q_endTime">
                and date_format(`create_time`,'%Y-%m-%d') &lt;= #{queryMap.q_endTime}
            </if>
        </where>
    </sql>

    <insert id="savePlusProgramLog"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusProgramLogPo"
            useGeneratedKeys="true" keyProperty="id">
        insert into plus_program_log (
        `program_id`,
        `operating_id`,
        `operating_name`,
        `event`,
        `content`,
        `create_time`
        )
        values(
        #{programId},
        #{operatingId},
        #{operatingName},
        #{event},
        #{content},
        NOW()
        )
    </insert>

    <delete id="deletePlusProgramLog" parameterType="java.lang.Integer">
        delete from plus_program_log
        where `id` = #{id}
    </delete>

    <update id="updatePlusProgramLog"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusProgramLogPo">
        update plus_program_log
        set
        <if test="PlusProgramLog.programId != null">`program_id`=
            #{PlusProgramLog.programId},
        </if>
        <if test="PlusProgramLog.operatingId != null">`operating_id`=
            #{PlusProgramLog.operatingId},
        </if>
        <if test="PlusProgramLog.operatingName != null">`operating_name`=
            #{PlusProgramLog.operatingName},
        </if>
        <if test="PlusProgramLog.event != null">`event`=
            #{PlusProgramLog.event},
        </if>
        <if test="PlusProgramLog.content != null">`content`=
            #{PlusProgramLog.content},
        </if>
        where `id` = #{PlusProgramLog.id}
    </update>
</mapper>
