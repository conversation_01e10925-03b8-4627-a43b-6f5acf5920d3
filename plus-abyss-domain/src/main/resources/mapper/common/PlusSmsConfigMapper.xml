<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusSmsConfigMapper">

    <resultMap id="PlusSmsConfig" type="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigPo">
        <result column="id" property="id"/>
        <result column="channel_id" property="channelId"/>
        <result column="channel_name" property="channelName"/>
        <result column="config_id" property="configId"/>
        <result column="send_node" property="sendNode"/>
        <result column="node_name" property="nodeName"/>
        <result column="sign_code" property="signCode"/>
        <result column="template_code" property="templateCode"/>
        <result column="sms_status" property="smsStatus"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `channel_id`,
        `channel_name`,
        `config_id`,
        `send_node`,
        `node_name`,
        `sign_code`,
        `template_code`,
        `sms_status`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="savePlusSmsConfig" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_sms_config (`channel_id`,
                                     `channel_name`,
                                     `config_id`,
                                     `send_node`,
                                     `node_name`,
                                     `sign_code`,
                                     `template_code`,
                                     `sms_status`,
                                     `remark`,
                                     `create_time`,
                                     `update_time`)
        VALUES (#{channelId},
                #{channelName},
                #{configId},
                #{sendNode},
                #{nodeName},
                #{signCode},
                #{templateCode},
                #{smsStatus},
                #{remark},
                NOW(),
                NOW())
    </insert>


    <insert id="insertPlusSmsConfig" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigPo">
        INSERT INTO plus_sms_config (`channel_id`,
                                     `channel_name`,
                                     `config_id`,
                                     `send_node`,
                                     `node_name`,
                                     `sign_code`,
                                     `template_code`,
                                     `sms_status`,
                                     `remark`,
                                     `create_time`,
                                     `update_time`)
        VALUES (#{plusSmsConfig.channelId},
                #{plusSmsConfig.channelName},
                #{plusSmsConfig.configId},
                #{plusSmsConfig.sendNode},
                #{plusSmsConfig.nodeName},
                #{plusSmsConfig.signCode},
                #{plusSmsConfig.templateCode},
                #{plusSmsConfig.smsStatus},
                #{plusSmsConfig.remark},
                NOW(),
                NOW())
    </insert>


    <delete id="deletePlusSmsConfig" parameterType="java.lang.Integer">
        DELETE
        FROM plus_sms_config
        WHERE `id` = #{id}
    </delete>

    <update id="updatePlusSmsConfig" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigPo">
        UPDATE plus_sms_config
        SET
        <if test="plusSmsConfig.channelId != null">`channel_id`= #{plusSmsConfig.channelId},</if>
        <if test="plusSmsConfig.channelName != null">`channel_name`= #{plusSmsConfig.channelName},</if>
        <if test="plusSmsConfig.configId != null">`config_id`= #{plusSmsConfig.configId},</if>
        <if test="plusSmsConfig.sendNode != null">`send_node`= #{plusSmsConfig.sendNode},</if>
        <if test="plusSmsConfig.nodeName != null">`node_name`= #{plusSmsConfig.nodeName},</if>
        <if test="plusSmsConfig.signCode != null">`sign_code`= #{plusSmsConfig.signCode},</if>
        <if test="plusSmsConfig.templateCode != null">`template_code`= #{plusSmsConfig.templateCode},</if>
        <if test="plusSmsConfig.smsStatus != null">`sms_status`= #{plusSmsConfig.smsStatus},</if>
        <if test="plusSmsConfig.remark != null">`remark`= #{plusSmsConfig.remark},</if>
        update_time = now()
        WHERE `id` = #{plusSmsConfig.id}
    </update>


    <select id="loadPlusSmsConfig" parameterType="java.lang.Integer" resultMap="PlusSmsConfig">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_config
        WHERE `id` = #{id}
    </select>

    <select id="pageList" parameterType="java.util.Map" resultMap="PlusSmsConfig">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_sms_config
        <include refid="getCondition"/>
        order by `id` desc
        <if test="size != null and size &gt; 0">limit #{start},#{size}</if>
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_sms_config
        <include refid="getCondition"/>
    </select>

    <select id="getIsExistsByTemplateCode"
            resultType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigPo">
        SELECT
        <include refid="Base_Column_List"/>
        from plus_sms_config where template_code = #{queryParam.templateCode}
        <if test="queryParam.id !=null">
            and `id` != #{queryParam.id}
        </if>
        <if test="queryParam.configId != null ">
            and `config_id` = #{queryParam.configId}
        </if>
        <if test="queryParam.sendNode != null ">
            and `send_node` = #{queryParam.sendNode}
        </if>
        <if test="queryParam.channelId != null ">
            and `channel_id` = #{queryParam.channelId}
        </if>
    </select>

    <select id="getPlusSmsConfig" resultType="com.juzifenqi.plus.module.common.repository.po.PlusSmsConfigPo">
        SELECT s.`channel_id`,
               s.`channel_name`,
               s.`config_id`,
               s.`send_node`,
               s.`node_name`,
               s.`sign_code`,
               s.`template_code`,
               s.`sms_status`,
               s.`remark`,
               t.sms_param
        FROM plus_sms_config s
                 left join plus_sms_template t
                           on s.template_code = t.template_code
        where s.id = #{id}
          and s.`channel_id` = #{channelId}
          and s.`config_id` = #{configId}
          and s.`send_node` = #{sendNode}
    </select>


    <sql id="getCondition">
        <where>
            <if test="queryParam.templateCode != null ">
                and `template_code` = #{queryParam.templateCode}
            </if>
            <if test="queryParam.configId != null ">
                and `config_id` = #{queryParam.configId}
            </if>
            <if test="queryParam.channelId != null ">
                and `channel_id` = #{queryParam.channelId}
            </if>
        </where>
    </sql>

</mapper>
