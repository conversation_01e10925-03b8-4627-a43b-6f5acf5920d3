<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusBlackMapper">

    <resultMap id="PlusPayBlackList" type="com.juzifenqi.plus.module.common.repository.po.PlusPayBlackListPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="config_id" property="configId"/>
        <result column="black_type" property="blackType"/>
        <result column="remark" property="remark"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `user_id`,
        `channel_id`,
        `config_id`,
        `black_type`,
        `remark`,
        `create_time`,
        `update_time`
    </sql>

    <sql id="whereConditions">
        <where>
            black_type= #{queryParam.blackType}
            <if test="queryParam.userId != null and queryParam.userId != ''">
                and `user_id` = #{queryParam.userId}
            </if>
            <if test="queryParam.configId != null">
                and `config_id` = #{queryParam.configId}
            </if>
        </where>
    </sql>

    <insert id="batchPlusPayBlackList">
        INSERT INTO plus_pay_black_list (
        `user_id`,
        `channel_id`,
        `config_id`,
        `black_type`,
        `create_time`
        )
        VALUES
        <foreach collection="list" index="index" item="plusPayBlackList" separator=",">
            (
            #{plusPayBlackList.userId},
            #{plusPayBlackList.channelId},
            #{plusPayBlackList.configId},
            #{plusPayBlackList.blackType},
            NOW()
            )
        </foreach>
    </insert>

    <insert id="saveBlackRecord">
        INSERT INTO plus_pay_black_list
        (`user_id`,
         `channel_id`,
         `config_id`,
         `black_type`,
         `create_time`)
        VALUES (#{userId},
                #{channelId},
                #{configId},
                #{blackType},
                NOW())
    </insert>

    <delete id="deletePlusPayBlackList" parameterType="java.lang.Integer">
        DELETE
        FROM plus_pay_black_list
        WHERE `id` = #{id}
    </delete>

    <delete id="batchDeletePayBlackList">
        DELETE FROM plus_pay_black_list
        WHERE
        black_type= #{blackType}
        <if test="configId != null">
            and `config_id` = #{configId}
        </if>
        <if test="channelId != null">
            and `channel_id` = #{channelId}
        </if>
        and user_id IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </delete>

    <select id="getCountByUserId" resultType="java.lang.Integer">
        SELECT
        count(1)
        FROM plus_pay_black_list
        where `user_id` = #{userId} and `black_type`= #{blackType}
        <if test="configId != null">
            and `config_id`= #{configId}
        </if>
    </select>

    <select id="pageListCount" parameterType="java.util.Map" resultType="java.lang.Integer">
        SELECT count(1)
        FROM plus_pay_black_list
        <include refid="whereConditions"/>
    </select>

    <select id="getPlusPayBlackList"
            resultMap="PlusPayBlackList" parameterType="java.util.Map">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_pay_black_list
        <include refid="whereConditions"/>
        order by create_time desc
        LIMIT #{pageStart}, #{pagesize}
    </select>

    <select id="getBlackListInfo" resultMap="PlusPayBlackList">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_pay_black_list
        where id = #{id}
    </select>
</mapper>
