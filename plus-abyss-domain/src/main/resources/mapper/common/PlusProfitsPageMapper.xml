<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusProfitsPageMapper">

    <resultMap id="MemberPlusProfitsPage"
            type="com.juzifenqi.plus.module.common.repository.po.PlusProfitsPagePo">
        <result column="id" property="id"/>
        <result column="backdrop_img" property="backdropImg"/>
        <result column="title" property="title"/>
        <result column="content" property="content"/>
        <result column="jump_url" property="jumpUrl"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `backdrop_img`,
        `title`,
        `content`,
        `jump_url`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusProfitsPage"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusProfitsPagePo"
            useGeneratedKeys="true" keyProperty="id">
      INSERT INTO member_plus_profits_page (
              `backdrop_img`,
              `title`,
              `content`,
              `jump_url`,
              `create_time`,
              `update_time`
      )
      VALUES(
                #{backdropImg},
                #{title},
                #{content},
                #{jumpUrl},
                  NOW(),
                #{updateTime}
      )
    </insert>

    <update id="updateMemberPlusProfitsPage"
            parameterType="com.juzifenqi.plus.module.common.repository.po.PlusProfitsPagePo">
        UPDATE member_plus_profits_page
        SET
        <if test="memberPlusProfitsPage.backdropImg != null">`backdrop_img`=
            #{memberPlusProfitsPage.backdropImg},
        </if>
        <if test="memberPlusProfitsPage.title != null">`title`= #{memberPlusProfitsPage.title},</if>
        <if test="memberPlusProfitsPage.content != null">`content`=
            #{memberPlusProfitsPage.content},
        </if>
        <if test="memberPlusProfitsPage.jumpUrl != null">`jump_url`=
            #{memberPlusProfitsPage.jumpUrl},
        </if>
        update_time = now()
        WHERE `id` = #{memberPlusProfitsPage.id}
    </update>

    <select id="loadMemberPlusProfitsPage" parameterType="java.lang.Integer"
            resultMap="MemberPlusProfitsPage">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_profits_page
        WHERE `id` = #{id}
    </select>

    <select id="getPlusProfitsPage"
            resultType="com.juzifenqi.plus.module.common.repository.po.PlusProfitsPagePo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_profits_page order by id desc limit 1
    </select>

</mapper>
