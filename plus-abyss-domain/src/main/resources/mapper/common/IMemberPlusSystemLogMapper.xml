<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.MemberPlusSystemLogMapper">


    <resultMap id="MemberPlusSystemLog"
            type="com.juzifenqi.plus.module.common.repository.po.MemberPlusSystemLogPo">
        <result column="id" property="id"/>
        <result column="member_id" property="memberId"/>
        <result column="channel_id" property="channelId"/>
        <result column="program_id" property="programId"/>
        <result column="order_sn" property="orderSn"/>
        <result column="node_type" property="nodeType"/>
        <result column="cancel_reason" property="cancelReason"/>
        <result column="cancel_remark" property="cancelRemark"/>
        <result column="remark" property="remark"/>
        <result column="use_profit" property="useProfit"/>
        <result column="operating_id" property="operatingId"/>
        <result column="operating_name" property="operatingName"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `member_id`,
        `channel_id`,
        `program_id`,
        `order_sn`,
        `node_type`,
        `cancel_reason`,
        `cancel_remark`,
        `remark`,
        `use_profit`,
        `operating_id`,
        `operating_name`,
        `create_time`
    </sql>

    <insert id="insertMemberPlusSystemLog"
            parameterType="com.juzifenqi.plus.module.common.repository.po.MemberPlusSystemLogPo">
        INSERT INTO member_plus_system_log (`member_id`,
                                            `channel_id`,
                                            `program_id`,
                                            `order_sn`,
                                            `node_type`,
                                            `cancel_reason`,
                                            `cancel_remark`,
                                            `remark`,
                                            `use_profit`,
                                            `operating_id`,
                                            `operating_name`,
                                            `create_time`)
        VALUES (#{memberPlusSystemLog.memberId},
                #{memberPlusSystemLog.channelId},
                #{memberPlusSystemLog.programId},
                #{memberPlusSystemLog.orderSn},
                #{memberPlusSystemLog.nodeType},
                #{memberPlusSystemLog.cancelReason},
                #{memberPlusSystemLog.cancelRemark},
                #{memberPlusSystemLog.remark},
                #{memberPlusSystemLog.useProfit},
                #{memberPlusSystemLog.operatingId},
                #{memberPlusSystemLog.operatingName},
                NOW())
    </insert>

    <insert id="batchSaveLogs">
        INSERT INTO member_plus_system_log (
        `member_id`,
        `channel_id`,
        `program_id`,
        `order_sn`,
        `node_type`,
        `cancel_reason`,
        `cancel_remark`,
        `remark`,
        `use_profit`,
        `operating_id`,
        `operating_name`,
        `create_time`
        )
        VALUES
        <foreach collection="list" index="index" item="record" separator=",">
            (
            #{record.memberId},
            #{record.channelId},
            #{record.programId},
            #{record.orderSn},
            #{record.nodeType},
            #{record.cancelReason},
            #{record.cancelRemark},
            #{record.remark},
            #{record.useProfit},
            #{record.operatingId},
            #{record.operatingName},
            NOW()
            )
        </foreach>
    </insert>

    <select id="getSystemOptLog" resultMap="MemberPlusSystemLog">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_system_log
        where `member_id` = #{memberId} and `channel_id`= #{channelId}
    </select>

    <select id="getPlusOrderSystemLog" resultMap="MemberPlusSystemLog">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_system_log
        where `member_id` = #{memberId} and `order_sn`= #{orderSn}
    </select>


    <select id="getLastCancelInfos" resultMap="MemberPlusSystemLog">
        SELECT order_sn,operating_name
        FROM member_plus_system_log
        where node_type in (4,21,24,23,27,28,32,31,33,34)
        and
        <foreach collection="orderSns" item="orderSn" open=" order_sn  in  (" close=")"
                separator=",">
            #{orderSn}
        </foreach>
        order by id asc
    </select>

</mapper>
