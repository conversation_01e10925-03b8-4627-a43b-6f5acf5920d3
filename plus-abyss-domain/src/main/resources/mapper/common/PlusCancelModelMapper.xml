<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusCancelModelMapper">

    <resultMap id="PlusCancelModel"
            type="com.juzifenqi.plus.module.common.repository.po.PlusCancelModelPo">
        <result column="id" property="id"/>
        <result column="profit_name" property="profitName"/>
        <result column="profit_type" property="profitType"/>
        <result column="model_id" property="modelId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `profit_name`,
        `profit_type`,
        `model_id`,
        `create_time`
    </sql>

    <select id="getCancelModels"
            resultType="com.juzifenqi.plus.module.common.repository.po.PlusCancelModelPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_cancel_model
    </select>

</mapper>
