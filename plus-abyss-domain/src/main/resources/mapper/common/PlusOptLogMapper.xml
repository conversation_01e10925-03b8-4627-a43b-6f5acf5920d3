<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusOptLogMapper">

    <resultMap id="PlusOptLog" type="com.juzifenqi.plus.module.common.repository.po.PlusOptLogPo">
        <result column="id" property="id"/>
        <result column="business_id" property="businessId"/>
        <result column="opt_type" property="optType"/>
        <result column="business_type" property="businessType"/>
        <result column="opt_name" property="optName"/>
        <result column="bef_content" property="befContent"/>
        <result column="aft_content" property="aftContent"/>
        <result column="create_user" property="createUser"/>
        <result column="create_user_id" property="createUserId"/>
        <result column="create_time" property="createTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`,
        `business_id`,
        `opt_type`,
        `business_type`,
        `opt_name`,
        `bef_content`,
        `aft_content`,
        `create_user`,
        `create_user_id`,
        `create_time`
    </sql>

    <insert id="insert" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusOptLogPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO plus_opt_log (
        `business_id`,
        `opt_type`,
        `opt_name`,
        `business_type`,
        `bef_content`,
        `aft_content`,
        `create_user`,
        `create_user_id`,
        `create_time`
        )
        VALUES(
        #{businessId},
        #{optType},
        #{optName},
        #{businessType},
        #{befContent},
        #{aftContent},
        #{createUser},
        #{createUserId},
        NOW()
        )
    </insert>

    <select id="selectList" resultMap="PlusOptLog">
        select
        <include refid="Base_Column_List"/>
        from plus_opt_log where business_id = #{businessId} and business_type = #{businessType}
        order by id
        desc
    </select>
</mapper>