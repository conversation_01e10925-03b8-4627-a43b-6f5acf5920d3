<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.MemberPlusSwitchControlMapper">

    <resultMap id="MemberPlusSwitchControl"
            type="com.juzifenqi.plus.module.common.repository.po.MemberPlusSwitchControlPo">
        <result column="id" property="id"/>
        <result column="switch_name" property="switchName"/>
        <result column="switch_type" property="type"/>
        <result column="config_id" property="configId"/>
        <result column="status" property="status"/>
        <result column="back_mode" property="backMode"/>
        <result column="delay_time" property="delayTime"/>
        <result column="explain" property="explain"/>
        <result column="deduct_delay" property="deductDelay"/>
        <result column="msg_delay" property="msgDelay"/>
        <result column="deduct_type" property="deductType"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        `id`
        ,
        `switch_name`,
        `switch_type`,
        `config_id`,
        `status`,
        `back_mode`,
        `delay_time`,
        `explain`,
        `deduct_delay`,
        `msg_delay`,
        `deduct_type`,
        `create_time`,
        `update_time`
    </sql>

    <insert id="saveMemberPlusSwitchControl"
            parameterType="com.juzifenqi.plus.module.common.repository.po.MemberPlusSwitchControlPo"
            useGeneratedKeys="true" keyProperty="id">
        INSERT INTO member_plus_switch_control (`switch_name`,
                                                `switch_type`,
                                                `config_id`,
                                                `status`,
                                                `back_mode`,
                                                `delay_time`,
                                                `explain`,
                                                `deduct_delay`,
                                                `msg_delay`,
                                                `deduct_type`,
                                                `create_time`,
                                                `update_time`)
        VALUES (#{switchName},
                #{type},
                #{configId},
                #{status},
                #{backMode},
                #{delayTime},
                #{explain},
                #{deductDelay},
                #{msgDelay},
                #{deductType},
                NOW(),
                #{updateTime})
    </insert>

    <update id="updateMemberPlusSwitchControl">
        <foreach collection="list" index="index" item="record" separator=";">
            update member_plus_switch_control
            set `status` = #{record.status},
            <if test="record.backMode != null">
                `back_mode` = #{record.backMode},
            </if>
            <if test="record.delayTime != null">
                `delay_time` = #{record.delayTime},
            </if>
            <if test="record.deductDelay != null">
                `deduct_delay` = #{record.deductDelay},
            </if>
            <if test="record.msgDelay != null">
                `msg_delay` = #{record.msgDelay},
            </if>
            <if test="record.deductType != null">
                `deduct_type` = #{record.deductType},
            </if>
            update_time = NOW()
            where id = #{record.id}
        </foreach>
    </update>

    <select id="loadMemberPlusSwitchControl"
            resultType="com.juzifenqi.plus.module.common.repository.po.MemberPlusSwitchControlPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_switch_control
        where `switch_type`=#{type}
    </select>

    <select id="getMemberPlusSwitchByType"
            resultType="com.juzifenqi.plus.module.common.repository.po.MemberPlusSwitchControlPo">
        SELECT
        <include refid="Base_Column_List"/>
        FROM member_plus_switch_control
        where `config_id` = #{configId}
    </select>

    <select id="getSwitchTypeById" resultType="integer">
        SELECT
            switch_type
        FROM member_plus_switch_control
        where id = #{id}
    </select>
</mapper>
