<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.juzifenqi.plus.module.common.repository.dao.IPlusPayBlackListTaskMapper">

    <resultMap id="BaseResultMap"
            type="com.juzifenqi.plus.module.common.repository.po.PlusPayBlackListTaskPo">
        <result column="id" property="id"/>
        <result column="user_id" property="userId"/>
        <result column="channel_id" property="channelId"/>
        <result column="opt_user_id" property="optUserId"/>
        <result column="opt_user_name" property="optUserName"/>
        <result column="config_id" property="configId"/>
        <result column="state" property="state"/>
        <result column="retry_num" property="retryNum"/>
        <result column="create_time" property="createTime"/>
        <result column="update_time" property="updateTime"/>
    </resultMap>

    <sql id="Base_Column_List">
        id
        ,
     user_id,
     channel_id,
     opt_user_id,
     opt_user_name,
     config_id,
     `state`,
     retry_num,
     create_time,
     update_time
    </sql>

    <insert id="insert" useGeneratedKeys="true" keyColumn="id" keyProperty="id" parameterType="com.juzifenqi.plus.module.common.repository.po.PlusPayBlackListTaskPo">
        INSERT INTO plus_pay_black_list_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="null != userId and '' != userId">
                user_id,
            </if>
            <if test="null != channelId and '' != channelId">
                channel_id,
            </if>
            <if test="null != optUserId and '' != optUserId">
                opt_user_id,
            </if>
            <if test="null != optUserName and '' != optUserName">
                opt_user_name,
            </if>
            <if test="null != configId and '' != configId">
                config_id,
            </if>
            <if test="null != state and '' != state">
                `state`,
            </if>
            <if test="null != retryNum">
                retry_num,
            </if>
                create_time,
                update_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="null != userId and '' != userId">
                #{userId},
            </if>
            <if test="null != channelId and '' != channelId">
                #{channelId},
            </if>
            <if test="null != optUserId and '' != optUserId">
                #{optUserId},
            </if>
            <if test="null != optUserName and '' != optUserName">
                #{optUserName},
            </if>
            <if test="null != configId and '' != configId">
                #{configId},
            </if>
            <if test="null != state and '' != state">
                #{state},
            </if>
            <if test="null != retryNum">
                #{retryNum},
            </if>
                now(),
                now()
        </trim>
    </insert>

    <update id="updateStateBatch">
        update plus_pay_black_list_task set `state` = #{state}, update_time = now()
        where
        <foreach collection="ids" item="id" open="id in (" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <update id="updateFailStateBatch">
        update plus_pay_black_list_task set `state` = #{state} , retry_num = retry_num + 1, update_time = now()
        where
        <foreach collection="ids" item="id" open="id in (" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <select id="load" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_pay_black_list_task
        WHERE id = #{id}
    </select>

    <select id="pageList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM plus_pay_black_list_task
        where `state` = #{state} and retry_num &lt;= 5
        <if test="state == 1">
            and date_sub( now(),interval 1 day) > create_time
        </if>
        LIMIT #{offset}, #{pageSize}
    </select>


</mapper>