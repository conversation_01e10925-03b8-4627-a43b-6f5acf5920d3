#dubbo
dubbo:
  application:
    name: plus-abyss-provider-prd
  registry:
    protocol: zookeeper
    address: zookeeper://************:2181?backup=************:2181,*************:2181
  # 启动服务时不检查提供者是否存在
  consumer:
    check: false
    # 远程调用默认超时时间
    timeout: 5000
  protocol:
    name: dubbo
    port: -1
  service:
    delay: 30000
  reference:
    retries: 0
    timeout: 5000
  #以下为dubbo相关的业务配置
  group: prd
  member:
    group: member-platform
  sms:
    group: sms_prd
  auth:
    group: auth_prd
  coupon:
    group: coupon-group-prd
  product:
    group: product-group-prd
    cache:
      group: productSearch-prd
  credit:
    group: prod
  order:
    group: super-order-group-prd
  virtual:
    group: virtual-center-prd
  alpha:
    group: plus-aplha-prd
  acm:
    group: acm-prd
  support:
    group: support-prd
  contract:
    group: prd
  onlineShop:
    group: online-core-prd
  mallTrade:
    group: mall-trade-group-prod
  act:
    group: activity-provider-prd
  workOrder:
    group: work-order-prd
  market:
    group: plus-magic-prd
  rms:
    group: prod
  omsTaker:
    group: oms-taker
