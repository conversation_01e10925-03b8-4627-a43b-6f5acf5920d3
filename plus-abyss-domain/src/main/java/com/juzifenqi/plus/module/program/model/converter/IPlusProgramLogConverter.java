package com.juzifenqi.plus.module.program.model.converter;

import com.juzifenqi.plus.module.common.repository.po.PlusProgramLogPo;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramLogEntity;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 方案操作日志
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 11:05
 */
@Mapper
public interface IPlusProgramLogConverter {

    IPlusProgramLogConverter instance = Mappers.getMapper(IPlusProgramLogConverter.class);

    List<PlusProgramLogEntity> toPlusProgramLogEntityList(List<PlusProgramLogPo> list);
}
