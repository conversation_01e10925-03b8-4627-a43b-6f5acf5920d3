package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusLiftAmountRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusLiftAmountConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProModelMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusLiftAmountMapper;
import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class PlusLiftAmountRepositoryImpl implements IPlusLiftAmountRepository {

    PlusLiftAmountConverter converter = PlusLiftAmountConverter.instance;

    @Autowired
    private IPlusLiftAmountMapper plusLiftAmountMapper;
    @Autowired
    private IPlusProModelMapper   plusProModelMapper;


    @Override
    public PlusLiftAmountPo getByProgramId(Integer programId) {
        return plusLiftAmountMapper.getPlusLiftAmountByProgramId(programId);
    }

    @Override
    public PlusProModelEntity getPlusProModelDetail(CreatePlusProModelEvent event) {
        //获取权益基本信息
        PlusProModelPo po = plusProModelMapper.getPlusProModelByVo(event.getProgramId(),
                event.getModelId());
        PlusProModelEntity entity = converter.toPlusProModelEntity(po);
        PlusLiftAmountPo plusLiftAmount = plusLiftAmountMapper.getPlusLiftAmountByProgramId(
                event.getProgramId());
        entity.setPlusLiftAmount(plusLiftAmount);
        return entity;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveLiftAmount(CreatePlusProModelEvent event) {
        plusProModelMapper.updateMemberPlusProModel(converter.toPlusProModelPo(event));
        plusLiftAmountMapper.deletePlusLiftAmountByProgramId(event.getProgramId());
        plusLiftAmountMapper.savePlusLiftAmount(
                converter.toPlusLiftAmountPo(event.getPlusLiftAmount()));
    }
}
