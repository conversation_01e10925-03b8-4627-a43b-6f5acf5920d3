package com.juzifenqi.plus.module.program.repository.impl;


import com.juzifenqi.plus.module.program.model.contract.IPlusConfigRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusConfigEntity;
import com.juzifenqi.plus.module.program.repository.converter.IPlusConfigRepositoryConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusConfigMapper;
import java.util.List;
import javax.annotation.Resource;
import org.springframework.stereotype.Repository;

/**
 * PlusConfV3RepositoryImpl
 *
 * <AUTHOR>
 * @Description
 * @Date 2024/06/14 11:08
 **/
@Repository
public class PlusConfigRepositoryImpl implements IPlusConfigRepository {

    private final IPlusConfigRepositoryConverter converter = IPlusConfigRepositoryConverter.instance;

    @Resource
    private IPlusConfigMapper mapper;

    @Override
    public List<PlusConfigEntity> loadPlusConf() {
        return converter.toPlusConfigEntityList(mapper.getPlusConfigList());
    }
}
