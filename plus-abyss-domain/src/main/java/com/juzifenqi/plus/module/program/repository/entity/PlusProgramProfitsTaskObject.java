package com.juzifenqi.plus.module.program.repository.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 会员权益：任务类达成类权益
 */
@Data
public class PlusProgramProfitsTaskObject {

    private Long programId;

    private Long packageId;

    /**
     * 权益类型（拒就赔、多买多送、购物返现）
     */
    private Integer profitType;

    /**
     * 达成条件
     */
    private PlusReachConditionObject plusReachCondition;


    /**
     * 券id
     */
    private Long couponId;

    /**
     * 现金奖励类
     */
    private BigDecimal cashGiftAmount;
}
