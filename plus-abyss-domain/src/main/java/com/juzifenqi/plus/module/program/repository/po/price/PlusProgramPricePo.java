package com.juzifenqi.plus.module.program.repository.po.price;

import java.util.Date;
import lombok.Data;

/**
 * 会员定价主表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 17:36
 */
@Data
public class PlusProgramPricePo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道标识 1-宜口袋 2-桔多多
     */
    private Integer bizSource;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 方案id
     */
    private Integer configId;

    /**
     * 定价类型 1：默认方案价 2：差异化定价
     */
    private Integer priceType;

    /**
     * 提额等级顺序 1_399-199  2_199-399
     */
    private Integer grade;
}
