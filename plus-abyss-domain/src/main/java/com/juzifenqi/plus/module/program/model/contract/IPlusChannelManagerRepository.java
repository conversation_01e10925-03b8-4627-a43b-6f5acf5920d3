package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.dto.req.admin.program.ChannelManagerQueryReq;
import com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelManagerEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo;
import java.util.List;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 11:16
 */
public interface IPlusChannelManagerRepository {

    /**
     * 是否支持发送短信
     */
    PlusChannelFunctionPo getConfig(Integer channelId, Integer configId);

    /**
     * 获取配置的渠道列表
     */
    List<PlusChannelManagerEntity> getChannelList(ChannelManagerQueryReq req);

    /**
     * 获取渠道配置
     */
    PlusChannelManagePo getByChannelId(Integer channelId);

    /**
     * 获取渠道配置
     */
    PlusChannelManagePo getById(Integer id);

    /**
     * 保存
     */
    void savePlusChannelManage(PlusChannelManagePo po);

    /**
     * 获取渠道功能配置
     */
    List<PlusChannelFunctionPo> getFunctionList(Integer managerId);

    /**
     * 批量保存渠道功能配置
     */
    void batchSaveFunction(List<PlusChannelFunctionPo> list);

    /**
     * 修改渠道管理配置
     */
    void updateChannelManager(PlusChannelManagePo po);

    /**
     * 获取渠道管理和功能配置
     */
    PlusChannelManagerEntity getManagerAndFunctionById(Integer id);

    /**
     * 获取配置的渠道分页列表
     */
    List<PlusChannelManagerEntity> getChannelListPage(ChannelManagerQueryReq req);

    /**
     * 分页总数量
     */
    Integer countChannelListPage(ChannelManagerQueryReq req);

    /**
     * 通过渠道id获取渠道功能配置
     */
    List<PlusChannelFunctionPo> getFunctionListByChannelId(Integer channelId);
}
