package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramHalfPriceProductPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @createTime 2024/2/28 18:05
 * @description
 */
@Mapper
public interface IPlusProgramHalfPriceProductMapper {

    List<PlusProgramHalfPriceProductPo> getHalfPriceProduct(@Param("start") Integer start,
            @Param("size") Integer pageSize);

    Integer getHalfPriceProductCount();

    /**
     * 获取半价商品ID
     */
    List<Integer> halfPriceProductIdList();

    void savePlusProgramHalfPriceProduct(@Param("list")List<PlusProgramHalfPriceProductPo> poList);

    Integer getByProductCodeById(@Param("id") Integer id, @Param("programId") Integer programId);

    Boolean deletePlusProgramHalfPriceProduct(@Param("id")Integer id);

    void updatePlusProgramHalfPriceProduct(@Param("product") PlusProgramHalfPriceProductPo po);

    /**
     * 通过商品sku+方案id获取数据
     */
    List<PlusProgramProductNewPo> getByProductSkuAndProgramId(@Param("programId") Integer programId,
            @Param("productSku") String productSku, @Param("modelId") Integer modelId);

    /**
     * 通过商品id+方案id获取数据
     */
    List<PlusProgramProductNewPo> getByProductIdAndProgramId(@Param("programId") Integer programId,
            @Param("productId") Integer productId, @Param("modelId") Integer modelId);

}
