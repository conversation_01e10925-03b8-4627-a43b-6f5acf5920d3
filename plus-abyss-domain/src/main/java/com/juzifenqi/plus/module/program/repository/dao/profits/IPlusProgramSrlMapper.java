package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramSrlPo;
import org.apache.ibatis.annotations.Mapper;

/**
 * <AUTHOR>
 * @createTime 2024/2/28 18:14
 * @description
 */
@Mapper
public interface IPlusProgramSrlMapper {

    /**
     * 新增返回ID
     */
    int insert(PlusProgramSrlPo po);

    /**
     * 更新
     */
    int update(PlusProgramSrlPo po);

    /**
     * 按方案id查询
     */
    PlusProgramSrlPo selectByProgramId(Integer programId);

    /**
     * 按方案id查询
     */
    int countByProgramId(Integer programId);
}
