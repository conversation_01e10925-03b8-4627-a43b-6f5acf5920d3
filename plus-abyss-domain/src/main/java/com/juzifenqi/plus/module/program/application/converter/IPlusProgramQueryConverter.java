package com.juzifenqi.plus.module.program.application.converter;

import com.juzifenqi.plus.dto.resp.admin.program.PlusProgramResp;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 方案查询转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/7/3 09:55
 */
@Mapper
public interface IPlusProgramQueryConverter {

    IPlusProgramQueryConverter instance = Mappers.getMapper(IPlusProgramQueryConverter.class);

    PlusProgramResp toPlusProgramResp(PlusProgramEntity entity);
}
