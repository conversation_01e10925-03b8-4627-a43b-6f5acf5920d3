package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.entity.PlusProgramTxEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramTxEvent;
import java.util.List;

/**
 * 通信联名卡
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/14 18:23
 */
public interface IPlusProgramTxLmkRepository {

    /**
     * 保存
     */
    void saveTx(List<CreatePlusProgramTxEvent> list);

    /**
     * 获取配置
     */
    List<PlusProgramTxEntity> getList(Integer programId);

    /**
     * 删除
     */
    void deleteById(Integer id);

    /**
     * 修改
     */
    void update(CreatePlusProgramTxEvent event);
}
