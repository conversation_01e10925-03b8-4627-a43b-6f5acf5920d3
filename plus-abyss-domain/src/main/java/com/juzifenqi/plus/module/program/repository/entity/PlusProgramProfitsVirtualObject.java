package com.juzifenqi.plus.module.program.repository.entity;

import com.juzifenqi.plus.enums.PlusProfitsGroupEnum;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> 会员权益：虚拟货币类权益
 */
@Data
public class PlusProgramProfitsVirtualObject {

    private Integer profitsId;

    private Integer programId;

    private Integer modelId;

    private Integer packageId;

    /**
     * 权益类型（桔豆）
     */
    private Integer profitType = PlusProfitsGroupEnum.PLUS_VIRTUAL.getValue();

    /**
     * 达成条件
     */
    private List<PlusReachConditionObject> plusReachCondition;

    /**
     * 虚拟货币数量
     */
    private BigDecimal virtualNum;
}
