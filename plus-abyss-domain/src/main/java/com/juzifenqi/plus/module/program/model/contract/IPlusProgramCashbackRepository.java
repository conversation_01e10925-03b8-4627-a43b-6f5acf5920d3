package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramCashbackEntity;
import java.util.List;

/**
 * 返现配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 15:11
 */
public interface IPlusProgramCashbackRepository {

    /**
     * 获取配置数量
     */
    int countByProgramId(Integer programId, Integer modelId);

    /**
     * 获取返现配置
     */
    List<PlusProgramCashbackEntity> getByProgram(Integer programId, Integer modelId);

    /**
     * 获取方案配置的返现信息
     */
    List<PlusProgramCashbackEntity> getByProgramIdAndModelId(Integer programId, Integer modelId);
}
