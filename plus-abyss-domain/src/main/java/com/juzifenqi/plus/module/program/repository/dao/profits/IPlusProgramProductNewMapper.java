package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 新会员商品表
 *
 * <AUTHOR>
 * @date 2023/05/06 13:45
 */
@Mapper
public interface IPlusProgramProductNewMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusProgramProductNew(PlusProgramProductNewPo plusProgramProductNew);

    /**
     * 更新
     */
    Integer updatePlusProgramProductNew(
            @Param("plusProgramProductNew") PlusProgramProductNewPo plusProgramProductNew);

    /**
     * 根据modelId和productId更新状态
     */
    Integer updateStateByModelAndProduct(@Param("modelId") Integer modelId,
            @Param("productId") Integer productId, @Param("onSaleState") Integer onSaleState);

    /**
     * 根据分类id查询商品关系
     */
    List<PlusProgramProductNewPo> selectByTypeId(@Param("typeId") Integer typeId,
            @Param("modelId") Integer modelId);

    /**
     * 根据方案id查询商品关系
     */
    List<PlusProgramProductNewPo> selectByProgramId(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    /**
     * 通过商品id+方案id获取数据
     */
    PlusProgramProductNewPo getByProductSkuAndProgramId(@Param("programId") Integer programId,
            @Param("productSku") String productSku, @Param("modelId") Integer modelId);

    /**
     * 删除
     */
    Integer deletePlusProgramProductNew(@Param("id") Integer id);

    /**
     * Load查询
     */
    PlusProgramProductNewPo loadPlusProgramProductNew(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusProgramProductNewPo> pageList(@Param("offset") Integer offset,
            @Param("pagesize") Integer pagesize);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("offset") Integer offset, @Param("pagesize") Integer pagesize);

    Integer getCountByProgramId(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);


    /**
     * 获取半价商品ID
     */
    List<Integer> yygProductIdList(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    /**
     * 获取商品权益
     */
    List<PlusProgramProductNewPo> yygProductList(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId, @Param("typeId") Integer typeId);

    /**
     * 批量新增
     */
    int batchSaveYygProduct(@Param("list") List<PlusProgramProductNewPo> list);

    /**
     * 通过商品id+方案id获取数据
     */
    PlusProgramProductNewPo getByProductIdAndProgramId(@Param("programId") Integer programId,
            @Param("productId") Integer productId, @Param("modelId") Integer modelId);

    /**
     * 批量删除
     */
    int batchDelYygProduct(@Param("list") List<PlusProgramProductNewPo> list);

    /**
     * 获取所有一元购商品id
     */
    List<Integer> getAllProductId(@Param("modelId") Integer modelId);

    /**
     * 根据方案id+权益id删除商品关系
     */
    Integer delByProgramAndModelAndType(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId, @Param("typeId") Integer typeId);

    /**
     * 根据商品id删除关联商品
     */
    Integer delByProductSkuAndModelId(@Param("productSku") String productSku,
            @Param("modelId") Integer modelId);

    /**
     * 根据modelId和productId更新状态
     */
    Integer updateStateByModelAndProduct(@Param("modelId") Integer modelId,
            @Param("productSku") String productSku, @Param("onSaleState") Integer onSaleState);

    Integer batchSaveProductNew(@Param("list") List<PlusProgramProductNewPo> list);

    List<PlusProgramProductNewPo> getProductNewList(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId, @Param("typeId") Integer typeId);

    /**
     * 更新
     */
    Integer updateProductNew(@Param("plusProgramProductNew") PlusProgramProductNewPo po);

    List<PlusProgramProductNewPo> getByProgramAndModel(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);
}
