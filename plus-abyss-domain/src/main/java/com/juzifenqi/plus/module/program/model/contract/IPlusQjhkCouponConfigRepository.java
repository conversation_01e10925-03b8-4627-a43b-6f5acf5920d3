package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.entity.PlusQjhkCouponConfigEntity;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigStateEvent;
import java.util.List;

/**
 * 区间还款优惠券配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9 17:37
 */
public interface IPlusQjhkCouponConfigRepository {

    /**
     * 保存数据
     */
    Integer save(PlusQjhkCouponConfigEntity entity);

    /**
     * 获取生效的配置列表
     */
    List<PlusQjhkCouponConfigEntity> getEffectiveList();

    /**
     * 刷新配置
     */
    void qjhkCouponConfigRefresh();

    /**
     * 全部列表
     */
    List<PlusQjhkCouponConfigEntity> getPageList(Integer periodNum, Integer start, Integer size);

    /**
     * 总数量
     */
    Integer countList(Integer periods);

    /**
     * 根据id查询
     */
    PlusQjhkCouponConfigEntity getById(Integer id);

    /**
     * 修改数据
     */
    int updateById(EditQjhkCouponConfigEvent event);

    /**
     * 修改状态
     */
    int updateStateById(EditQjhkCouponConfigStateEvent event);

}
