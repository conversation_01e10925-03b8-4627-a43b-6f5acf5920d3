package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.PlusProgramExtendPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @description: 方案扩展表
 * @date 2022/2/22 15:04
 */
@Mapper
public interface IPlusProgramExtendMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusProgramExtend(PlusProgramExtendPo plusProgramExtend);

    /**
     * 删除
     */
    Integer deletePlusProgramExtend(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusProgramExtend(
            @Param("plusProgramExtend") PlusProgramExtendPo plusProgramExtend);

    /**
     * Load查询
     */
    PlusProgramExtendPo loadPlusProgramExtend(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusProgramExtendPo> pageList(@Param("offset") Integer offset,
            @Param("pagesize") Integer pagesize);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("offset") Integer offset, @Param("pagesize") Integer pagesize);

    /**
     * 根据方案id查扩展信息
     */
    PlusProgramExtendPo getProgramExtendByPid(@Param("programId") Integer programId);

}
