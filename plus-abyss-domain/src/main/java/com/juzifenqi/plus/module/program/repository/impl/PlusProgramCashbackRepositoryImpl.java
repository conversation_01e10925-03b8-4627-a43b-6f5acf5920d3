package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusProgramCashbackRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.program.repository.converter.IPlusProgramCashbackConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramCashbackMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 返现配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 15:13
 */
@Repository
public class PlusProgramCashbackRepositoryImpl implements IPlusProgramCashbackRepository {

    private final IPlusProgramCashbackConverter converter = IPlusProgramCashbackConverter.instance;

    @Autowired
    private IPlusProgramCashbackMapper cashbackMapper;

    @Override
    public int countByProgramId(Integer programId, Integer modelId) {
        return cashbackMapper.countByProgramId(programId, modelId);
    }

    @Override
    public List<PlusProgramCashbackEntity> getByProgram(Integer programId, Integer modelId) {
        List<PlusProgramCashbackPo> list = cashbackMapper.queryByProgramId(programId, modelId);
        return converter.toPlusProgramCashbackEntityList(list);
    }

    @Override
    public List<PlusProgramCashbackEntity> getByProgramIdAndModelId(Integer programId,
            Integer modelId) {
        List<PlusProgramCashbackPo> list = cashbackMapper.queryByProgramId(programId, modelId);
        return converter.toList(list);
    }
}
