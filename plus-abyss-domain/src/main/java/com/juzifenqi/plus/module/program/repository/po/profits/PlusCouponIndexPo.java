package com.juzifenqi.plus.module.program.repository.po.profits;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 会员权益优惠券(开卡礼、月享、品牌专区、还款优惠、生日关怀、专属好礼)
 * <AUTHOR>
 *
 */
@Data
public class PlusCouponIndexPo implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 类型-开卡礼
     */
    public static final Integer TYPE_KKL = 1;
    /**
     * 类型-月享
     */
    public static final Integer TYPE_YX  = 2;
    /**
     * 类型-品牌专区
     */
    public static final Integer TYPE_PP  = 3;

    /**
     * 类型-还款优惠
     */
    public static final Integer TYPE_HK = 4;

    /**
     * 类型-生日关怀
     */
    public static final Integer TYPE_SR = 5;

    /**
     * 类型-专属好礼
     */
    public static final Integer TYPE_ZSHL = 6;


    /**
     *
     */
    private Integer id;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 类型（1.开卡礼 2.月享 3.品牌专区 4.还款优惠）
     */
    private Integer type;

    /**
     * 优惠券ID
     */
    private Integer couponId;

    /**
     *
     */
    private Date createTime;

    /**
     *
     */
    private Date updateTime;

    private Date useStartTime;

    private Date useEndTime;

    private Integer validDays;

    private Integer totalLimitNum;

    private Integer receivedNum;

    private Integer alreadyGet;

    private String couponName;

    private String brandName;

    private Integer index;

    private Integer lastNumFormat;

    private String useTime;
}
