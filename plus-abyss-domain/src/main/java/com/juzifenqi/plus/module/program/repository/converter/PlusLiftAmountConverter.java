package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusLiftAmountEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PlusLiftAmountConverter {

    PlusLiftAmountConverter instance = Mappers.getMapper(PlusLiftAmountConverter.class);


    PlusProModelPo toPlusProModelPo(CreatePlusProModelEvent event);

    PlusProModelEntity toPlusProModelEntity(PlusProModelPo po);

    PlusLiftAmountPo toPlusLiftAmountPo(PlusLiftAmountEntity entity);
}
