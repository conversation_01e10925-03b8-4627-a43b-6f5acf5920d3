package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramTaskEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 优惠券转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 10:41
 */
@Mapper
public interface PlusProgramCouponConverter {

    PlusProgramCouponConverter instantiate = Mappers.getMapper(PlusProgramCouponConverter.class);

    List<PlusProgramXFZKPo> toXFZFPoList(List<CreateProgramCouponEvent> list);

    List<PlusProgramTaskPo> toTaskPoList(List<CreateProgramCouponEvent> list);

    List<PlusProgramRejectionPo> toRejectionPoList(List<CreateProgramCouponEvent> list);

    List<PlusProgramCouponEntity> toPlusProgramCouponEntity(List<PlusProgramXFZKPo> list);

    List<PlusProgramCouponEntity> toPlusProgramCouponEntity2(List<PlusProgramTaskPo> list);

    List<PlusProgramCouponEntity> toPlusProgramCouponEntity3(List<PlusProgramRejectionPo> list);

    List<PlusProgramTaskEntity> toPlusProgramTaskEntityList(List<PlusProgramTaskPo> list);

}
