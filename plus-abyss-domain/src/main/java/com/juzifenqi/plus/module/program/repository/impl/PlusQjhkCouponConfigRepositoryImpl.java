package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.program.model.contract.IPlusQjhkCouponConfigRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusQjhkCouponConfigEntity;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigStateEvent;
import com.juzifenqi.plus.module.program.repository.converter.IPlusQjhkCouponConfigConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusQjhkCouponConfigMapper;
import com.juzifenqi.plus.module.program.repository.po.PlusQjhkCouponConfigPo;
import com.juzifenqi.plus.utils.RedisLock;
import com.juzifenqi.plus.utils.RedisUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * 区间还款优惠券配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9 17:38
 */
@Slf4j
@Repository
public class PlusQjhkCouponConfigRepositoryImpl implements IPlusQjhkCouponConfigRepository {

    private final IPlusQjhkCouponConfigConverter converter = IPlusQjhkCouponConfigConverter.instance;

    @Autowired
    private IPlusQjhkCouponConfigMapper configMapper;
    @Autowired
    private RedisUtils                  redisUtils;
    @Autowired
    private RedisLock redisLock;

    /**
     * 保存数据
     */
    @Override
    public Integer save(PlusQjhkCouponConfigEntity entity) {
        PlusQjhkCouponConfigPo qjhkCouponConfigPo = converter.toPo(entity);
        configMapper.save(qjhkCouponConfigPo);
        return qjhkCouponConfigPo.getId();
    }

    /**
     * 获取生效的配置列表
     */
    @Override
    public List<PlusQjhkCouponConfigEntity> getEffectiveList() {
        String key = RedisConstantPrefix.QJHK_COUPON_CONFIG;
        String cache = redisUtils.get(key);
        log.info("获取生效的区间还款优惠券配置缓存结果：{}", cache);
        if (StringUtils.isNotBlank(cache)) {
            return JSONObject.parseArray(cache, PlusQjhkCouponConfigEntity.class);
        }
        List<PlusQjhkCouponConfigPo> list = configMapper.selectEffectiveList();
        if (CollectionUtils.isEmpty(list)) {
            return null;
        }
        List<PlusQjhkCouponConfigEntity> entityList = converter.toEntityList(list);
        redisUtils.set(key, JSONObject.toJSONString(entityList));
        return entityList;
    }

    /**
     * 刷新配置
     */
    @Override
    public void qjhkCouponConfigRefresh() {
        log.info("刷新区间还款优惠券配置开始");
        String key = RedisConstantPrefix.QJHK_COUPON_CONFIG_REFRESH;
        boolean lock = redisLock.lock(key, "1", 3);
        if (!lock) {
            log.info("刷新缓存过快，请稍后再试");
            throw new PlusAbyssException("刷新缓存过快，请稍后再试");
        }
        List<PlusQjhkCouponConfigPo> list = configMapper.selectEffectiveList();
        if (CollectionUtils.isEmpty(list)) {
            log.info("刷新区间还款优惠券配置列表为空");
            return;
        }
        List<PlusQjhkCouponConfigEntity> entityList = converter.toEntityList(list);
        redisUtils.set(RedisConstantPrefix.QJHK_COUPON_CONFIG, JSONObject.toJSONString(entityList));
        log.info("刷新区间还款优惠券配置结束");
    }

    /**
     * 全部列表
     */
    @Override
    public List<PlusQjhkCouponConfigEntity> getPageList(Integer periodNum, Integer start,
            Integer size) {
        List<PlusQjhkCouponConfigPo> pageList = configMapper.getPageList(periodNum, start, size);
        return converter.toPlusQjhkCouponConfigEntityList(pageList);
    }

    /**
     * 总数量
     */
    @Override
    public Integer countList(Integer periodNum) {
        return configMapper.countList(periodNum);
    }

    /**
     * 根据id查询
     */
    @Override
    public PlusQjhkCouponConfigEntity getById(Integer id) {
        return converter.toEntity(configMapper.getById(id));
    }

    /**
     * 修改数据
     */
    @Override
    public int updateById(EditQjhkCouponConfigEvent event) {
        return configMapper.updateById(converter.toPlusQjhkCouponConfigPo(event));
    }

    /**
     * 修改状态
     */
    @Override
    public int updateStateById(EditQjhkCouponConfigStateEvent event) {
        return configMapper.updateStateById(event.getId(), event.getState());
    }
}
