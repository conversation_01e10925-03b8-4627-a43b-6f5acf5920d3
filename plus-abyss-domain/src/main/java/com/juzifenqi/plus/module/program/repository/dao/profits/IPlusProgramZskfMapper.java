package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramZskfPo;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface IPlusProgramZskfMapper {

    /**
     * 新增返回ID
     */
    int insert(PlusProgramZskfPo po);

    /**
     * 按权益id查询
     */
    PlusProgramZskfPo selectByProgramId(Integer programId);

    /**
     * 按权益id查询
     */
    int countByProgramId(Integer programId);

    /**
     * 编辑
     */
    int update(PlusProgramZskfPo po);
}
