package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.dto.req.profits.QueryHalfPriceProductReq;
import com.juzifenqi.plus.dto.req.profits.QueryPlusProductReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusHalfPriceProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductNewEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductTypeEntity;
import com.juzifenqi.plus.module.program.model.event.CreateHalfPriceProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductNewEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductTypeEvent;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @createTime 2024/2/28 14:05
 * @description
 */
public interface IPlusProgramProductRepository {

    /**
     * 会员0元商品新增分类配置
     */
    void savePlusProductType(CreatePlusProductTypeEvent event);

    /**
     * 会员0元商品新增分类编辑
     */
    PlusProgramProductTypeEntity editPlusProductType(CreatePlusProductTypeEvent event);

    /**
     * 获取方案下配置的0元商品分类数据
     */
    PageResultEntity<PlusProgramProductTypeEntity> getPlusProductType(Integer programId,
            Integer modelId);

    /**
     * 会员0元商品分类删除
     */
    void delPlusProductType(CreatePlusProductTypeEvent event);

    /**
     * 获取商品分类
     */
    PlusProgramProductTypeEntity getProductTypeById(Integer id);

    /**
     * 获取半价商品
     */
    PageResultEntity<PlusHalfPriceProductEntity> getHalfPriceProductList(
            QueryHalfPriceProductReq req);

    /**
     * 保存半价商品
     */
    void savePlusProgramHalfPriceProduct(List<CreateHalfPriceProductEvent> events);

    /**
     * 删除半价商品
     */
    void deletePlusProgramHalfPriceProduct(Integer id);

    /**
     * 修改半价商品
     */
    void updatePlusProgramHalfPriceProduct(CreateHalfPriceProductEvent event);

    /**
     * 获取0元/一元购商品列表
     */
    List<PlusProgramProductNewEntity> getProductNewList(Integer programId, Integer modelId,
            Integer typeId);

    /**
     * 批量删除0元/1元购商品
     */
    List<PlusProgramProductNewEntity> batchDelYygProduct(List<CreatePlusProductNewEvent> list);

    /**
     * 修改0元/1元购商品
     */
    PlusProgramProductNewEntity editPlusProduct(CreatePlusProductNewEvent event);

    /**
     * 新增0元/1元购商品
     */
    List<PlusProgramProductNewEntity> batchSaveYygProduct(List<CreatePlusProductNewEvent> list);

    /**
     * 获取已存在的0元/1元购商品
     */
    List<String> getExitProductNewList(List<CreatePlusProductNewEvent> list,
            List<CreatePlusProductNewEvent> targetList);

    /**
     * 会员0元商品池列表(分页)
     */
    PageResultEntity<PlusProgramProductEntity> selectPlusProductList(
            QueryPlusProductReq plusProductVo);

    /**
     * 新增会员0元商品池的商品
     */
    void savePlusProduct(CreatePlusProductEvent event);

    /**
     * 根据sku查询商品池商品
     */
    PlusProgramProductEntity selectProductBySku(String sku);

    /**
     * 根据id查询商品池商品
     */
    PlusProgramProductEntity selectProductById(Integer id);

    /**
     * 编辑会员0元商品池的商品
     */
    PlusProgramProductEntity updatePlusProduct(CreatePlusProductEvent event);

    /**
     * 删除会员0元商品池的商品
     */
    PlusProgramProductEntity delPlusProduct(CreatePlusProductEvent event);

    /**
     * 商品池会员0元商品上架/下架
     */
    PlusProgramProductEntity updatePlusProductState(CreatePlusProductEvent event);

    /**
     * 根据方案和权益类型id获取商品列表
     */
    List<PlusProgramProductNewEntity> getProductNewByProgramAndModel(Integer programId,
            Integer modelId);

    /**
     * 获取半价商品数量
     */
    int getHalfPriceProductCount();

    /**
     * 获取半价商品ID
     */
    List<Integer> getHalfPriceProductIdList();

    /**
     * 获取一元购商品ID
     */
    List<Integer> yygProductIdList(Integer programId, Integer modelId);

    /**
     * 根据权益id获取商品ID列表
     */
    List<Integer> getAllProductId(Integer modelId);
}
