package com.juzifenqi.plus.module.program.repository.entity;

import com.juzifenqi.plus.enums.PlusMemberProfitsGivePeriodsTypeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusProfitsGiveTypeEnum;
import com.juzifenqi.plus.enums.PlusProgramSendTypeEnum;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusProgramProfitsPackageObject {

    /**
     * 权益包id
     */
    private Integer packageId;

    private Integer modelId;

    /**
     * 权益类型枚举值
     */
    private PlusModelEnum plusModel;

    /**
     * 方案id （聚合根）
     */
    private Integer programId;
    /**
     * 发放节点 1_开卡即发 2_后付款支付成功发放
     *
     * @see PlusProgramSendTypeEnum
     */
    private Integer sendNode;

    /**
     * 发放周期
     *
     * @see com.juzifenqi.plus.enums.PlusMemberProfitsGivePeriodsTypeEnum
     */
    private PlusMemberProfitsGivePeriodsTypeEnum givePeriod;

    /**
     * 发放类型(系统发放、用户领取)
     */
    private PlusProfitsGiveTypeEnum giveType;

    /**
     * 限制类型（无限制、每月限制、累计限制）
     */
    private Integer limitType = -1;

    /**
     * 限制的总次数
     */
    private Integer totalLimitCount = -1;

    /**
     * 限制的种类次数
     */
    private Integer categoryLimitCount = -1;

    /**
     * 权益信息-券
     */
    private List<PlusProgramProfitsCouponObject> coupons;

    /**
     * 权益信息-会员商品
     */
    private List<PlusProgramProfitsProductObject> plusProducts;

    /**
     * 权益信息-基础权益（加速、融单还款券）
     */
    private PlusProgramProfitsTypeObject plusSpeed;

    /**
     * 权益信息-额度
     */
    private List<PlusProgramProfitsCreditObject> plusCredits;

    /**
     * 权益信息-任务达成类（多买多送、拒就赔）
     */
    private List<PlusProgramProfitsTaskObject> plusTask;

    /**
     * 返现类权益
     */
    private List<PlusProgramProfitsCashbackObject> plusCashBacks;

    /**
     * 虚拟货币（目前是桔豆）
     */
    private List<PlusProgramProfitsVirtualObject> virtual;

    /**
     * 虚拟商品（目前是权益0元发放）
     */
    private List<PlusProgramProfitsVirtualGoodsObject> virtualGoods;


    /**
     * 6类券构造权益包
     */
    public static PlusProgramProfitsPackageObject createFromCoupon(
            List<PlusProgramProfitsCouponObject> coupons, PlusModelEnum plusModel,
            Integer programId) {
        PlusProgramProfitsPackageObject o = new PlusProgramProfitsPackageObject();
        o.setProgramId(programId);
        o.setPackageId(plusModel.getModelId());
        o.setModelId(plusModel.getModelId());
        o.setPlusModel(plusModel);
        o.setGivePeriod(plusModel.getGivePeriodsTypeEnum());
        o.setGiveType(plusModel.getGiveTypeEnum());
        o.setCoupons(coupons);
        return o;
    }

    /**
     * 提额类构造权益包
     */
    public static PlusProgramProfitsPackageObject createFromLiftAmount(
            List<PlusProgramProfitsCreditObject> liftAmounts, PlusModelEnum plusModel,
            Integer programId) {
        PlusProgramProfitsPackageObject o = new PlusProgramProfitsPackageObject();
        o.setProgramId(programId);
        o.setPackageId(plusModel.getModelId());
        o.setModelId(plusModel.getModelId());
        o.setPlusModel(plusModel);
        o.setGivePeriod(plusModel.getGivePeriodsTypeEnum());
        o.setGiveType(plusModel.getGiveTypeEnum());
        o.setPlusCredits(liftAmounts);
        return o;
    }

    /**
     * 返现类构造权益包
     */
    public static PlusProgramProfitsPackageObject createFromCashBack(
            List<PlusProgramProfitsCashbackObject> cashBacks, PlusModelEnum plusModel,
            Integer programId) {
        PlusProgramProfitsPackageObject o = new PlusProgramProfitsPackageObject();
        o.setProgramId(programId);
        o.setPackageId(plusModel.getModelId());
        o.setModelId(plusModel.getModelId());
        o.setPlusModel(plusModel);
        o.setGivePeriod(plusModel.getGivePeriodsTypeEnum());
        o.setGiveType(plusModel.getGiveTypeEnum());
        o.setPlusCashBacks(cashBacks);
        return o;
    }

    /**
     * 虚拟货币类构造权益包
     */
    public static PlusProgramProfitsPackageObject createFromVirtual(
            List<PlusProgramProfitsVirtualObject> virtual, PlusModelEnum plusModel,
            Integer programId) {
        PlusProgramProfitsPackageObject o = new PlusProgramProfitsPackageObject();
        o.setProgramId(programId);
        o.setPackageId(plusModel.getModelId());
        o.setModelId(plusModel.getModelId());
        o.setPlusModel(plusModel);
        o.setGivePeriod(plusModel.getGivePeriodsTypeEnum());
        o.setGiveType(plusModel.getGiveTypeEnum());
        o.setVirtual(virtual);
        return o;
    }

    /**
     * 虚拟商品构造权益包
     */
    public static PlusProgramProfitsPackageObject createFromVirtualGoods(
            List<PlusProgramProfitsVirtualGoodsObject> virtualGoods, PlusModelEnum plusModel,
            Integer programId) {
        PlusProgramProfitsPackageObject o = new PlusProgramProfitsPackageObject();
        o.setProgramId(programId);
        o.setPackageId(plusModel.getModelId());
        o.setModelId(plusModel.getModelId());
        o.setPlusModel(plusModel);
        o.setGivePeriod(plusModel.getGivePeriodsTypeEnum());
        o.setGiveType(plusModel.getGiveTypeEnum());
        o.setVirtualGoods(virtualGoods);
        return o;
    }

}
