package com.juzifenqi.plus.module.program.model.contract.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;


/**
 * 返现权益
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/13 10:51
 */
@Data
public class PlusProgramCashbackEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 权益类型id
     */
    private Integer modelId;

    /**
     * 下单笔数
     */
    private Integer orderNum;

    /**
     * 返现类型：1_固定金额 2_会员费
     */
    private Integer cashbackType;

    /**
     * 返现金额
     */
    private BigDecimal cashbackAmount;

    /**
     * 去下单跳转连接
     */
    private String buyUrl;

    /**
     * 创建者id
     */
    private Integer createUserId;

    /**
     * 创建者名字
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
