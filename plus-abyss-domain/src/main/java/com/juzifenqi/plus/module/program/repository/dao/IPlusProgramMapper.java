package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramPo;
import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface IPlusProgramMapper {

    /**
     * Load查询
     */
    PlusProgramPo loadMemberPlusProgram(@Param("id") Integer id);

    List<PlusProgramPo> loadPlusProgramByIdAndStatus(@Param("idList") List<Integer> idList);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("queryMap") Map<String, String> queryMap);

    /**
     * 分页查询Count
     */
    Integer pageListCountNew(@Param("queryParam") ProgramQueryReq req);

    /**
     * 分页查询Data
     */
    List<PlusProgramPo> pageList(@Param("queryMap") Map<String, String> queryMap,
            @Param("start") Integer start, @Param("size") Integer size);

    /**
     * 查所有上架的方案信息
     */
    List<PlusProgramPo> getMemberPlusProgramListed();

    /**
     * 查所有上架生效的方案信息
     */
    List<PlusProgramPo> getMemberPlusProgramAll(@Param("configId") Integer configId);

    /**
     * 获取所有生效状态的方案（不管上架与否）
     */
    List<PlusProgramPo> getValidMemberPlusProgram();

    /**
     * 新增返回ID
     */
    Integer saveMemberPlusProgram(PlusProgramPo plusProgram);

    /**
     * 删除
     */
    Integer deleteMemberPlusProgram(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusProgram(PlusProgramPo plusProgram);

    /**
     * 上架
     */
    Boolean upAct(@Param("id") String id);

    /**
     * 下架
     */
    Boolean downAct(@Param("id") String id);

    Boolean updateProgrammeEffective(@Param("id") Integer id);

    /**
     * 后台方案名称是否存在
     */
    Integer getBackstageNameCount(@Param("backstageName") String backstageName,
            @Param("id") Integer id, @Param("channel") Integer channel);

    /**
     * 查询当前方案是否支持展示购买记录
     */
    Integer getProgramIsRecord(@Param("id") Integer id);

    Integer checkSignProgram(String sign);

    Integer updateMallMobilePrice(@Param("programId") Integer programId,
            @Param("price") BigDecimal price);

    /**
     * 查所有上架的方案
     */
    List<PlusProgramPo> getMemberPlusProgramList(@Param("configId") Integer configId,
            @Param("programId") Integer programId, @Param("channelId") Integer channelId);

    List<PlusProgramListEntity> pageListNew(@Param("queryParam") ProgramQueryReq req,
            @Param("start") Integer start, @Param("size") Integer size);


    ProgramDetailEntity getPlusProgramDetailById(@Param("id") int id);
}
