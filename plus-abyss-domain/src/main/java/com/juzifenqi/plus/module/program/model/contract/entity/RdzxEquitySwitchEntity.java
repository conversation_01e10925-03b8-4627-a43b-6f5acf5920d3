package com.juzifenqi.plus.module.program.model.contract.entity;

import com.juzifenqi.plus.enums.RdSendTypeEnum;
import lombok.Data;

/**
 * 融担咨询卡权益开关
 *
 * <AUTHOR>
 * @date 2024/6/13 下午6:19
 */
@Data
public class RdzxEquitySwitchEntity {

    /**
     * 开启权益发放类型
     *
     * @see RdSendTypeEnum
     */
    private Integer rdSendType = RdSendTypeEnum.NONE.getCode();

    /**
     * 挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer popUp = 0;

    /**
     * 二次挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer twoPopUp = 0;

    /**
     * 关闭挽留弹窗是否选中融担卡 0-否 1-是
     */
    private Integer rdChoose = 0;

}
