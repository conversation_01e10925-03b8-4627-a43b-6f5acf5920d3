package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.dto.req.profits.QueryHalfPriceProductReq;
import com.juzifenqi.plus.dto.req.profits.QueryLmkVirtualReq;
import com.juzifenqi.plus.dto.req.profits.QueryLyffVirtualGoodsReq;
import com.juzifenqi.plus.dto.req.profits.QueryPlusProductReq;
import com.juzifenqi.plus.dto.req.profits.QueryPlusProductTypeReq;
import com.juzifenqi.plus.dto.req.profits.QueryProgramCouponReq;
import com.juzifenqi.plus.dto.req.profits.QueryQjhkCouponConfigReq;
import com.juzifenqi.plus.dto.req.profits.QueryRepayCashBackReq;
import com.juzifenqi.plus.dto.req.profits.QueryVirtualTypeReq;
import com.juzifenqi.plus.dto.req.profits.QueryYygProductReq;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramQjhkEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusGwfxProgramEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusHalfPriceProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductNewEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductTypeEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramSrlEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramTxEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramZskfEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusQjhkCouponConfigEntity;
import com.juzifenqi.plus.module.program.model.event.CreateHalfPriceProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreateLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusGwfxProgramEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductNewEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductTypeEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramLmkVirtualEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramSrlEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramTxEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramZskfEvent;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkEvent;
import com.juzifenqi.plus.module.program.model.event.CreateRepayCashBackEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualTypeEvent;
import com.juzifenqi.plus.module.program.model.event.DeleteLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.DeleteRepayCashBackEvent;
import com.juzifenqi.plus.module.program.model.event.EditLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigStateEvent;
import com.juzifenqi.plus.module.program.model.event.EditRepayCashBackEvent;
import com.juzifenqi.plus.module.program.model.event.UpdateProfitVirtualEvent;
import java.util.List;

/**
 * 会员方案权益model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 15:20
 */
public interface IPlusProfitModel {

    /**
     * 方案的权益列表
     */
    List<PlusProModelEntity> getPlusProModelList(Integer programId);

    /**
     * 获取权益配置基础信息
     */
    PlusProModelEntity getPlusProModel(Integer programId, Integer modelId);

    /**
     * 编辑权益配置基础信息
     */
    void editPlusProModel(CreatePlusProModelEvent event);

    /**
     * 权益列表排序修改
     */
    void editPlusProModelSort(List<CreatePlusProModelEvent> list);

    /**
     * 保存优惠券信息
     * <p>开卡礼/月享红包/息费折扣/品牌专区/还款优惠/生日关怀/专属好礼/多买多送/拒就陪</p>
     */
    void saveProgramCouponIndex(List<CreateProgramCouponEvent> list);

    /**
     * 获取优惠券信息
     * <p>开卡礼/月享红包/息费折扣/品牌专区/还款优惠/生日关怀/专属好礼/多买多送/拒就陪</p>
     */
    PageResultEntity<PlusProgramCouponEntity> getPlusProgramCouponList(QueryProgramCouponReq event);

    /**
     * 删除优惠券信息
     * <p>开卡礼/月享红包/息费折扣/品牌专区/还款优惠/生日关怀/专属好礼/多买多送/拒就陪</p>
     */
    void deletePlusProgramCoupon(CreateProgramCouponEvent event);

    /**
     * 编辑优惠券信息
     * <p>多买多送/拒就陪</p>
     */
    void editPlusProgramCoupon(CreateProgramCouponEvent event);

    /**
     * 半价商品查询
     */
    PageResultEntity<PlusHalfPriceProductEntity> getHalfPriceProductList(
            QueryHalfPriceProductReq req);

    /**
     * 半价商品保存
     */
    void batchSaveHalfPriceProduct(List<CreateHalfPriceProductEvent> events);

    /**
     * 删除半价商品
     */
    void halfPriceDelete(CreateHalfPriceProductEvent event);

    /**
     * 修改半价商品
     */
    void halfPriceUpdate(CreateHalfPriceProductEvent event);

    /**
     * 一元购商品查询
     */
    PageResultEntity<PlusProgramProductNewEntity> getYygProducts(QueryYygProductReq req);

    /**
     * 一元购商品保存
     */
    List<String> batchSaveYygProduct(List<CreatePlusProductNewEvent> list);

    /**
     * 删除一元购
     */
    void batchDelYygProduct(List<CreatePlusProductNewEvent> list);

    /**
     * 0元商品编辑
     * <p>只能编辑排序、折扣字段、营销图片</p>
     */
    void editPlusProduct(CreatePlusProductNewEvent req);

    /**
     * 专属客服权益查询
     */
    PlusProgramZskfEntity getPlusProgramZskf(Integer programId);

    /**
     * 专属客服权益保存
     */
    void saveZskf(CreatePlusProgramZskfEvent event);

    /**
     * 生日礼权益保存
     */
    void saveSrl(CreatePlusProgramSrlEvent event);

    /**
     * 获取生日礼权益
     */
    PlusProgramSrlEntity getPlusProgramSrl(Integer programId);

    /**
     * 通信联名卡权益保存
     */
    void saveTxLmk(List<CreatePlusProgramTxEvent> events);

    /**
     * 通信联名卡权益列表
     */
    PageResultEntity<PlusProgramTxEntity> selectTxLmkList(Integer programId);

    /**
     * 删除通信联名卡权益-单条
     */
    void deleteTxLmk(CreatePlusProgramTxEvent event);

    /**
     * 编辑通信联名卡-单条
     */
    void updateTxLmk(CreatePlusProgramTxEvent event);

    PageResultEntity<PlusProgramVirtualTypeEntity> getVirtualTypeByProgram(QueryVirtualTypeReq req);

    /**
     * 新增生活权益分类
     * <p>生活权益/</>
     */
    void saveVirtualType(CreateVirtualTypeEvent event);

    /**
     * 编辑生活权益分类
     * <p>生活权益/</>
     *
     * @return 方案id
     */
    Integer modifyVirtualTypeById(CreateVirtualTypeEvent event);

    /**
     * 删除生活权益分类
     * <p>生活权益/</>
     */
    PlusProgramVirtualTypeEntity removeVirtualTypeById(CreateVirtualTypeEvent event);

    /**
     * 查询分类下的权益集合
     * <p>生活权益/</>
     */
    PageResultEntity<PlusProgramVirtualEntity> getVirtualByType(Integer typeId);

    /**
     * 根据id删除权益
     * <p>生活权益/</>
     */
    void removeVirtualById(CreateVirtualGoodsEvent event);

    /**
     * 根据id编辑权益
     * <p>生活权益/</>
     */
    void modifyVirtualById(CreateVirtualGoodsEvent event);

    /**
     * 根据sku集合批量添加权益
     * <p>生活权益/</>
     */
    List<String> saveVirtualBatch(List<CreateVirtualGoodsEvent> list);

    /**
     * 通过id单个查询
     */
    PlusProgramVirtualTypeEntity getVirtualTypeById(Integer profitTypeId);

    /**
     * 根据一级分类查询所有二级分类和商品明细-不分页
     */
    List<PlusProgramVirtualTypeEntity> getListByParentId(QueryVirtualTypeReq req);

    /**
     * 刷新缓存数据-上架后再修改，同步刷新
     */
    void refreshProfitCache(Integer programId, Integer modelId);

    /**
     * 会员0元商品新增分类配置
     */
    void saveProgramProductType(CreatePlusProductTypeEvent event);

    /**
     * 会员0元商品新增分类编辑
     */
    void editProgramProductType(CreatePlusProductTypeEvent event);

    /**
     * 获取方案下配置的0元商品分类数据
     */
    PageResultEntity<PlusProgramProductTypeEntity> getProgramProductType(
            QueryPlusProductTypeReq event);

    /**
     * 会员0元商品分类删除
     */
    void delProgramProductType(CreatePlusProductTypeEvent event);

    /**
     * 会员0元商品池列表(分页)
     */
    PageResultEntity<PlusProgramProductEntity> selectPlusProductList(
            QueryPlusProductReq plusProductVo);

    /**
     * 新增会员0元商品池的商品
     */
    void savePlusProduct(CreatePlusProductEvent event);

    /**
     * 编辑会员0元商品池的商品
     */
    void updatePlusProduct(CreatePlusProductEvent event);

    /**
     * 删除会员0元商品池的商品
     */
    void delPlusProduct(CreatePlusProductEvent event);

    /**
     * 商品池会员0元商品上架/下架
     */
    void updatePlusProductState(CreatePlusProductEvent event);

    /**
     * 根据方案ID和模块id获取权益基本信息+提额数据
     */
    PlusProModelEntity getPlusProModelDetail(CreatePlusProModelEvent event);


    /**
     * 添加提额数据信息+编辑基本信息
     */
    void saveLiftAmount(CreatePlusProModelEvent event);

    /**
     * 根据条件查询分页集合
     */
    PageResultEntity<PlusProgramLmkVirtualEntity> getLmkListPage(QueryLmkVirtualReq req);

    /**
     * 删除某个权益
     */
    void deleteLmk(CreatePlusProgramLmkVirtualEvent event);

    /**
     * 编辑某个权益
     */
    void editLmk(CreatePlusProgramLmkVirtualEvent event);

    /**
     * 批量添加
     */
    List<String> saveBatchLmk(List<CreatePlusProgramLmkVirtualEvent> events);

    /**
     * 修改联名卡虚拟权益状态和缓存---虚拟三方服务虚拟权益上下架时调用
     */
    void updateLmkVirtualState(CreatePlusProgramLmkVirtualEvent event);

    /**
     * 修改虚拟权益状态和缓存---虚拟三方服务虚拟权益上下架时调用
     */
    void updateVirtualState(CreatePlusProgramLmkVirtualEvent event);

    /**
     * 批量添加
     */
    void saveBatchCashback(CreatePlusGwfxProgramEvent event);

    /**
     * 批量修改
     */
    void updateBatchCashback(CreatePlusGwfxProgramEvent event);

    /**
     * 查询方案下购物返现/结清返现权益
     */
    PageResultEntity<PlusGwfxProgramEntity> queryCashback(CreatePlusGwfxProgramEvent event);

    /**
     * 获取区间还款配置详情
     */
    PlusProgramQjhkEntity getQjhkByProgramId(Integer programId);

    /**
     * 新增或编辑区间还款优惠折扣配置
     */
    void editQjhk(CreateQjhkEvent event);

    /**
     * 权益0元发放虚拟商品配置-新增
     */
    void saveLyffVirtualGoodsBatch(CreateLyffVirtualGoodsEvent event);

    /**
     * 权益0元发放虚拟商品-列表
     */
    PageResultEntity<PlusProgramVirtualEntity> getLyffVirtualGoodsListPage(
            QueryLyffVirtualGoodsReq req);

    /**
     * 权益0元发放虚拟商品配置-编辑
     */
    void editLyffVirtualGoods(EditLyffVirtualGoodsEvent event);

    /**
     * 权益0元发放虚拟商品配置-删除
     */
    void deleteLyffVirtualGoods(DeleteLyffVirtualGoodsEvent event);

    /**
     * 获取权益基础信息名称为空的数量
     */
    Integer getCountNullByProgramId(Integer programId);

    /**
     * 方案ID缓存 50分钟、设置方案基本权益缓存；
     */
    void saveCache(Integer programId);

    /**
     * 删除方案ID和基础权益缓存
     */
    void deleteCache(Integer programId);

    /**
     * 加载所有半价商品缓存
     */
    void reloadAllHalfPriceProductToCache();

    /**
     * 加载排序前6个半价商品缓存
     */
    void reloadHalfPriceProductLimit6ToCache();
    /**
     * 还款返现配置-新增
     */
    void saveRepayCashBack(CreateRepayCashBackEvent event);

    /**
     * 还款返现配置-列表
     */
    PageResultEntity<PlusProgramCashbackEntity> getRepayCashBackListPage(QueryRepayCashBackReq req);

    /**
     * 还款返现配置-编辑
     */
    void editRepayCashBack(EditRepayCashBackEvent event);

    /**
     * 还款返现配置-删除
     */
    void deleteRepayCashBack(DeleteRepayCashBackEvent event);

    /**
     * 区间还款配置优惠券-新增
     */
    void saveQjhkCouponConfig(CreateQjhkCouponConfigEvent event);

    /**
     * 区间还款配置优惠券-列表
     */
    PageResultEntity<PlusQjhkCouponConfigEntity> getQjhkCouponConfigListPage(
            QueryQjhkCouponConfigReq req);

    /**
     * 区间还款配置优惠券-编辑
     */
    void editQjhkCouponConfig(EditQjhkCouponConfigEvent event);

    /**
     * 区间还款配置优惠券-修改状态
     */
    void editQjhkCouponConfigState(EditQjhkCouponConfigStateEvent event);

    /**
     * 区间还款配置优惠券-刷新缓存
     */
    void refreshQjhkCouponConfig();

    /**
     * 更新会员方案-权益数据营销图片 加速卡、固定提额卡、桔享卡、小额月卡
     */
    boolean updateProfitVirtualImgUrl(UpdateProfitVirtualEvent event);
}
