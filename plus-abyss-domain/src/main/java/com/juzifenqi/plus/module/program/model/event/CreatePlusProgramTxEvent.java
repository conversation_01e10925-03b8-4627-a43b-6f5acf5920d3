package com.juzifenqi.plus.module.program.model.event;

import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/28 17:45
 * @description
 */
@Data
public class CreatePlusProgramTxEvent {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 广告图url
     */
    private String imgUrl;

    /**
     * 跳转url
     */
    private String returnUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private int updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserName;
}
