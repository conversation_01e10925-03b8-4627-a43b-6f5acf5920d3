package com.juzifenqi.plus.module.program.model.contract.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @Date 2021/11/10 18:23
 * @Version 1.0 桔享Plus-提额设置表
 */
@Data
public class PlusLiftAmountEntity implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 提额等级
     */
    private String grade;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人名
     */
    private Integer updateUserId;

    /**
     * 编辑人姓名
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 提额天数
     */
    private Integer periods;
}
