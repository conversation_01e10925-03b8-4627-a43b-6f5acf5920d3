package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProModelConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProModelMapper;
import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import java.util.List;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 方案下配置的权益信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/22 10:22
 */
@Service
@Slf4j
public class PlusProModelRepositoryImpl implements IPlusProModelRepository {

    private final PlusProModelConverter converter = PlusProModelConverter.instance;

    @Autowired
    private IPlusProModelMapper plusProModelMapper;

    @Override
    public List<PlusProModelEntity> getQuotaModelByMember(Integer userId, Integer channelId,
            Integer configId) {
        List<PlusProModelPo> list = plusProModelMapper.getQuotaModelByMember(userId, channelId,
                configId);
        return converter.toPlusProModelEntityList(list);
    }

    @Override
    public List<PlusProModelEntity> getPlusProModelList(Integer programId) {
        List<PlusProModelPo> list = plusProModelMapper.getPlusProModelByProgramId(programId);
        return converter.toPlusProModelEntityList(list);
    }

    @Override
    public int countProModelByProgramId(Integer programId, Integer modelId) {
        return plusProModelMapper.countProModelByProgramId(programId, modelId);
    }

    @Override
    public PlusProModelEntity getPlusProModel(Integer programId, Integer modelId) {
        PlusProModelPo po = plusProModelMapper.getPlusProModelByVo(programId, modelId);
        return converter.toPlusProModelEntity(po);
    }

    @Override
    public void editPlusProModel(CreatePlusProModelEvent event) {
        plusProModelMapper.updateMemberPlusProModel(converter.toPlusProModelPo(event));
    }

    @Override
    public void editPlusProModelSort(List<CreatePlusProModelEvent> list) {
        plusProModelMapper.batchUpdateProModelSort(converter.toPlusProModelPoList(list));
    }

    /**
     * 获取排序后的列表
     */
    @Override
    public List<PlusProModelEntity> getProModelByProgramIdOrderBySort(Integer programId) {
        return converter.toPlusProModelEntityList(
                plusProModelMapper.getProModelByProgramIdOrderBySort(programId));
    }

    @Override
    public List<PlusProModelEntity> getByModelId(Integer modelId) {
        return converter.toPlusProModelEntityList(plusProModelMapper.getByModelId(modelId));
    }

    @Override
    public void saveProModel(Integer programId, List<Integer> modelIds) {
        if (CollectionUtils.isEmpty(modelIds)) {
            return;
        }
        List<PlusProModelPo> list = modelIds.stream().map(e -> {
            PlusProModelPo po = new PlusProModelPo();
            po.setModelId(e);
            po.setSort(e);
            po.setProgramId(programId);
            return po;
        }).collect(Collectors.toList());
        plusProModelMapper.batchInsertPlusProModel(list);
    }

    @Override
    public void delProModel(Integer programId, List<Integer> modelIds) {
        if (CollectionUtils.isEmpty(modelIds)) {
            return;
        }
        plusProModelMapper.batchDel(programId, modelIds);
    }

    @Override
    public Integer getCountNullByProgramId(Integer programId) {
        return plusProModelMapper.getCountNullByProgramId(programId);
    }
}
