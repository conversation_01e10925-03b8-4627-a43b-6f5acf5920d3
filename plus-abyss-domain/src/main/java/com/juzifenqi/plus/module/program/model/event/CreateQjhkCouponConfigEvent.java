package com.juzifenqi.plus.module.program.model.event;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 区间还款配置优惠券-新增
 *
 * <AUTHOR>
 * @date 2024/7/31 上午10:18
 */
@Data
public class CreateQjhkCouponConfigEvent {

    /**
     * 减免金额最小金额
     */
    private BigDecimal minAmount;

    /**
     * 减免金额最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 期数
     */
    private Integer periodNum;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作者姓名
     */
    private String optName;

}
