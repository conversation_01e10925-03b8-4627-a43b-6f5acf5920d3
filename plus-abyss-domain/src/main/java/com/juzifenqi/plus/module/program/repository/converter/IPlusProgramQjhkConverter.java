package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramQjhkEntity;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramQjhkPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/25 10:12
 */
@Mapper
public interface IPlusProgramQjhkConverter {

    IPlusProgramQjhkConverter instance = Mappers.getMapper(IPlusProgramQjhkConverter.class);

    PlusProgramQjhkPo toPo(CreateQjhkEvent event);

    PlusProgramQjhkEntity toEntity(PlusProgramQjhkPo po);
}
