package com.juzifenqi.plus.module.program.model.contract.entity;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * 融担咨询卡权益配置信息
 *
 * <AUTHOR>
 * @date 2024/6/18 上午11:08
 */
@Data
public class RdzxEquityInfoEntity {

    /**
     * 是否有匹配权益
     */
    private Boolean isMatch = false;

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 市场价(仅用于展示)
     */
    private String plusMarketPrice;

    /**
     * 还款返现金额信息
     */
    public List<HkfxAmountInfo> hkfxAmountInfos;

    @Data
    public static class HkfxAmountInfo {

        /**
         * 期数
         */
        private Integer periods;

        /**
         * 返现金额
         */
        private BigDecimal cashBackAmount;

    }

}
