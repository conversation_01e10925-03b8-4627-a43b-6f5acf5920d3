package com.juzifenqi.plus.module.program.repository.entity;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusReachConditionObject {

    private Long conditionId;

    /**
     * 达成指标
     */
    private String conditionField;
    /**
     * 达成指标值
     */
    private String reachCondition;

    /**
     * 达成实际值
     */
    private String reachValue;

    /**
     * 是否达成
     */
    private Boolean isReached;

    /**
     * 达成是否触发发放任务
     */
    private Boolean isTaskTrigger;

    public static PlusReachConditionObject create(String reachConditionField, String reachCondition,
            String reachValue, Boolean isTaskTrigger) {
        PlusReachConditionObject object = new PlusReachConditionObject();
        object.setConditionField(reachConditionField);
        object.setReachCondition(reachCondition);
        object.setReachValue(reachValue);
        object.setIsTaskTrigger(isTaskTrigger);
        object.setIsReached(false);
        return object;
    }
}
