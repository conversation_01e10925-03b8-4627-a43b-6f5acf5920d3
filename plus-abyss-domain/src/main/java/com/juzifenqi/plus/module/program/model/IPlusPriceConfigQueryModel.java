package com.juzifenqi.plus.module.program.model;


import com.juzifenqi.plus.module.program.model.event.price.PriceConfigContext;
import com.juzifenqi.plus.module.program.model.event.price.PriceConfigQueryEvent;

/**
 * 差异化/默认方案查询model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/10 15:27
 */
public interface IPlusPriceConfigQueryModel {

    /**
     * 获取默认方案
     */
    Integer getDefaultProgramId(Integer channelId, Integer configId);

    Integer getDefaultProgramId(Integer channelId, Integer configId,Integer bizSource);

    /**
     * 获取差异化方案
     */
    Integer getDiffProgramId(PriceConfigQueryEvent event, PriceConfigContext context);

    /**
     * 校验提额等级是否一致
     */
    boolean checkGradeMatch(Integer programId, String grade);
}
