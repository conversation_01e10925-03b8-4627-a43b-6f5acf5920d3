package com.juzifenqi.plus.module.program.application.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.dto.req.profits.QueryHalfPriceProductReq;
import com.juzifenqi.plus.dto.req.profits.QueryLmkVirtualReq;
import com.juzifenqi.plus.dto.req.profits.QueryLyffVirtualGoodsReq;
import com.juzifenqi.plus.dto.req.profits.QueryPlusProductReq;
import com.juzifenqi.plus.dto.req.profits.QueryPlusProductTypeReq;
import com.juzifenqi.plus.dto.req.profits.QueryProgramCouponReq;
import com.juzifenqi.plus.dto.req.profits.QueryQjhkCouponConfigReq;
import com.juzifenqi.plus.dto.req.profits.QueryRepayCashBackReq;
import com.juzifenqi.plus.dto.req.profits.QueryVirtualGoodsReq;
import com.juzifenqi.plus.dto.req.profits.QueryVirtualTypeReq;
import com.juzifenqi.plus.dto.req.profits.QueryYygProductReq;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusProfitsGiveTypeEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.application.IPlusProfitApplication;
import com.juzifenqi.plus.module.program.model.IPlusProfitModel;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramQjhkEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusGwfxProgramEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusHalfPriceProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductNewEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductTypeEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramSrlEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramTxEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramZskfEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusQjhkCouponConfigEntity;
import com.juzifenqi.plus.module.program.model.event.CreateHalfPriceProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreateLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusGwfxProgramEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductNewEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductTypeEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramLmkVirtualEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramSrlEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramTxEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramZskfEvent;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkEvent;
import com.juzifenqi.plus.module.program.model.event.CreateRepayCashBackEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualTypeEvent;
import com.juzifenqi.plus.module.program.model.event.DeleteLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.DeleteRepayCashBackEvent;
import com.juzifenqi.plus.module.program.model.event.EditLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigStateEvent;
import com.juzifenqi.plus.module.program.model.event.EditRepayCashBackEvent;
import com.juzifenqi.plus.module.program.model.event.UpdateProfitVirtualEvent;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 会员权益
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 15:28
 */
@Service
@Slf4j
public class PlusProfitApplicationImpl implements IPlusProfitApplication {

    @Autowired
    private IPlusProfitModel plusProfitModel;

    @Override
    public List<PlusProModelEntity> getPlusProModelList(Integer programId) {
        return plusProfitModel.getPlusProModelList(programId);
    }

    @Override
    public PlusProModelEntity getPlusProModel(Integer programId, Integer modelId) {
        return plusProfitModel.getPlusProModel(programId, modelId);
    }

    @Override
    public void savePlusProModel(CreatePlusProModelEvent event) {
        if (Objects.nonNull(event.getSendType()) && Objects.isNull(
                PlusProfitsGiveTypeEnum.getByValue(event.getSendType()))) {
            throw new PlusAbyssException("发放方式无效");
        }
        plusProfitModel.editPlusProModel(event);
    }

    @Override
    public void editPlusProModelSort(List<CreatePlusProModelEvent> list) {
        plusProfitModel.editPlusProModelSort(list);
    }

    @Override
    public void saveProgramCouponIndex(List<CreateProgramCouponEvent> list) {
        plusProfitModel.saveProgramCouponIndex(list);
    }

    @Override
    public PageResultEntity<PlusProgramCouponEntity> getPlusProgramCouponList(
            QueryProgramCouponReq req) {
        return plusProfitModel.getPlusProgramCouponList(req);
    }

    @Override
    public void deletePlusProgramCoupon(CreateProgramCouponEvent event) {
        plusProfitModel.deletePlusProgramCoupon(event);
    }

    @Override
    public void editPlusProgramCoupon(CreateProgramCouponEvent event) {
        plusProfitModel.editPlusProgramCoupon(event);
    }

    @Override
    public PageResultEntity<PlusProgramVirtualTypeEntity> getVirtualTypeByProgram(
            QueryVirtualTypeReq req) {
        return plusProfitModel.getVirtualTypeByProgram(req);
    }

    @Override
    public PageResultEntity<PlusHalfPriceProductEntity> getHalfPriceProductList(
            QueryHalfPriceProductReq req) {
        return plusProfitModel.getHalfPriceProductList(req);
    }

    @Override
    public void batchSaveHalfPriceProduct(List<CreateHalfPriceProductEvent> events) {
        plusProfitModel.batchSaveHalfPriceProduct(events);
    }

    @Override
    public void halfPriceDelete(CreateHalfPriceProductEvent event) {
        plusProfitModel.halfPriceDelete(event);
    }

    @Override
    public void halfPriceUpdate(CreateHalfPriceProductEvent event) {
        if (event == null || event.getId() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.halfPriceUpdate(event);
    }

    @Override
    public PageResultEntity<PlusProgramProductNewEntity> getYygProducts(QueryYygProductReq req) {
        if (req.getProgramId() == null || req.getModelId() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getYygProducts(req);
    }

    @Override
    public void batchSaveYygProduct(List<CreatePlusProductNewEvent> list) {
        Integer optId = list.get(0).getOptId();
        String optUser = list.get(0).getOptUser();
        if (CollectionUtils.isEmpty(list) || optId == null || optUser == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.batchSaveYygProduct(list);
    }

    @Override
    public void batchDelYygProduct(List<CreatePlusProductNewEvent> list) {
        Integer optId = list.get(0).getOptId();
        String optUser = list.get(0).getOptUser();
        if (CollectionUtils.isEmpty(list) || optId == null || optUser == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.batchDelYygProduct(list);
    }

    @Override
    public void editPlusProduct(CreatePlusProductNewEvent req) {
        if (req == null || req.getSort() == null || req.getDiscountRate() == null
                || req.getOptId() == null || StringUtils.isBlank(req.getOptUser())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.editPlusProduct(req);
    }

    @Override
    public PlusProgramZskfEntity getPlusProgramZskf(Integer programId) {
        if (programId == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getPlusProgramZskf(programId);
    }

    @Override
    public void saveZskf(CreatePlusProgramZskfEvent event) {
        if (event == null || event.getProgramId() == null || event.getConfigId() == null
                || StringUtils.isBlank(event.getReturnUrl())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.saveZskf(event);
    }

    @Override
    public void saveSrl(CreatePlusProgramSrlEvent event) {
        if (event == null || event.getProgramId() == null || event.getConfigId() == null
                || StringUtils.isBlank(event.getImgUrl())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.saveSrl(event);
    }

    @Override
    public PlusProgramSrlEntity getPlusProgramSrl(Integer programId) {
        if (programId == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getPlusProgramSrl(programId);
    }

    @Override
    public void saveTxLmk(List<CreatePlusProgramTxEvent> events) {
        if (CollectionUtils.isEmpty(events)) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.saveTxLmk(events);
    }

    @Override
    public PageResultEntity<PlusProgramTxEntity> selectTxLmkList(Integer programId) {
        if (programId == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.selectTxLmkList(programId);
    }

    @Override
    public void deleteTxLmk(CreatePlusProgramTxEvent event) {
        if (event == null || event.getId() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.deleteTxLmk(event);
    }

    @Override
    public void updateTxLmk(CreatePlusProgramTxEvent event) {
        if (event == null || event.getId() == null || StringUtils.isBlank(event.getImgUrl())
                || StringUtils.isBlank(event.getReturnUrl())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.updateTxLmk(event);
    }

    @Override
    public void saveVirtualType(CreateVirtualTypeEvent event) {
        // 基础字段判断
        if (Objects.isNull(event.getTypeName()) || Objects.isNull(event.getRankNum())
                || Objects.isNull(event.getProgramId()) || Objects.isNull(event.getOptId())
                || Objects.isNull(event.getOptName()) || Objects.isNull(event.getModelId())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        // 渠道生活权益
        if (PlusModelEnum.isLevelVirtualProfitModel(event.getModelId())) {
            if (Objects.isNull(event.getTypeLevel())) {
                throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
            }
            // 非一级分类
            if (!event.getTypeLevel().equals(1)) {
                if (Objects.isNull(event.getParentId())) {
                    throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
                }
            } else {
                event.setParentId(null);
                // 渠道生活权益 一级类目不增加限制
                event.setNumLimit(0);
            }
        } else {
            // 生活权益
            event.setParentId(null);
            event.setTypeLevel(1);
        }
        plusProfitModel.saveVirtualType(event);
    }

    @Override
    public void modifyVirtualTypeById(CreateVirtualTypeEvent event) {
        if (Objects.isNull(event.getTypeName()) || Objects.isNull(event.getRankNum())
                || Objects.isNull(event.getId()) || Objects.isNull(event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        Integer programId = plusProfitModel.modifyVirtualTypeById(event);
        //刷新缓存
        plusProfitModel.refreshProfitCache(programId, event.getModelId());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeVirtualTypeById(CreateVirtualTypeEvent event) {
        if (Objects.isNull(event.getId()) || Objects.isNull(event.getOptId()) || Objects.isNull(
                event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        PlusProgramVirtualTypeEntity entity = plusProfitModel.removeVirtualTypeById(event);
        //刷新缓存
        plusProfitModel.refreshProfitCache(entity.getProgramId(), entity.getModelId());
    }

    @Override
    public PageResultEntity<PlusProgramVirtualEntity> getVirtualByType(QueryVirtualGoodsReq event) {
        if (Objects.isNull(event) || Objects.isNull(event.getProfitTypeId())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getVirtualByType(event.getProfitTypeId());
    }

    @Override
    public void removeVirtualById(CreateVirtualGoodsEvent event) {
        if (Objects.isNull(event.getId()) || Objects.isNull(event.getOptId()) || Objects.isNull(
                event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.removeVirtualById(event);
    }

    @Override
    public void modifyVirtualById(CreateVirtualGoodsEvent event) {
        if (Objects.isNull(event) || Objects.isNull(event.getId()) || Objects.isNull(
                event.getOptId()) || Objects.isNull(event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.modifyVirtualById(event);
    }

    @Override
    public List<String> saveVirtualBatch(List<CreateVirtualGoodsEvent> list) {
        if (CollectionUtils.isEmpty(list)) {
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERR_CODE_200021);
        }
        return plusProfitModel.saveVirtualBatch(list);
    }

    @Override
    public PlusProgramVirtualTypeEntity getVirtualTypeById(Integer profitTypeId) {
        if (Objects.isNull(profitTypeId)) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getVirtualTypeById(profitTypeId);
    }

    @Override
    public List<PlusProgramVirtualTypeEntity> getListByParentId(QueryVirtualTypeReq req) {
        if (Objects.isNull(req) || Objects.isNull(req.getProfitTypeId())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        List<PlusProgramVirtualTypeEntity> list = plusProfitModel.getListByParentId(req);
        if (CollectionUtils.isNotEmpty(list)) {
            list.forEach(v -> {
                // 查询分类下商品信息
                req.setProfitTypeId(v.getId());
                req.setPageNo(null);
                req.setPageSize(null);
                v.setProductsInfo(
                        plusProfitModel.getVirtualByType(req.getProfitTypeId()).getList());
            });
        }
        return list;
    }

    @Override
    public void saveProgramProductType(CreatePlusProductTypeEvent event) {
        log.info("会员0元商品新增分类配置,入参:{}", JSON.toJSONString(event));
        if (event.getTypeName() == null || event.getRankNum() == null || event.getConfigId() == null
                || event.getProgramId() == null || event.getModelId() == null
                || event.getNumLimit() == null || event.getUpdateUserId() == null
                || StringUtils.isBlank(event.getUpdateUserNm())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        if (event.getTypeLimit() != null && event.getTypeLimit() == 1
                && event.getTypeLimitNum() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.saveProgramProductType(event);
    }

    @Override
    public void editProgramProductType(CreatePlusProductTypeEvent event) {
        if (event.getId() == null || event.getTypeName() == null || event.getRankNum() == null
                || event.getConfigId() == null || event.getProgramId() == null
                || event.getModelId() == null || event.getNumLimit() == null
                || event.getUpdateUserId() == null || StringUtils.isBlank(
                event.getUpdateUserNm())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        if (event.getTypeLimit() != null && event.getTypeLimit() == 1
                && event.getTypeLimitNum() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.editProgramProductType(event);
    }

    @Override
    public PageResultEntity<PlusProgramProductTypeEntity> getProgramProductType(
            QueryPlusProductTypeReq event) {
        if (event.getProgramId() == null || event.getModelId() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getProgramProductType(event);
    }

    @Override
    public void delProgramProductType(CreatePlusProductTypeEvent event) {
        if (event.getId() == null || event.getUpdateUserId() == null || StringUtils.isBlank(
                event.getUpdateUserNm())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.delProgramProductType(event);
    }

    @Override
    public PageResultEntity<PlusProgramProductEntity> selectPlusProductList(
            QueryPlusProductReq plusProductVo) {
        return plusProfitModel.selectPlusProductList(plusProductVo);
    }

    @Override
    public void savePlusProduct(CreatePlusProductEvent event) {
        if (event.getProductSku() == null || event.getPurPrice() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.savePlusProduct(event);
    }

    @Override
    public void updatePlusProduct(CreatePlusProductEvent event) {
        if (event.getId() == null || event.getPurPrice() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.updatePlusProduct(event);
    }

    @Override
    public void delPlusProduct(CreatePlusProductEvent event) {
        if (event.getId() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.delPlusProduct(event);
    }

    @Override
    public void updatePlusProductState(CreatePlusProductEvent event) {
        if (event == null || event.getId() == null || event.getOnSaleState() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.updatePlusProductState(event);
    }

    @Override
    public PlusProModelEntity getPlusProModelDetail(CreatePlusProModelEvent event) {
        return plusProfitModel.getPlusProModelDetail(event);
    }

    @Override
    public void saveLiftAmount(CreatePlusProModelEvent event) {
        plusProfitModel.saveLiftAmount(event);
    }

    @Override
    public PageResultEntity<PlusProgramLmkVirtualEntity> getLmkListPage(QueryLmkVirtualReq req) {
        if (req.getProgramId() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getLmkListPage(req);
    }

    @Override
    public void deleteLmk(CreatePlusProgramLmkVirtualEvent event) {
        if (event.getId() == null || event.getUpdateUserId() == null || StringUtils.isBlank(
                event.getUpdateUserName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.deleteLmk(event);
    }

    @Override
    public void editLmk(CreatePlusProgramLmkVirtualEvent event) {
        if (event == null || event.getId() == null || StringUtils.isBlank(event.getImgUrl())
                || event.getUpdateUserId() == null || StringUtils.isBlank(
                event.getUpdateUserName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.editLmk(event);
    }

    @Override
    public List<String> saveBatchLmk(List<CreatePlusProgramLmkVirtualEvent> events) {
        if (org.springframework.util.CollectionUtils.isEmpty(events)) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.saveBatchLmk(events);
    }

    @Override
    public void updateVirtualState(CreatePlusProgramLmkVirtualEvent event) {
        if (event == null || event.getVirtualStatus() == null || StringUtils.isBlank(
                event.getSku())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.updateVirtualState(event);
        plusProfitModel.updateLmkVirtualState(event);
    }

    @Override
    public void saveBatchCashback(CreatePlusGwfxProgramEvent event) {
        plusProfitModel.saveBatchCashback(event);
    }

    @Override
    public void updateBatchCashback(CreatePlusGwfxProgramEvent event) {
        plusProfitModel.updateBatchCashback(event);
    }

    @Override
    public PageResultEntity<PlusGwfxProgramEntity> queryCashback(CreatePlusGwfxProgramEvent event) {
        return plusProfitModel.queryCashback(event);
    }

    @Override
    public PlusProgramQjhkEntity getQjhkByProgramId(Integer programId) {
        return plusProfitModel.getQjhkByProgramId(programId);
    }

    @Override
    public void editQjhk(CreateQjhkEvent event) {
        if (event.getDiscountRate() == null) {
            throw new PlusAbyssException("折扣不能为空");
        }
        if (event.getDiscountRate().compareTo(BigDecimal.ZERO) <= 0
                || event.getDiscountRate().compareTo(BigDecimal.ONE) >= 0) {
            throw new PlusAbyssException("折扣范围必须在：0 ~ 1之间");
        }
        if (event.getId() == null && (event.getProgramId() == null
                || event.getConfigId() == null)) {
            throw new PlusAbyssException("方案id、会员类型id不能为空");
        }
        plusProfitModel.editQjhk(event);
    }

    /**
     * 权益0元发放虚拟商品-新增
     */
    @Override
    public void saveLyffVirtualGoodsBatch(CreateLyffVirtualGoodsEvent list) {
        if (CollectionUtils.isEmpty(list.getVirtualGoodsList())) {
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERR_CODE_200021);
        }
        plusProfitModel.saveLyffVirtualGoodsBatch(list);
    }

    /**
     * 权益0元发放虚拟商品-列表
     */
    @Override
    public PageResultEntity<PlusProgramVirtualEntity> getLyffVirtualGoodsListPage(
            QueryLyffVirtualGoodsReq req) {
        if (Objects.isNull(req.getProgramId())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getLyffVirtualGoodsListPage(req);
    }

    /**
     * 权益0元发放虚拟商品-编辑
     */
    @Override
    public void editLyffVirtualGoods(EditLyffVirtualGoodsEvent event) {
        if (event.getId() == null || event.getImgUrl() == null || event.getMinAmount() == null
                || event.getMaxAmount() == null || event.getOptId() == null || StringUtils.isBlank(
                event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.editLyffVirtualGoods(event);
    }

    /**
     * 权益0元发放虚拟商品-删除
     */
    @Override
    public void deleteLyffVirtualGoods(DeleteLyffVirtualGoodsEvent event) {
        if (event.getId() == null || event.getOptId() == null || StringUtils.isBlank(
                event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.deleteLyffVirtualGoods(event);
    }

    /**
     * 还款返现配置-新增
     */
    @Override
    public void saveRepayCashBack(CreateRepayCashBackEvent event) {
        if (event.getProgramId() == null || event.getConfigId() == null
                || event.getModelId() == null || event.getMinAmount() == null
                || event.getMaxAmount() == null || event.getPeriods() == null
                || event.getCashbackAmount() == null || event.getFirstScale() == null
                || event.getOptId() == null || StringUtils.isBlank(event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.saveRepayCashBack(event);
    }

    /**
     * 还款返现配置-列表
     */
    @Override
    public PageResultEntity<PlusProgramCashbackEntity> getRepayCashBackListPage(
            QueryRepayCashBackReq req) {
        if (Objects.isNull(req.getProgramId())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.getRepayCashBackListPage(req);
    }

    /**
     * 还款返现配置-编辑
     */
    @Override
    public void editRepayCashBack(EditRepayCashBackEvent event) {
        if (event.getId() == null || event.getMinAmount() == null || event.getMaxAmount() == null
                || event.getPeriods() == null || event.getCashbackAmount() == null
                || event.getFirstScale() == null || event.getOptId() == null || StringUtils.isBlank(
                event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.editRepayCashBack(event);
    }

    /**
     * 还款返现配置-删除
     */
    @Override
    public void deleteRepayCashBack(DeleteRepayCashBackEvent event) {
        if (event.getId() == null || event.getOptId() == null || StringUtils.isBlank(
                event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.deleteRepayCashBack(event);
    }

    /**
     * 区间还款配置优惠券-新增
     */
    @Override
    public void saveQjhkCouponConfig(CreateQjhkCouponConfigEvent event) {
        if (event.getMinAmount() == null || event.getMaxAmount() == null
                || event.getPeriodNum() == null || event.getCouponId() == null
                || event.getOptId() == null || StringUtils.isBlank(event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.saveQjhkCouponConfig(event);
    }

    /**
     * 区间还款配置优惠券-列表
     */
    @Override
    public PageResultEntity<PlusQjhkCouponConfigEntity> getQjhkCouponConfigListPage(
            QueryQjhkCouponConfigReq req) {
        return plusProfitModel.getQjhkCouponConfigListPage(req);
    }

    /**
     * 区间还款配置优惠券-编辑
     */
    @Override
    public void editQjhkCouponConfig(EditQjhkCouponConfigEvent event) {
        if (event.getId() == null || event.getMinAmount() == null || event.getMaxAmount() == null
                || event.getPeriodNum() == null || event.getCouponId() == null
                || event.getOptId() == null || StringUtils.isBlank(event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.editQjhkCouponConfig(event);
    }

    /**
     * 区间还款配置优惠券-修改状态
     */
    @Override
    public void editQjhkCouponConfigState(EditQjhkCouponConfigStateEvent event) {
        if (event.getId() == null || event.getState() == null || event.getOptId() == null
                || StringUtils.isBlank(event.getOptName())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        plusProfitModel.editQjhkCouponConfigState(event);
    }

    /**
     * 区间还款配置优惠券-刷新缓存
     */
    @Override
    public void refreshQjhkCouponConfig() {
        plusProfitModel.refreshQjhkCouponConfig();
    }

    @Override
    public boolean updateProfitVirtualImgUrl(UpdateProfitVirtualEvent event) {
        if (StringUtils.isBlank(event.getProductSku()) || StringUtils.isBlank(event.getImgUrl())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        return plusProfitModel.updateProfitVirtualImgUrl(event);
    }

}
