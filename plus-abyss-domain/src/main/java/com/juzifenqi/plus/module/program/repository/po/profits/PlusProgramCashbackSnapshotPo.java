package com.juzifenqi.plus.module.program.repository.po.profits;

import com.juzifenqi.plus.enums.GwfxCashTypeEnum;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * <p>
 * 购物返现权益快照表
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Data
public class PlusProgramCashbackSnapshotPo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 权益类型id
     */
    private Integer modelId;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 下单笔数
     */
    private Integer orderNum;

    /**
     * 返现类型：1_固定金额 2_会员费
     *
     * @see GwfxCashTypeEnum
     */
    private Integer cashbackType;

    /**
     * 返现金额
     */
    private BigDecimal cashbackAmount;

    /**
     * 未下单跳转连接
     */
    private String goodsUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
