package com.juzifenqi.plus.module.program.model.event;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 区间还款配置优惠券-编辑
 *
 * <AUTHOR>
 * @date 2024/7/31 下午2:03
 */
@Data
public class EditQjhkCouponConfigEvent {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 减免金额最小金额
     */
    private BigDecimal minAmount;

    /**
     * 减免金额最大金额
     */
    private BigDecimal maxAmount;

    /**
     * 期数
     */
    private Integer periodNum;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作者姓名
     */
    private String optName;

}
