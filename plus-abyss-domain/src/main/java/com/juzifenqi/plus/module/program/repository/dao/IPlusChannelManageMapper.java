package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.dto.req.admin.program.ChannelManagerQueryReq;
import com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelManagerEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Filename: PlusChannelManageDao.java
 * @Version: 1.0
 * @Author:**
 * @Email: **@**.com
 * @Date: 2022-12-14 11:50:24
 * @Description: 渠道管理
 */
@Mapper
public interface IPlusChannelManageMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusChannelManage(PlusChannelManagePo plusChannelManage);

    /**
     * 新增
     */
    Integer insertPlusChannelManage(
            @Param("plusChannelManage") PlusChannelManagePo plusChannelManage);

    /**
     * 删除
     */
    Integer deletePlusChannelManage(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusChannelManage(
            @Param("plusChannelManage") PlusChannelManagePo plusChannelManage);

    /**
     * Load查询
     */
    PlusChannelManagePo loadPlusChannelManage(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusChannelManagePo> pageList(@Param("offset") Integer offset,
            @Param("pagesize") Integer pagesize);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("offset") Integer offset, @Param("pagesize") Integer pagesize);

    /**
     * 通过channelId查询
     */
    PlusChannelManagePo getByChannelId(Integer channelId);

    /**
     * 列表
     */
    List<PlusChannelManagePo> selectList(
            @Param("channelManageDTO") ChannelManagerQueryReq channelManageDTO);

    /**
     * 数量
     */
    int coutList(@Param("channelManageDTO") ChannelManagerQueryReq channelManageDTO);

    /**
     * 获取支持某个配置的渠道列表
     */
    List<PlusChannelManagePo> getByConfig(@Param("configType") String configType);

    /**
     * 获取渠道列表
     */
    List<PlusChannelManagerEntity> getChannelList(ChannelManagerQueryReq channelManageDTO);

}
