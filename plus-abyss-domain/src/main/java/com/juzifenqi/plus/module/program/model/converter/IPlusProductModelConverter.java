package com.juzifenqi.plus.module.program.model.converter;

import com.juzifenqi.plus.module.program.model.contract.entity.LyffVirtualInfoEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProductDetailEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.RdzxEquityInfoEntity;
import com.juzifenqi.product.search.vo.ProductDetailCacheVo;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 会员sp转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/4 17:37
 */
@Mapper
public interface IPlusProductModelConverter {

    IPlusProductModelConverter instance = Mappers.getMapper(IPlusProductModelConverter.class);

    @Mappings({@Mapping(target = "productImageList", source = "productImages"),
            @Mapping(target = "marketPrice", source = "plusMarketPrice"),
            @Mapping(target = "productDetail", source = "productdetail"),
            @Mapping(target = "skuList", source = "list")})
    PlusProductDetailEntity toProductDetailEntity(ProductDetailCacheVo product);

    LyffVirtualInfoEntity toLyffVirtualInfoEntity(String imgUrl, String productName,
            String plusMarketPrice, Boolean isMatch);

    RdzxEquityInfoEntity toRdzxEquityInfoEntity(String imgUrl, String productName,
            String plusMarketPrice, Boolean isMatch);
}
