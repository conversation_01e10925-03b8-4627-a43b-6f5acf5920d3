package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.dto.req.profits.QueryProgramCouponReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramTaskEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo;
import java.util.List;

/**
 * 方案下配置的优惠券信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 10:29
 */
public interface IPlusProgramCouponRepository {

    /**
     * 批量保存息费折扣券-后台
     */
    void batchSaveXFZKCoupon(List<CreateProgramCouponEvent> list);

    /**
     * 批量保存多买多送任务信息-后台
     */
    void batchSaveDMDSTask(List<CreateProgramCouponEvent> list);

    /**
     * 批量保存拒就赔任务信息-后台
     */
    void batSaveRejection(List<CreateProgramCouponEvent> list);

    /**
     * 权益下配置的息费折扣优惠券分页列表-后台
     */
    PageResultEntity<PlusProgramCouponEntity> getXFZKCouponList(QueryProgramCouponReq event);

    /**
     * 权益下配置的多买多送优惠券分页列表-后台
     */
    PageResultEntity<PlusProgramCouponEntity> getDMDSCouponList(QueryProgramCouponReq event);

    /**
     * 权益下配置的拒就赔优惠券分页列表-后台
     */
    PageResultEntity<PlusProgramCouponEntity> getRejectCouponList(QueryProgramCouponReq event);

    /**
     * 删除息费折扣券配置信息-后台
     */
    void delXFZKCoupon(CreateProgramCouponEvent event);

    /**
     * 删除多买多送任务配置-后台
     */
    void delDMDSTask(CreateProgramCouponEvent event);

    /**
     * 删除拒就赔优惠券配置信息-后台
     */
    void delRejectCoupon(CreateProgramCouponEvent event);

    /**
     * 编辑多买多送任务-后台
     */
    void updDMDSTask(CreateProgramCouponEvent event);

    /**
     * 编辑拒就赔配置-后台
     */
    void updRejectCoupon(CreateProgramCouponEvent event);

    /**
     * 获取多买多送任务
     */
    List<PlusProgramTaskEntity> getProgramTaskList(Integer programId);

    /**
     * 获取拒就赔配置
     */
    List<PlusProgramRejectionPo> getRejectList(Integer programId);

    /**
     * 获取拒就赔配置
     */
    List<PlusProgramRejectionPo> getRejectListByIds(List<Integer> rejectIds);

    /**
     * 获取息费折扣配置
     */
    List<PlusProgramXFZKPo> getXfzkList(Integer programId);

    /**
     * Load查询
     */
    PlusProgramRejectionPo getRejectById(Integer id);
}
