package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员权益任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 14:10
 */
@Mapper
public interface IPlusProgramTaskMapper {

    /**
     * Load查询
     */
    PlusProgramTaskPo loadPlusProgramTask(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusProgramTaskPo> pageList(@Param("programId") Integer programId,
            @Param("start") int startPage, @Param("size") Integer pageSize);

    /**
     * 根据方案ID查询会员任务
     */
    List<PlusProgramTaskPo> getTaskCouponByProgramId(@Param("programId") Integer programId);

    /**
     * 查最大下单笔数
     */
    Integer getmMaxOrdersNumberDate(@Param("programId") Integer programId);

    /**
     * 新增返回ID
     */
    Integer savePlusProgramTask(PlusProgramTaskPo PlusProgramTask);

    /**
     * 新增
     */
    Integer insertPlusProgramTask(PlusProgramTaskPo PlusProgramTask);

    /**
     * 删除
     */
    Integer deletePlusProgramTask(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusProgramTask(@Param("PlusProgramTask") PlusProgramTaskPo PlusProgramTask);

    Integer getCountByProgramId(@Param("programId") Integer programId);

    /**
     * 批量保存
     */
    Boolean batchInsertPlusProgramTask(@Param("list") List<PlusProgramTaskPo> list);
}
