package com.juzifenqi.plus.module.order.model.contract.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;

/**
 * 会员订单详情退款信息Entity
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/2 10:38
 */
@Data
public class PlusOrderDetailRefundEntity implements Serializable {
    private static final long serialVersionUID = 42L;

    /**
     * ===========================退款信息=======================================
     */

    /**
     * 退款状态: 0_待处理 1_退款中 2_退款成功 3_退款失败 4_存疑
     */
    private Integer refundState;

    /**
     * 退款比例
     */
    private BigDecimal refundRatio;

    /**
     * 退款银行信息（银行卡后四位）
     */
    private String refundBankCardNo;

    /**
     * 打款银行名称
     */
    private String refundBankName;

    /**
     * 退款失败原因
     */
    private String refundFailMsg;

    /**
     * 退款方式：0 原路退 1 代付
     */
    private Integer refundType;

    /**
     * 出账方(实际出账方)
     */
    private String outSupplier;

    /**
     * 计划入账方
     */
    private String planInSupplier;

    /**
     * 实际入账方
     */
    private String inSupplier;

    /**
     * 退款流水号
     */
    private String refundSerialNumber;

    /**
     * 是否扣减生活权益差价
     */
    private Boolean needDeductPrice;

    /**
     * 权益数量
     */
    private Integer profitNum;

    /**
     * 差价
     */
    private BigDecimal deductPrice;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 退款通道
     */
    private String refundChannel;

    /**
     * 退费时间
     */
    private Date refundTime;

    /**
     * 更新时间
     */
    private Date updateTime;
}
