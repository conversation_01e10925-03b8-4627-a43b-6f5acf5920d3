package com.juzifenqi.plus.module.program.repository.dao.profits;


import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;
import org.apache.ibatis.annotations.Mapper;

/**
 * 提额等级
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29 18:25
 */
@Mapper
public interface IPlusLiftAmountMapper {

    /**
     * 根据方案id查提额数据
     */
    PlusLiftAmountPo getPlusLiftAmountByProgramId(Integer programId);


    /**
     * 新增返回ID
     */
    Integer savePlusLiftAmount(PlusLiftAmountPo po);

    /**
     * 更新
     */
    Integer updatePlusLiftAmount(PlusLiftAmountPo po);

    /**
     * 根据方案id查提额数据
     */
    PlusLiftAmountPo getPlusLiftAmountByProgramIdAndProgramUp(Integer programId);

    /**
     * 删除提额数据
     */
    Integer deletePlusLiftAmountByProgramId(Integer programId);
}
