package com.juzifenqi.plus.module.program.model.contract.entity;

import com.juzifenqi.plus.enums.RdSendTypeEnum;
import lombok.Data;

/**
 * 融担咨询卡权益配置信息
 *
 * <AUTHOR>
 * @date 2024/6/13 下午5:51
 */
@Data
public class RdzxEquityConfigInfoEntity {

    /**
     * 开启权益发放类型
     *
     * @see RdSendTypeEnum
     */
    private Integer rdSendType = RdSendTypeEnum.JSSH.getCode();

    /**
     * 挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer popUp = 1;

    /**
     * 二次挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer twoPopUp = 0;

    /**
     * 关闭挽留弹窗是否选中融担卡 0-否 1-是
     */
    private Integer rdChoose = 0;

    /**
     * 权益配置信息
     */
    private RdzxEquityInfoEntity equityInfo;

}
