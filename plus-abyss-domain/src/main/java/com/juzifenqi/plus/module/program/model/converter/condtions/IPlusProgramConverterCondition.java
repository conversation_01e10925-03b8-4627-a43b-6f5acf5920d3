package com.juzifenqi.plus.module.program.model.converter.condtions;

import java.util.Date;
import org.mapstruct.Named;
import org.springframework.stereotype.Component;

/**
 * 自定义转换
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9 14:52
 */
@Component
public class IPlusProgramConverterCondition {

    @Named("convertDiscountTime")
    public Long convertDiscountTime(Date discountEndTime) {
        if (discountEndTime == null) {
            return null;
        }
        return discountEndTime.getTime() - new Date().getTime();
    }
}
