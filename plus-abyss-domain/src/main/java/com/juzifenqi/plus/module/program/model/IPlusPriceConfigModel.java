package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.dto.req.admin.program.PriceQueryReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DefaultPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DiffPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;

/**
 * 差异化/默认方案配置model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11 10:29
 */
public interface IPlusPriceConfigModel {

    /**
     * 新增差异化方案
     */
    void saveDiffPrice(SaveDiffPriceEvent event);

    /**
     * 编辑差异化方案
     */
    void editDiffPrice(SaveDiffPriceEvent event);

    /**
     * 获取差异化定价详情
     */
    DiffPriceDetailEntity getDiffDetail(Integer id);

    /**
     * 保存默认方案配置
     */
    void addDefaultPrice(SaveDefaultPriceEvent event);

    /**
     * 编辑默认方案配置
     */
    void editDefaultPrice(SaveDefaultPriceEvent event);

    /**
     * 默认方案配置详情
     */
    DefaultPriceDetailEntity getDefaultPriceDetail(Integer id);

    /**
     * 获取差异化/默认方案配置分页列表
     */
    PageResultEntity<PlusProgramPriceListEntity> getPriceList(PriceQueryReq req);
}
