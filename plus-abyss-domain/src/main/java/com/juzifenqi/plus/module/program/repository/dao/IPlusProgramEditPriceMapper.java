package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.PlusProgramEditPricePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 方案改价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 14:31
 */
@Mapper
public interface IPlusProgramEditPriceMapper {

    /**
     * 新增返回ID
     */
    Integer saveMemberPlusEditPrice(PlusProgramEditPricePo memberPlusEditPrice);

    List<PlusProgramEditPricePo> getMemberPlusEditPriceByProgram(
            @Param("programId") Integer programId);

    List<PlusProgramEditPricePo> getLastDataByResultAndTime(@Param("result") Integer result);

    Integer updateResultById(@Param("id") Integer id, @Param("result") Integer result);

    Integer updateResultByTimeAndProgramIdAndIgnoreId(@Param("programId") Integer programId,
            @Param("id") Integer id, @Param("result") Integer result,
            @Param("nowResult") Integer nowResult);

    List<PlusProgramEditPricePo> getMemberPlusEditPriceByProgramPage(
            @Param("programId") Integer pragramId, @Param("start") Integer startPage,
            @Param("size") Integer pageSize);

    Integer countMemberPlusEditPriceByProgram(@Param("programId") Integer pragramId);
}
