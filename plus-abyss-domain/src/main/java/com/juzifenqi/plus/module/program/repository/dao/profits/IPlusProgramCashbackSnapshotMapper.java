package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackSnapshotPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <p>
 * 购物返现权益快照表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Mapper
public interface IPlusProgramCashbackSnapshotMapper {

    Integer insertBatch(@Param("snapshots") List<PlusProgramCashbackSnapshotPo> snapshots);

    List<PlusProgramCashbackSnapshotPo> queryAuthSnapshots(@Param("plusOrderSn") String plusOrderSn,
            @Param("programId") Integer programId, @Param("modelIds") List<Integer> modelIds);

    List<PlusProgramCashbackSnapshotPo> querySnapshots(@Param("plusOrderSn") String plusOrderSn,
            @Param("programId") Integer programId, @Param("modelId") Integer modelId);

    /**
     * 获取权益快照
     */
    List<PlusProgramCashbackSnapshotPo> getSnapshots(
            @Param("plusOrderSns") List<String> plusOrderSns);
}
