package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramQjhkEntity;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkEvent;
import java.math.BigDecimal;

/**
 * 区间还款
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/25 10:09
 */
public interface IPlusProgramQjhkRepository {

    /**
     * 保存
     */
    Integer save(CreateQjhkEvent entity);

    /**
     * 更新
     */
    void update(CreateQjhkEvent entity);

    /**
     * 详情
     */
    PlusProgramQjhkEntity getByProgramId(Integer programId);

    /**
     * 根据id查询数据
     */
    PlusProgramQjhkEntity getById(Integer id);

    /**
     * 获取区间还款折扣比例配置
     * <p>无配置则返回默认值0.95</p>
     */
    BigDecimal getQjhkDiscountRate(Integer programId);
}
