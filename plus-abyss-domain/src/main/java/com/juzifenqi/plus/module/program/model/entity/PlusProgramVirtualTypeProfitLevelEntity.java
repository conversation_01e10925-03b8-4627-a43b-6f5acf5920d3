package com.juzifenqi.plus.module.program.model.entity;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/27 19:24
 * @description
 */
@Data
public class PlusProgramVirtualTypeProfitLevelEntity implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 分类名称
     */
    private String typeName;

    /**
     * 购买次数限制 0_不限制购买次数 1_每月购买一次 2_限会员有效期购买一次 3_每周期购买一次
     */
    private Integer numLimit;

    /**
     * 购买分类下权益种类数量限制 0_不限制 1_限制
     */
    private Integer typeLimit;

    /**
     * 可选分类下权益种类数量
     */
    private Integer typeLimitNum;

    /**
     * 排序序号
     */
    private Integer rankNum;

    /**
     * 父级ID
     */
    private Integer parentId;

    /**
     * 分类层级
     */
    private Integer typeLevel;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 领取开始时间 yyyy年MM月dd日HH时
     */
    private String receiveStartTime;

    /**
     * 领取结束时间 yyyy年MM月dd日HH时
     */
    private String receiveEndTime;

    /**
     * 关联子分类
     */
    private List<PlusProgramVirtualTypeProfitLevelEntity> virtualTypeVos;

    /**
     * 关联权益
     */
    private List<PlusProgramVirtualEntity> productsInfo;
}
