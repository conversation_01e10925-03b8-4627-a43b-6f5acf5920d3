package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusHalfPriceProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductNewEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductTypeEntity;
import com.juzifenqi.plus.module.program.model.event.CreateHalfPriceProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductNewEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductTypeEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramHalfPriceProductPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProductInfoPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PlusProgramProductConverter {

    PlusProgramProductConverter instantiate = Mappers.getMapper(PlusProgramProductConverter.class);

    PlusProgramProductTypePo toPlusProgramProductTypePo(CreatePlusProductTypeEvent event);

    PlusProgramProductNewPo toPlusProgramProductNewPo(CreatePlusProductNewEvent event);

    PlusProgramProductNewEntity toPlusProgramProductNewEntity(PlusProgramProductNewPo po);

    PlusProgramHalfPriceProductPo toPlusProgramHalfPriceProductPo(
            CreateHalfPriceProductEvent event);

    @Mappings(@Mapping(target = "saleState", source = "onSaleState"))
    PlusProductInfoPo toPlusProductInfoPo(CreatePlusProductEvent event);

    List<PlusProgramProductTypeEntity> toPlusProgramProductTypeEntityList(
            List<PlusProgramProductTypePo> list);

    List<PlusProgramProductNewEntity> toPlusProgramProductNewEntityList(
            List<PlusProgramProductNewPo> list);

    List<PlusProgramProductEntity> toPlusProgramProductEntityList(List<PlusProductInfoPo> po);

    List<PlusHalfPriceProductEntity> toPlusProgramHalfPriceProductEntityList(
            List<PlusProgramHalfPriceProductPo> list);

    List<PlusProgramHalfPriceProductPo> toPlusProgramHalfPriceProductPoList(
            List<CreateHalfPriceProductEvent> list);

    List<PlusProgramProductNewPo> toPlusProgramProductNewPoList(
            List<CreatePlusProductNewEvent> list);

    PlusProgramProductEntity toPlusProgramProductEntity(PlusProductInfoPo po);

    PlusProgramProductTypeEntity toPlusProgramProductTypeEntity(PlusProgramProductTypePo po);
}
