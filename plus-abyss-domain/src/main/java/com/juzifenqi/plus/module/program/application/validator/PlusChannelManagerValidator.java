package com.juzifenqi.plus.module.program.application.validator;

import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.program.model.IPlusChannelManagerQueryModel;
import com.juzifenqi.plus.module.program.model.event.CreateChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EditChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EnableChannelManageEvent;
import com.juzifenqi.plus.utils.RedisLock;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 渠道管理配置校验器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 20:03
 */
@Slf4j
@Component
public class PlusChannelManagerValidator {

    @Autowired
    private RedisLock                     redisLock;
    @Autowired
    private IPlusChannelManagerQueryModel queryModel;

    /**
     * 校验创建入参
     */
    public void checkCreateParam(CreateChannelManagerEvent event) {
        String lockKey = RedisConstantPrefix.PLUS_CHANNEL_MANAGE_ADD_LOCK + event.getChannelId();
        boolean lock = redisLock.lock(lockKey, "1", 5);
        if (!lock) {
            throw new PlusAbyssException("请勿重复操作！");
        }
        // 校验是否已配置
        if (Objects.nonNull(queryModel.getByChannelId(event.getChannelId()))) {
            throw new PlusAbyssException("渠道已存在");
        }
    }

    /**
     * 校验编辑入参
     */
    public void checkEditeParam(EditChannelManagerEvent event) {
        String lockKey = RedisConstantPrefix.PLUS_CHANNEL_MANAGE_EDIT_LOCK + event.getId();
        boolean lock = redisLock.lock(lockKey, "1", 5);
        if (!lock) {
            throw new PlusAbyssException("请勿重复操作！");
        }
    }

    /**
     * 校验发布入参
     */
    public void checkEnableParam(EnableChannelManageEvent event) {
        String lockKey = RedisConstantPrefix.PLUS_CHANNEL_MANAGE_ISSUE_LOCK + event.getId();
        boolean lock = redisLock.lock(lockKey, "1", 5);
        if (!lock) {
            throw new PlusAbyssException("请勿重复操作！");
        }
    }
}
