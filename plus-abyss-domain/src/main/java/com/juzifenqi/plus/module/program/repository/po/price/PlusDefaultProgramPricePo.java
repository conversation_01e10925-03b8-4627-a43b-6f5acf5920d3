package com.juzifenqi.plus.module.program.repository.po.price;

import java.util.Date;
import lombok.Data;

/**
 * 默认方案价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 10:50
 */
@Data
public class PlusDefaultProgramPricePo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 主表id
     */
    private Integer priceId;

    /**
     * 默认方案id
     */
    private Integer programId;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;
}
