package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusLiftAmountEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IPlusProgramQueryModel {

    /**
     * 获取方案实体
     */
    PlusProgramEntity getById(Integer id);

    /**
     * 获取方案信息及方案配置的权益包信息
     * <p>目前用于开通会员生成权益发放计划使用</p>
     */
    PlusProgramEntity getProgramAndProfitsPackage(Integer id, String plusOrderSn);

    /**
     * 获取某个方案下是否配置了某个权益
     */
    int countProModelByProgramId(Integer programId, Integer modelId);

    /**
     * 获取方案下的提额等级
     */
    PlusLiftAmountEntity getByProgramId(Integer programId);

    /**
     * 按方案id和sku获取虚拟权益
     */
    PlusProgramVirtualEntity getByProgramAndSku(Integer programId, Integer modelId, String sku);

    /**
     * 获取联名卡权益
     */
    PlusProgramLmkVirtualEntity getVirtualById(Integer id);

    /**
     * 获取方案下联名权益列表
     */
    List<PlusProgramLmkVirtualEntity> getLmkList(Integer programId);

    /**
     * 获取虚拟权益分类
     */
    PlusProgramVirtualTypePo getVirtualTypeById(Integer id);

    /**
     * 是否支持后付款
     */
    boolean supportAfterPay(Integer programId, Integer userId, Integer channelId);

    /**
     * 获取方案续费配置
     */
    PlusRenewRelevancePo getRenewRelevanceByProgramId(Integer programId);

    /**
     * 获取会员类型id生效的方案
     */
    List<PlusProgramEntity> getProgramByConfigId(Integer configId);

    /**
     * 获取0元商品权益分类
     */
    PlusProgramProductTypePo getProductType(String productSku, Integer programId, Integer modelId);

    /**
     * 获取某个方案的结清返现金额配置
     */
    BigDecimal getSettleCashbackAmount(Integer configId, Integer programId);

    /**
     *
     */
    List<PlusProModelEntity> getPlusProModelList(Integer programId);

}
