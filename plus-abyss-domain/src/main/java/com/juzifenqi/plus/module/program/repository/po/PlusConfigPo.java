package com.juzifenqi.plus.module.program.repository.po;


import java.util.Date;
import lombok.Data;

/**
 * PlusConfigPo 
 * <AUTHOR> 
 * @Description  
 * @Date 2024/06/14 11:22
**/
@Data
public class PlusConfigPo {

    /**
     *
     */
    private int id;
    /**
     * 类型卡名称
     */
    private  String name;
    /**
     * 类型唯一标识
     */
    private String signConfig;

    /**
     * 状态 1启用 2不启用
     */
    private int status;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 编辑时间
     */
    private Date updateTime;

    /**
     * 更新时间
     */
    private Date btTime;
}
