package com.juzifenqi.plus.module.program.model.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 通信联名卡
 *
 * <AUTHOR>
 * @createTime 2024/2/28 17:46
 * @description
 */
@Data
public class PlusProgramTxEntity implements Serializable {

    private static final long serialVersionUID = -472404948577745914L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 广告图url
     */
    private String imgUrl;

    /**
     * 跳转url
     */
    private String returnUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private int updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserName;
}
