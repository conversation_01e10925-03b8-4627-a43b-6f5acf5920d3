package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramTaskEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 会员权益任务转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 14:13
 */
@Mapper
public interface PlusProgramTaskConverter {

    PlusProgramTaskConverter instance = Mappers.getMapper(PlusProgramTaskConverter.class);

    List<PlusProgramTaskEntity> toPlusProgramTaskEntity(List<PlusProgramTaskPo> po);
}
