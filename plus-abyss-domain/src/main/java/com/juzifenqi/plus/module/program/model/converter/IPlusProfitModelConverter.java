package com.juzifenqi.plus.module.program.model.converter;

import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitModelBasicDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusHalfPriceProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductNewEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeProfitLevelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusQjhkCouponConfigEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandModelBasicDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import com.juzifenqi.plus.module.program.model.event.CreateLyffVirtualGoodsEvent.LyffVirtualGoods;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramLmkVirtualEvent;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.model.event.CreateRepayCashBackEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import com.juzifenqi.product.entity.Product;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/4 10:46
 */
@Mapper
public interface IPlusProfitModelConverter {

    IPlusProfitModelConverter instance = Mappers.getMapper(IPlusProfitModelConverter.class);

    PlusHalfPriceProductEntity toPlusHalfPriceProductEntity(Product product);

    PlusProgramProductNewEntity toPlusProgramProductNewEntity(Product product);

    PlusProgramVirtualTypeProfitLevelEntity toPlusProgramVirtualTypeProfitLevelEntity(
            PlusProgramVirtualTypeEntity entity);

    PlusProgramVirtualTypeEntity toPlusProgramVirtualTypeEntity(PlusProgramVirtualTypePo po);

    List<PlusProgramLmkVirtualPo> toPlusProgramLmkVirtualPoList(
            List<CreatePlusProgramLmkVirtualEvent> list);

    List<PlusProgramVirtualPo> toPlusProgramVirtualPoList(List<CreateVirtualGoodsEvent> list);

    PlusProgramVirtualPo toPlusProgramLyffVirtualPo(Integer programId, Integer configId,
            Integer modelId, LyffVirtualGoods goods);

    ProfitModelBasicDetailEntity toProfitModelBasicDetailEntity(PlusProModelEntity entity);

    LandModelBasicDetailEntity toModelBasicDetailEntity(PlusProModelEntity entity);

    LandOldDetailEntity toLandOldDetailEntity(PlusProgramEntity entity);

    @Mappings({@Mapping(target = "createUserId", source = "optId"),
            @Mapping(target = "createUserName", source = "optName")})
    PlusProgramCashbackEntity toPlusProgramCashbackEntity(CreateRepayCashBackEvent event);

    PlusQjhkCouponConfigEntity toPlusQjhkCouponConfigEntity(CreateQjhkCouponConfigEvent event);
}
