package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.dto.req.profits.QueryLmkVirtualReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramLmkVirtualEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo;
import java.util.List;

/**
 * 联名卡
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 16:18
 */
public interface IPlusLmkVirtualRepository {

    PlusProgramLmkVirtualEntity getVirtualById(Integer id);

    List<PlusProgramLmkVirtualEntity> getLmkList(Integer programId);

    /**
     * 根据条件查询分页集合
     */
    PageResultEntity<PlusProgramLmkVirtualEntity> getListPage(QueryLmkVirtualReq req);

    /**
     * 删除某个权益
     */
    void delete(Integer id);

    /**
     * 编辑某个权益
     */
    void edit(CreatePlusProgramLmkVirtualEvent event);

    /**
     * 批量添加
     */
    void saveBatch(List<PlusProgramLmkVirtualPo> list);

    /**
     * 修改虚拟权益状态和缓存---虚拟三方服务虚拟权益上下架时调用
     */
    int updateVirtualState(CreatePlusProgramLmkVirtualEvent event);

    PlusProgramLmkVirtualEntity selectBySkuAndProgramId(String sku, Integer programId);

    /**
     * 根据sku查询
     */
    List<PlusProgramLmkVirtualEntity> selectBySku(String sku);

    List<PlusProgramLmkVirtualEntity> selectByProgramId(Integer programId);

    /**
     * 获取配置数量
     */
    int countByProgramId(Integer programId);
}
