package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.price.PlusDefaultProgramPricePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 默认方案价mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/13 10:52
 */
@Mapper
public interface IPlusDefaultProgramPriceMapper {

    /**
     * 新增返回ID
     */
    int insert(PlusDefaultProgramPricePo plusDefaultProgramPrice);

    /**
     * Load查询
     */
    PlusDefaultProgramPricePo selectById(Integer id);

    /**
     * 列表
     */
    List<PlusDefaultProgramPricePo> selectByPriceId(Integer priceId);

    /**
     * 删除某个配置的差异化定价明细
     */
    int deleteByPriceId(Integer priceId);
}
