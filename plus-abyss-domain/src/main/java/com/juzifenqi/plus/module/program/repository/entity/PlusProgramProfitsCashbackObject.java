package com.juzifenqi.plus.module.program.repository.entity;

import com.juzifenqi.plus.enums.PlusProfitsGroupEnum;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> 会员权益：返现类权益
 */
@Data
public class PlusProgramProfitsCashbackObject {

    private Integer profitsId;

    private Integer programId;

    private Integer modelId;

    private Integer packageId;

    /**
     * 权益类型（购物返现、结清返现、还款返现）
     */
    private Integer profitType = PlusProfitsGroupEnum.PLUS_CASH.getValue();

    /**
     * 达成条件
     */
    private List<PlusReachConditionObject> plusReachCondition;


    /**
     * 现金奖励类
     */
    private BigDecimal cashbackAmount;

    /**
     * 订单笔数
     */
    private Integer orderNum;

    /**
     * 购物返现类型
     */
    private Integer cashbackType;
}
