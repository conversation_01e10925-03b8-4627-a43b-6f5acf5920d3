package com.juzifenqi.plus.module.program.repository.impl;

import static com.juzifenqi.plus.enums.PlusModelEnum.LYSP;

import com.juzifenqi.plus.dto.req.profits.QueryHalfPriceProductReq;
import com.juzifenqi.plus.dto.req.profits.QueryPlusProductReq;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramProductRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusHalfPriceProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductNewEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductTypeEntity;
import com.juzifenqi.plus.module.program.model.event.CreateHalfPriceProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductNewEvent;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProductTypeEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramProductConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProductInfoMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramHalfPriceProductMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductNewMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductTypeMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProductInfoPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramHalfPriceProductPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 * @createTime 2024/2/28 14:05
 * @description
 */
@Repository
@Slf4j
public class PlusProgramProductRepositoryImpl implements IPlusProgramProductRepository {

    private final PlusProgramProductConverter converter = PlusProgramProductConverter.instantiate;

    @Autowired
    private IPlusProgramProductTypeMapper      programProductTypeMapper;
    @Autowired
    private IPlusProgramProductNewMapper       productNewMapper;
    @Autowired
    private IPlusProgramHalfPriceProductMapper halfPriceProductMapper;
    @Autowired
    private IPlusProductInfoMapper             productInfoMapper;

    @Override
    public void savePlusProductType(CreatePlusProductTypeEvent event) {
        //序号重复检查
        List<Integer> rankNumList = programProductTypeMapper.selectRankNumList(event.getProgramId(),
                event.getModelId());
        if (rankNumList.contains(event.getRankNum())) {
            throw new PlusAbyssException("当前分类序号重复:" + event.getRankNum());
        }
        programProductTypeMapper.saveProductType(converter.toPlusProgramProductTypePo(event));
    }

    @Override
    public PlusProgramProductTypeEntity editPlusProductType(CreatePlusProductTypeEvent event) {
        PlusProgramProductTypePo type = programProductTypeMapper.getProductTypeById(event.getId());
        if (type == null) {
            throw new PlusAbyssException("无效的分类id");
        }
        List<PlusProgramProductTypePo> types = programProductTypeMapper.selectTypeListByProgram(
                event.getProgramId(), event.getModelId());
        List<Integer> collect = types.stream()
                .filter(x -> !Objects.equals(x.getId(), event.getId()))
                .map(PlusProgramProductTypePo::getRankNum).collect(Collectors.toList());
        if (collect.contains(event.getRankNum())) {
            throw new PlusAbyssException("当前分类序号重复:" + event.getRankNum());
        }
        programProductTypeMapper.updateProductType(converter.toPlusProgramProductTypePo(event));
        return converter.toPlusProgramProductTypeEntity(type);
    }

    @Override
    public PageResultEntity<PlusProgramProductTypeEntity> getPlusProductType(Integer programId,
            Integer modelId) {
        PageResultEntity<PlusProgramProductTypeEntity> result = new PageResultEntity<>();
        List<PlusProgramProductTypePo> typeList = programProductTypeMapper.getByProgramAndModel(
                programId, modelId);
        result.setList(converter.toPlusProgramProductTypeEntityList(typeList));
        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delPlusProductType(CreatePlusProductTypeEvent event) {
        programProductTypeMapper.deleteProductTypeById(event.getId());
        productNewMapper.delByProgramAndModelAndType(event.getProgramId(), event.getModelId(),
                event.getId());
    }

    @Override
    public PlusProgramProductTypeEntity getProductTypeById(Integer id) {
        return converter.toPlusProgramProductTypeEntity(
                programProductTypeMapper.getProductTypeById(id));
    }

    @Override
    public PageResultEntity<PlusHalfPriceProductEntity> getHalfPriceProductList(
            QueryHalfPriceProductReq req) {
        PageResultEntity<PlusHalfPriceProductEntity> result = new PageResultEntity<>();
        List<PlusProgramHalfPriceProductPo> halfPriceProductPoList = halfPriceProductMapper.getHalfPriceProduct(
                req.getStartPage(), req.getPageSize());
        Integer count = halfPriceProductMapper.getHalfPriceProductCount();
        result.setList(converter.toPlusProgramHalfPriceProductEntityList(halfPriceProductPoList));
        result.setCount(count);
        return result;
    }

    @Override
    public void savePlusProgramHalfPriceProduct(List<CreateHalfPriceProductEvent> events) {
        List<CreateHalfPriceProductEvent> listSave = new ArrayList<>();
        for (CreateHalfPriceProductEvent halfPriceProduct : events) {
            Integer byProductCodeCount = halfPriceProductMapper.getByProductCodeById(
                    halfPriceProduct.getProductId(), halfPriceProduct.getProgramId());
            if (byProductCodeCount < 1) {
                //进行批量插入操作
                CreateHalfPriceProductEvent halfProduct = new CreateHalfPriceProductEvent();
                halfProduct.setProductId(halfPriceProduct.getProductId());
                halfProduct.setProgramId(halfPriceProduct.getProgramId());
                listSave.add(halfProduct);
            }
        }
        if (CollectionUtils.isEmpty(listSave)) {
            return;
        }
        halfPriceProductMapper.savePlusProgramHalfPriceProduct(
                converter.toPlusProgramHalfPriceProductPoList(listSave));
    }

    @Override
    public void deletePlusProgramHalfPriceProduct(Integer id) {
        halfPriceProductMapper.deletePlusProgramHalfPriceProduct(id);
    }

    @Override
    public void updatePlusProgramHalfPriceProduct(CreateHalfPriceProductEvent event) {
        halfPriceProductMapper.updatePlusProgramHalfPriceProduct(
                converter.toPlusProgramHalfPriceProductPo(event));
    }

    @Override
    public List<PlusProgramProductNewEntity> getProductNewList(Integer programId, Integer modelId,
            Integer typeId) {
        List<PlusProgramProductNewPo> productNewList = productNewMapper.getProductNewList(programId,
                modelId, typeId);
        return converter.toPlusProgramProductNewEntityList(productNewList);
    }

    @Override
    public List<PlusProgramProductNewEntity> batchDelYygProduct(
            List<CreatePlusProductNewEvent> list) {
        List<PlusProgramProductNewPo> poList = converter.toPlusProgramProductNewPoList(list);
        productNewMapper.batchDelYygProduct(poList);
        return converter.toPlusProgramProductNewEntityList(poList);
    }

    @Override
    public PlusProgramProductNewEntity editPlusProduct(CreatePlusProductNewEvent event) {
        PlusProgramProductNewPo po = converter.toPlusProgramProductNewPo(event);
        productNewMapper.updateProductNew(po);
        return converter.toPlusProgramProductNewEntity(po);
    }

    @Override
    public List<PlusProgramProductNewEntity> batchSaveYygProduct(
            List<CreatePlusProductNewEvent> list) {
        List<PlusProgramProductNewPo> plusProgramProductNewPoList = converter.toPlusProgramProductNewPoList(
                list);
        productNewMapper.batchSaveYygProduct(plusProgramProductNewPoList);
        return converter.toPlusProgramProductNewEntityList(plusProgramProductNewPoList);
    }

    @Override
    public List<String> getExitProductNewList(List<CreatePlusProductNewEvent> list,
            List<CreatePlusProductNewEvent> targetList) {
        List<String> exit = new ArrayList<>();
        for (CreatePlusProductNewEvent yygProduct : list) {
            PlusProgramProductNewPo hasRes;
            if (Objects.equals(yygProduct.getModelId(), LYSP.getModelId())) {
                hasRes = productNewMapper.getByProductSkuAndProgramId(yygProduct.getProgramId(),
                        yygProduct.getProductSku(), yygProduct.getModelId());
                if (hasRes != null) {
                    exit.add(hasRes.getProductSku());
                }
            } else {
                hasRes = productNewMapper.getByProductIdAndProgramId(yygProduct.getProductId(),
                        yygProduct.getProgramId(), yygProduct.getModelId());
                if (hasRes != null) {
                    exit.add(String.valueOf(hasRes.getProductId()));
                }
            }
            if (hasRes == null) {
                yygProduct.setOptId(list.get(0).getOptId());
                yygProduct.setOptUser(list.get(0).getOptUser());
                targetList.add(yygProduct);
            }
        }
        return exit;
    }

    @Override
    public PageResultEntity<PlusProgramProductEntity> selectPlusProductList(
            QueryPlusProductReq req) {
        PageResultEntity<PlusProgramProductEntity> result = new PageResultEntity<>();
        int plusProductCount = productInfoMapper.selectPlusProductCount(req);
        List<PlusProductInfoPo> poList = productInfoMapper.selectPlusProductList(req);
        result.setCount(plusProductCount);
        result.setList(converter.toPlusProgramProductEntityList(poList));
        return result;
    }

    @Override
    public void savePlusProduct(CreatePlusProductEvent event) {
        productInfoMapper.savePlusProduct(converter.toPlusProductInfoPo(event));
    }

    @Override
    public PlusProgramProductEntity selectProductBySku(String sku) {
        return converter.toPlusProgramProductEntity(productInfoMapper.selectProductBySku(sku));
    }

    @Override
    public PlusProgramProductEntity selectProductById(Integer id) {
        return converter.toPlusProgramProductEntity(productInfoMapper.selectProductById(id));
    }

    @Override
    public PlusProgramProductEntity updatePlusProduct(CreatePlusProductEvent event) {
        PlusProductInfoPo oldInfo = productInfoMapper.selectProductById(event.getId());
        if (oldInfo == null) {
            throw new PlusAbyssException("无效的ID");
        }
        if (oldInfo.getSaleState() == 1) {
            throw new PlusAbyssException("上架状态不允许编辑");
        }
        productInfoMapper.updatePlusProduct(converter.toPlusProductInfoPo(event));
        return converter.toPlusProgramProductEntity(oldInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlusProgramProductEntity delPlusProduct(CreatePlusProductEvent event) {
        PlusProductInfoPo plusProductInfo = productInfoMapper.selectProductById(event.getId());
        if (plusProductInfo == null) {
            throw new PlusAbyssException("无效的ID");
        }
        if (plusProductInfo.getSaleState() == 1) {
            throw new PlusAbyssException("上架状态不可删除");
        }
        productInfoMapper.delById(event.getId());
        productNewMapper.delByProductSkuAndModelId(event.getProductSku(), LYSP.getModelId());
        return converter.toPlusProgramProductEntity(plusProductInfo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public PlusProgramProductEntity updatePlusProductState(CreatePlusProductEvent event) {
        PlusProductInfoPo plusProductInfo = productInfoMapper.selectProductById(event.getId());
        if (plusProductInfo == null) {
            throw new PlusAbyssException("未查询到商品信息");
        }
        productInfoMapper.updatePlusProductState(converter.toPlusProductInfoPo(event));
        productNewMapper.updateStateByModelAndProduct(LYSP.getModelId(),
                plusProductInfo.getProductSku(), event.getOnSaleState());
        return converter.toPlusProgramProductEntity(plusProductInfo);
    }

    @Override
    public List<PlusProgramProductNewEntity> getProductNewByProgramAndModel(Integer programId,
            Integer modelId) {
        return converter.toPlusProgramProductNewEntityList(
                productNewMapper.getByProgramAndModel(programId, modelId));
    }

    @Override
    public int getHalfPriceProductCount() {
        return halfPriceProductMapper.getHalfPriceProductCount();
    }

    @Override
    public List<Integer> getHalfPriceProductIdList() {
        return halfPriceProductMapper.halfPriceProductIdList();
    }

    @Override
    public List<Integer> yygProductIdList(Integer programId, Integer modelId) {
        return productNewMapper.yygProductIdList(programId, modelId);
    }

    @Override
    public List<Integer> getAllProductId(Integer modelId) {
        return productNewMapper.getAllProductId(modelId);
    }
}
