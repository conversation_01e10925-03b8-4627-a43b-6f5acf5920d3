package com.juzifenqi.plus.module.program.application;

import com.juzifenqi.plus.dto.req.PlusVirtualProductReq;
import com.juzifenqi.plus.dto.req.detail.LandReq;
import com.juzifenqi.plus.dto.req.detail.VirtualQueryReq;
import com.juzifenqi.plus.dto.resp.PlusBasicInfoReq;
import com.juzifenqi.plus.module.program.model.entity.PlusVirtualProductEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandVirtualProductTypeEntity;
import java.util.List;
import java.util.Map;

/**
 * 落地页
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 18:26
 */
public interface IPlusLandDetailApplication {

    /**
     * 新落地页详情
     */
    LandDetailEntity getLandDetail(LandReq event);

    /**
     * 老落地页详情
     */
    LandOldDetailEntity getOldLandDetail(LandReq event);

    /**
     * 方案购买记录轮播数据
     */
    List<String> getPlusBuyRecords(Integer programId);

    /**
     * 获取方案下的虚拟权益列表
     */
    List<LandVirtualProductTypeEntity> getVirtualProductList(VirtualQueryReq req);

    /**
     * 老落地页公共详情
     * <p>不返回权益明细数据，目前用于营销会员后，前端根据方案id获取方案的基础信息和折扣信息</p>
     */
    LandOldDetailEntity getLandCommonDetail(LandReq event);

    /**
     * 会员详情页获取顶部数据用到
     * <p>目前就还款卡调用</p>
     */
    Map<String, Object> getPlusBasicInfo(PlusBasicInfoReq req);
}
