package com.juzifenqi.plus.module.program.repository.po;

import com.juzifenqi.plus.enums.PlusProgramSendTypeEnum;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 方案扩展表
 * @date 2022/2/22 14:55
 */
@Data
public class PlusProgramExtendPo implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 模板id
     */
    private Integer configId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 渠道
     */
    private Integer channelId;

    /**
     * 模板名称
     */
    private String configName;

    /**
     * 二级方案id
     */
    private Integer secondConfigId;

    /**
     * 二级方案类型
     */
    private Integer secondConfigType;

    /**
     * 二级方案类型名称
     */
    private String secondConfigName;

    /**
     * 开启权益发放类型
     *
     * @see com.juzifenqi.plus.enums.RdSendTypeEnum
     */
    private Integer rdSendType;

    /**
     * 挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer popUp;

    /**
     * 二次挽留弹窗是否开启 0-关闭 1-开启
     */
    private Integer twoPopUp;

    /**
     * 关闭挽留弹窗是否选中融担卡 0-否 1-是
     */
    private Integer rdChoose;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人
     */
    private Integer updateUserId;
    /**
     * 发放节点 1_开卡即发 2_后付款支付成功发放
     * @see PlusProgramSendTypeEnum
     */
    private Integer sendNode;
}
