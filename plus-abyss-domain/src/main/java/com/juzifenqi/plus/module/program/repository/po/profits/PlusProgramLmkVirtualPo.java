package com.juzifenqi.plus.module.program.repository.po.profits;

import java.util.Date;
import lombok.Data;

/**
 * 联名卡权益表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/6 15:22
 */
@Data
public class PlusProgramLmkVirtualPo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 商品ID(即权益ID_product表主键)
     */
    private Integer productId;

    /**
     * 商品名称(即权益名称)
     */
    private String productName;

    /**
     * 虚拟商品ID(virtual_goods表主键)
     */
    private Integer virtualGoodsId;

    /**
     * 商品sku
     */
    private String sku;

    /**
     * 虚拟商品上下架 0_未上架 1_上架
     */
    private Integer virtualStatus;

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 修改人ID
     */
    private Integer updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserName;
}
