package com.juzifenqi.plus.module.program.application;

import com.juzifenqi.plus.dto.req.PlusVirtualProductReq;
import com.juzifenqi.plus.module.program.model.contract.entity.LyffVirtualDetailEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.LyffVirtualInfoEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProductDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusVirtualProductEntity;
import com.juzifenqi.plus.module.program.model.event.LyffVirtualDetailEvent;
import com.juzifenqi.plus.module.program.model.event.LyffVirtualInfoEvent;
import com.juzifenqi.plus.module.program.model.event.PlusProductDetailEvent;

/**
 * 会员商品api
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/4 17:29
 */
public interface IPlusProductApplication {

    /**
     * 获取会员商品详情页信息
     */
    PlusProductDetailEntity getPlusProductDetail(PlusProductDetailEvent event);

    /**
     * 查看权益0元发放虚拟商品详情
     */
    LyffVirtualInfoEntity getLyffVirtualInfo(LyffVirtualInfoEvent event);

    /**
     * 查看权益0元发放虚拟商品详情
     */
    LyffVirtualDetailEntity getLyffVirtualDetail(LyffVirtualDetailEvent event);

    /**
     * 虚拟商品详情页
     * <p>目前是生活权益</p>
     */
    PlusVirtualProductEntity getVirtualProductDetail(PlusVirtualProductReq req);
}
