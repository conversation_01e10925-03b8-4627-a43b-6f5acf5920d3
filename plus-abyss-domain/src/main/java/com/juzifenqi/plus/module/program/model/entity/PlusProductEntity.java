package com.juzifenqi.plus.module.program.model.entity;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员商品信息
 * <p>与商品product的区别是商品改版后市场价为mallMobilePrice</p>
 * <p>不影前端展示（不用发版），用malMobilePrice转一下</p>
 *
 * <AUTHOR>
 * @date 2021/1/18 17:45
 */
@Data
public class PlusProductEntity implements Serializable {

    private static final long serialVersionUID = 4743842357902446693L;

    private Integer    id;
    private Integer    thirdCateId;
    private Integer    secondCateId;
    private Integer    firstCateId;
    private String     name1;
    private String     promotion;
    private Integer    jumpType;
    private String     linkPage;
    private String     linkId;
    private String     shortName;
    private String     keyword;
    private Integer    productBrandId;
    private Integer    isSelf;
    private BigDecimal costPrice;
    private Integer    isNorm;
    private Integer    state;
    private Date       upTime;
    private Integer    sellerId;
    private String     sellerName;
    private Date       createTime;
    private Integer    createId;
    private Date       updateTime;
    private String     sellerCateId;
    private Integer    sellerIsTop;
    private String     masterImg;
    private String     productCode;
    private Date       offTime;
    private String     masterLittleImg;
    private String     tagFall;
    private String     tagFree;
    private Integer    isSearch;
    private Integer    isPickself;
    private Integer    limits;
    private Integer    identification;
    private Date       biTime;
    private String     productCateName;
    private String     productBrandName;
    private BigDecimal marketPrice;
    private BigDecimal protectedPrice;
    private BigDecimal malMobilePrice;
    private String     productPaymentMinInstallment;
    private Integer    productStock;
    private String     imagePath;
    private String     sku;
    private String     normName;
    private Integer    goodId;
    private Integer    productCateState;
    private String     deliveryChannel;
    private String     name2;
    private Integer    virtualSales;
    private String     key;
    private BigDecimal mallMobilePrice;
}
