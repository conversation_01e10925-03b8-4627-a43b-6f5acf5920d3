package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusCouponIndexEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 方案优惠券配置转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 15:24
 */
@Mapper
public interface PlusCouponIndexConverter {

    PlusCouponIndexConverter instance = Mappers.getMapper(PlusCouponIndexConverter.class);

    List<PlusCouponIndexEntity> toEntity(List<PlusCouponIndexPo> po);

    List<PlusCouponIndexPo> toPoList(List<CreateProgramCouponEvent> list);

    List<PlusProgramCouponEntity> toPlusProgramCouponEntity(List<PlusCouponIndexPo> list);
}
