package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.price.PlusDifferenceProgramPricePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 差异化定价mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 16:12
 */
@Mapper
public interface IPlusDifferenceProgramMapper {

    /**
     * 新增返回ID
     */
    int batchInsert(List<PlusDifferenceProgramPricePo> list);

    /**
     * Load查询
     */
    PlusDifferenceProgramPricePo selectById(Integer id);

    /**
     * 列表
     */
    List<PlusDifferenceProgramPricePo> selectByPriceId(Integer priceId);

    /**
     * 删除某个配置的差异化定价明细
     */
    int deleteByPriceId(Integer priceId);
}
