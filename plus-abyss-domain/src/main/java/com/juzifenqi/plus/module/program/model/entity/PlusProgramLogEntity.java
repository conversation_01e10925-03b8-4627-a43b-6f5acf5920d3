package com.juzifenqi.plus.module.program.model.entity;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 方案更新记录
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/12 10:54
 */
@Data
public class PlusProgramLogEntity implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     *
     */
    private Integer id;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 操作人ID，系统操作为0
     */
    private Integer operatingId;

    /**
     * 操作人
     */
    private String operatingName;

    /**
     * 操作事件
     */
    private String event;

    /**
     * 内容
     */
    private String content;

    /**
     * 创建时间
     */
    private Date createTime;
}