package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.PlusConfigPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * IPlusConfigMapper 
 * <AUTHOR> 
 * @Description  
 * @Date 2024/06/14 11:10
**/
@Mapper
public interface IPlusConfigMapper {


    /**
     * 新增返回ID
     */
    Integer saverPlusConfig(PlusConfigPo plusConfig);

    /**
     * 更新
     */
    Integer updatePlusConfig(PlusConfigPo plusConfig);

    /**
     * 根据标识查询一类型
     */
    PlusConfigPo loadPlusConfig(PlusConfigPo plusConfig);

    /**
     * 查询会员模板类型列表
     */
    List<PlusConfigPo> getPlusConfigList();

    /**
     * 校验标识是否存在
     */
    int checkSignConfig(String signConfig);

    /**
     * 根据标识查询一类型
     */
    PlusConfigPo getPlusConfigById(Integer configId);
}
