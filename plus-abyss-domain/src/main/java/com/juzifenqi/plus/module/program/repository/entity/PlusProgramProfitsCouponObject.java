package com.juzifenqi.plus.module.program.repository.entity;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR> 会员权益：券
 */
@Data
public class PlusProgramProfitsCouponObject implements Serializable {

    private static final long serialVersionUID = -8918242979250412838L;

    /**
     * 方案id
     */
    private Long programId;

    /**
     * 权益id
     */
    private Integer profitsId;

    /**
     * 券id
     */
    private Long couponId;

    /**
     * 达成条件
     * <p>多买多送、拒就赔等权益需要达成条件</p>
     */
    private List<PlusReachConditionObject> plusReachCondition;

    /**
     * 扩展信息
     */
    private PlusProgramProfitsCouponExtObject ext;
}
