package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.dto.req.profits.QueryPlusProductReq;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProductInfoPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员商品池表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/19 16:18
 */
@Mapper
public interface IPlusProductInfoMapper {

    /**
     * 查询会员0元商品池列表
     */
    List<PlusProductInfoPo> selectPlusProductList(QueryPlusProductReq req);

    /**
     * 查询会员0元商品数量
     */
    int selectPlusProductCount(QueryPlusProductReq req);

    /**
     * 根据sku查询商品池中的商品
     */
    PlusProductInfoPo selectProductBySku(@Param("sku") String sku);

    List<PlusProductInfoPo> selectProductBySkus(@Param("skus") List<String> skus);

    /**
     * 根据id查询商品池中的商品
     */
    PlusProductInfoPo selectProductById(@Param("id") Integer id);

    /**
     * 新增会员0元商品
     */
    Integer savePlusProduct(PlusProductInfoPo po);

    /**
     * 编辑会员0元商品
     */
    Integer updatePlusProduct(PlusProductInfoPo po);

    /**
     * 会员0元商品上架/下架
     */
    Integer updatePlusProductState(PlusProductInfoPo po);

    /**
     * 根据主键删除会员商品
     */
    Integer delById(@Param("id") Integer id);
}
