package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 方案续费信息配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2021/12/21 11:45
 */
@Mapper
public interface IPlusRenewRelevanceMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusRenewRelevance(PlusRenewRelevancePo plusRenewRelevance);

    /**
     * 更新
     */
    Integer updatePlusRenewRelevance(
            @Param("plusRenewRelevance") PlusRenewRelevancePo plusRenewRelevance);

    /**
     * Load查询
     */
    PlusRenewRelevancePo loadPlusRenewRelevance(@Param("id") Integer id);

    /**
     * 通过方案ID查询续费信息
     */
    PlusRenewRelevancePo getRenewInfoByProgramId(Integer programId);
}
