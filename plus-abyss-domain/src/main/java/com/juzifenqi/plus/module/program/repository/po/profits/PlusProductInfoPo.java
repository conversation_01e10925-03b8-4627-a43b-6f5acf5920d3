package com.juzifenqi.plus.module.program.repository.po.profits;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员商品池表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/19 16:17
 */
@Data
public class PlusProductInfoPo {


    /**
     * id
     */
    private int id;

    /**
     * 商品id
     */
    private int productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 会员商品上架状态 0_未上架 1_已上架
     */
    private int saleState;

    /**
     * 采购价
     */
    private BigDecimal purPrice;

    /**
     *  扣减保护价
     */
    private BigDecimal protPrice;

    /**
     * 商品库存
     */
    private Integer stock;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

}
