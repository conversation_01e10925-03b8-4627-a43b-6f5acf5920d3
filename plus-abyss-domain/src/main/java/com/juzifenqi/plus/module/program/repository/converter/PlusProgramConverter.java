package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderModelSnapshotEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusLiftAmountEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.entity.RdzxEquitySwitchEntity;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.model.event.RdzxEquityConfigInfoEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCashbackObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCouponExtObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCouponObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCreditObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsPackageObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsVirtualGoodsObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsVirtualObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusReachConditionObject;
import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramExtendPo;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramPo;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

import org.mapstruct.*;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

@Mapper
public interface PlusProgramConverter {

    PlusProgramConverter instance = Mappers.getMapper(PlusProgramConverter.class);

    PlusLiftAmountEntity toPlusLiftAmountEntity(PlusLiftAmountPo plusLiftAmountPo);

    @Mappings({@Mapping(target = "id", source = "plusProgramPo.id"),
            @Mapping(target = "configId", source = "plusProgramPo.configId"),
            @Mapping(target = "programName", source = "plusProgramPo.name"),
            @Mapping(target = "programId", source = "plusProgramPo.id"),
            @Mapping(target = "createTime", source = "plusProgramPo.createTime"),
            @Mapping(target = "createUserId", source = "plusProgramPo.createUserId"),
            @Mapping(target = "createUserName", source = "plusProgramPo.createUserName"),
            @Mapping(target = "updateTime", source = "plusProgramPo.updateTime"),
            @Mapping(target = "updateUserId", source = "plusProgramPo.updateUserId"),
            @Mapping(target = "updateUserName", source = "plusProgramPo.updateUserName")})
    PlusProgramEntity toPlusProgramEntity(PlusProgramPo plusProgramPo,
            PlusProgramExtendPo plusProgramExtendPo,
            List<PlusProgramProfitsPackageObject> profitsPackageList);

    /**
     * 六类券的权益包
     */
    @Mappings({@Mapping(target = "profitsId", source = "id")})
    List<PlusProgramProfitsCouponObject> toCoupons(List<PlusCouponIndexPo> plusCouponIndex);


    /**
     * 多买多送权益包
     */
    @Mappings({@Mapping(target = "profitsId", source = "po.id"),
            @Mapping(target = "plusReachCondition", source = "plusReachCondition"),
            @Mapping(target = "ext.orderNum", source = "po.ordersNumber"),
            @Mapping(target = "ext.dmdsTaskId", source = "po.id")})
    PlusProgramProfitsCouponObject toDmdsCoupon(PlusProgramTaskPo po,
            List<PlusReachConditionObject> plusReachCondition);

    /**
     * 拒就赔权益包
     */
    @Mappings({@Mapping(target = "profitsId", source = "po.id"),
            @Mapping(target = "plusReachCondition", source = "plusReachCondition"),
            @Mapping(target = "ext.rejectTaskId", source = "po.id"),
            @Mapping(target = "ext.rejectType", source = "po.type")})
    PlusProgramProfitsCouponObject toJjpCoupon(PlusProgramRejectionPo po,
            List<PlusReachConditionObject> plusReachCondition);

    /**
     * 息费折扣权益包
     */
    @Mappings({@Mapping(target = "profitsId", source = "po.id")})
    PlusProgramProfitsCouponObject toXfzkCoupon(PlusProgramXFZKPo po);

    PlusProgramProfitsCouponExtObject toDmdsCouponExt(Integer orderNum, Integer dmdsTaskId);

    /**
     * 提额的权益包
     */
    @Mappings({@Mapping(target = "profitsId", source = "id")})
    PlusProgramProfitsCreditObject toLiftAmount(PlusLiftAmountPo plusLiftAmountPo);

    @Mappings({@Mapping(target = "configId", source = "plusProgramPo.configId"),
            @Mapping(target = "programName", source = "plusProgramPo.name"),
            @Mapping(target = "programId", source = "plusProgramPo.id")})
    PlusProgramEntity toSimpleProgramEntity(PlusProgramPo plusProgramPo);

    /**
     * 返现类的权益包
     */
    @Mappings({@Mapping(target = "modelId", source = "modelId"),
            @Mapping(target = "packageId", source = "modelId"),
            @Mapping(target = "cashbackType", source = "cashbackType"),
            @Mapping(target = "orderNum", source = "orderNum")})
    PlusProgramProfitsCashbackObject toCashback(Integer programId, BigDecimal cashbackAmount,
            Integer modelId, Integer profitsId, List<PlusReachConditionObject> plusReachCondition,
            Integer orderNum, Integer cashbackType);

    PlusProgramVirtualEntity toPlusProgramVirtualEntity(PlusProgramVirtualPo po);

    @Mappings({@Mapping(target = "programName", source = "name"),
            @Mapping(target = "programId", source = "id")})
    List<PlusProgramEntity> toPlusProgramEntityList(List<PlusProgramPo> list);

    /**
     * 区间还款权益包
     */
    @Mappings({@Mapping(target = "ext.loanOrderSn", source = "loanOrderSn")})
    PlusProgramProfitsCouponObject toQjhk(Integer programId, Integer couponId, String loanOrderSn);

    /**
     * 生日关怀桔豆权益包
     */
    PlusProgramProfitsVirtualObject toSrghJd(Integer programId, BigDecimal virtualNum);

    /**
     * 权益0元发放
     */
    PlusProgramProfitsVirtualGoodsObject toLyff(Integer programId, String programVirtualId);

    @Mappings({@Mapping(target = "createUserId", source = "event.optUserId"),
            @Mapping(target = "createUserName", source = "event.optUserName"),
            @Mapping(target = "payTypes", ignore = true)
    })
    PlusProgramPo toPlusProgramPo(SavePlusProgramEvent event);


    @Mappings({@Mapping(target = "programId", source = "programId"),
            @Mapping(target = "isRenew", source = "event.isRenew"),
            @Mapping(target = "renewPrice", source = "event.renewPrice"),
            @Mapping(target = "renewOpenTime", source = "event.renewOpenTime"),
            @Mapping(target = "isShowFrame", source = "event.isShowFrame"),
            @Mapping(target = "frameImage", source = "event.frameImage"),
            @Mapping(target = "frameType", source = "event.frameType"),
            @Mapping(target = "interval", source = "event.interval"),
            @Mapping(target = "alternateProgram", source = "event.alternateProgram"),
            @Mapping(target = "id", ignore = true), @Mapping(target = "isRenewId", ignore = true)})
    PlusRenewRelevancePo toPlusRenewRelevancePo(SavePlusProgramEvent event, Integer programId);

    @Mappings({@Mapping(target = "programId", source = "programId")})
    PlusProModelPo toPlusProModelPo(PlusProModelEntity entity, Integer programId);

    RdzxEquitySwitchEntity toRdzxEquitySwitchEntity(PlusProgramExtendPo po);

    @Mappings({@Mapping(target = "channelId", source = "event.channel"),
            @Mapping(target = "programId", source = "programId")})
    PlusProgramExtendPo toPlusProgramExtendPo(SavePlusProgramEvent event, Integer programId);

    @Mapping(target = "id", ignore = true)
    PlusProgramVirtualTypePo toPlusProgramVirtualTypePo(PlusProgramVirtualTypePo po);
    /**
     * 还款返现
     */
    @Mappings({@Mapping(target = "periods", source = "loanPeriod")})
    RdzxEquityConfigInfoEvent toRdzxEquityConfigInfoEvent(BigDecimal loanAmount,
            Integer loanPeriod);

    @Mappings({
            @Mapping(source = "sortTime", target = "updateTime")
    })
    PlusProModelEntity toPlusProModelEntity(PlusOrderModelSnapshotEntity entity);
    /**
     * 权益快照转化
     */
    List<PlusProModelEntity> toPlusProModelEntityList(List<PlusOrderModelSnapshotEntity> entityList);

    @AfterMapping
    default void ofPlusProgramPo(@MappingTarget PlusProgramPo plusProgramPo,SavePlusProgramEvent event) {
        if (null != event && !CollectionUtils.isEmpty(event.getPayTypes())) {
            String payTypes = event.getPayTypes().stream()
                    .map(String::valueOf)
                    .collect(Collectors.joining(","));
            plusProgramPo.setPayTypes(payTypes);
        }
    }
}
