package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.price.PlusDefaultProgramPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusDifferenceProgramPriceEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDifferenceProgramPriceEvent;
import com.juzifenqi.plus.module.program.repository.po.price.PlusDefaultProgramPricePo;
import com.juzifenqi.plus.module.program.repository.po.price.PlusDifferenceProgramPricePo;
import com.juzifenqi.plus.module.program.repository.po.price.PlusProgramPricePo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 定价配置转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11 10:44
 */
@Mapper
public interface IPlusPriceConverter {

    IPlusPriceConverter instance = Mappers.getMapper(IPlusPriceConverter.class);

    @Mappings({@Mapping(target = "createUserId", source = "event.optUserId"),
            @Mapping(target = "createUser", source = "event.optUser")})
    PlusProgramPricePo toPlusProgramPrice(SaveDiffPriceEvent event, Integer priceType);

    List<PlusDifferenceProgramPricePo> toPlusProgramPriceList(
            List<SaveDifferenceProgramPriceEvent> list);

    List<PlusDifferenceProgramPriceEntity> toPlusProgramPriceEntityList(
            List<PlusDifferenceProgramPricePo> list);

    List<PlusDefaultProgramPriceEntity> toPlusDefaultProgramPriceList(
            List<PlusDefaultProgramPricePo> list);
}
