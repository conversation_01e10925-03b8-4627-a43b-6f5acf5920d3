package com.juzifenqi.plus.module.program.repository.po.price;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 差异化定价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 16:09
 */
@Data
public class PlusDifferenceProgramPricePo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人
     */
    private String updateUser;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 主表id
     */
    private Integer priceId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 提额等级
     */
    private String grade;

    /**
     * 剩余可借最小金额
     */
    private BigDecimal availableMinPrice;

    /**
     * 剩余可借最大金额
     */
    private BigDecimal availableMaxPrice;

    /**
     * 会员可提额最小金额
     */
    private BigDecimal promoteMinPrice;

    /**
     * 会员可提额最大金额
     */
    private BigDecimal promoteMaxPrice;

    /**
     * 提额后可借最小金额
     */
    private BigDecimal promoteAvailableMinPrice;

    /**
     * 提额后可借最大金额
     */
    private BigDecimal promoteAvailableMaxPrice;

    /**
     * 借款最小金额
     */
    private BigDecimal loanMinPrice;

    /**
     * 借款最大金额
     */
    private BigDecimal loanMaxPrice;

    /**
     * 期数
     */
    private Integer periods;

    /**
     * 用户等级
     */
    private String userLevel;

    /**
     * 借款实际提升额度最小值
     */
    private BigDecimal realityQuotaMinPrice;

    /**
     * 借款实际提升额度最大值
     */
    private BigDecimal realityQuotaMaxPrice;

    /**
     * 是否重提客群用户
     */
    private Boolean resubmitUser;

    /**
     * 借款RFM模型最小分
     */
    private BigDecimal loanRfmMinScore;

    /**
     * 借款RFM模型最大分
     */
    private BigDecimal loanRfmMaxScore;

    /**
     * 会员RFM模型最小分
     */
    private BigDecimal plusRfmMinScore;

    /**
     * 会员RFM模型最大分
     */
    private BigDecimal plusRfmMaxScore;

    /**
     * 营销触达最小分
     */
    private BigDecimal marketReachMinScore;

    /**
     * 营销触达最大分
     */
    private BigDecimal marketReachMaxScore;

    /**
     * 用户尾号-最小
     */
    private Integer userLastMinNumber;

    /**
     * 用户尾号-最大
     */
    private Integer userLastMaxNumber;

    /**
     * 是否会员
     */
    private Boolean memberUser;

}
