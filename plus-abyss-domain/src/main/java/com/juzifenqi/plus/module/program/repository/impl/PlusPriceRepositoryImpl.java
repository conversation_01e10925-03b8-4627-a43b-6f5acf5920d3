package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.magic.bean.enums.BizSourceEnum;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.admin.program.PriceQueryReq;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.program.PriceTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.program.model.contract.IPlusPriceRepository;
import com.juzifenqi.plus.module.program.model.entity.price.PlusDefaultProgramPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusDifferenceProgramPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent.DefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDifferenceProgramPriceEvent;
import com.juzifenqi.plus.module.program.repository.converter.IPlusPriceConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusDefaultProgramPriceMapper;
import com.juzifenqi.plus.module.program.repository.dao.IPlusDifferenceProgramMapper;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProgramPriceMapper;
import com.juzifenqi.plus.module.program.repository.po.price.PlusDefaultProgramPricePo;
import com.juzifenqi.plus.module.program.repository.po.price.PlusDifferenceProgramPricePo;
import com.juzifenqi.plus.module.program.repository.po.price.PlusProgramPricePo;
import com.juzifenqi.plus.utils.RedisUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 定价配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11 10:42
 */
@Slf4j
@Repository
public class PlusPriceRepositoryImpl implements IPlusPriceRepository {

    private final IPlusPriceConverter converter = IPlusPriceConverter.instance;

    @Autowired
    private IPlusProgramPriceMapper plusProgramPriceMapper;
    @Autowired
    private IPlusDefaultProgramPriceMapper defaultProgramPriceMapper;
    @Autowired
    private IPlusDifferenceProgramMapper differenceProgramMapper;
    @Autowired
    private RedisUtils redisUtils;

    @Override
    public boolean checkByChannelAndConfigId(Integer channelId, Integer configId,
                                             Integer bizSource, Integer priceType) {
        return plusProgramPriceMapper.selectByChannelAndConfigId(channelId, configId, bizSource, priceType)
                != null;
    }

    @Override
    public Integer saveDiffPrice(SaveDiffPriceEvent event) {
        PlusProgramPricePo programPrice = converter.toPlusProgramPrice(event,
                PriceTypeEnum.DIFF.getCode());
        plusProgramPriceMapper.insert(programPrice);
        List<PlusDifferenceProgramPricePo> list = converter.toPlusProgramPriceList(
                event.getDiffList());
        list.forEach(e -> {
            e.setPriceId(programPrice.getId());
            e.setCreateUser(event.getOptUser());
            e.setCreateUserId(event.getOptUserId());
        });
        differenceProgramMapper.batchInsert(list);
        cacheDiffPrice(event, programPrice);
        return programPrice.getId();
    }

    @Override
    public PlusProgramPricePo getById(Integer id) {
        return plusProgramPriceMapper.selectById(id);
    }

    @Override
    public void updateDiffPrice(SaveDiffPriceEvent event) {
        PlusProgramPricePo plusProgramPrice = plusProgramPriceMapper.selectById(event.getId());
        if (plusProgramPrice == null) {
            throw new PlusAbyssException("无效的id");
        }
        PlusProgramPricePo programPrice = new PlusProgramPricePo();
        programPrice.setUpdateUser(event.getOptUser());
        programPrice.setUpdateUserId(event.getOptUserId());
        programPrice.setGrade(event.getGrade());
        programPrice.setId(event.getId());
        plusProgramPriceMapper.update(programPrice);
        // 删除再新增
        differenceProgramMapper.deleteByPriceId(event.getId());
        List<PlusDifferenceProgramPricePo> list = converter.toPlusProgramPriceList(
                event.getDiffList());
        list.forEach(e -> {
            e.setPriceId(event.getId());
            e.setCreateUser(event.getOptUser());
            e.setCreateUserId(event.getOptUserId());
        });
        differenceProgramMapper.batchInsert(list);
        event.setBizSource(plusProgramPrice.getBizSource());
        cacheDiffPrice(event, programPrice);
    }

    @Override
    public List<PlusDifferenceProgramPriceEntity> getDiffPrices(Integer priceId) {
        List<PlusDifferenceProgramPricePo> list = differenceProgramMapper.selectByPriceId(priceId);
        return converter.toPlusProgramPriceEntityList(list);
    }

    @Override
    public Integer saveDefaultPrice(SaveDefaultPriceEvent event) {
        DefaultPriceEvent defaultPrice = event.getPriceList().get(0);
        // 唯一判断
        boolean exist = this.checkByChannelAndConfigId(event.getChannelId(),
                defaultPrice.getConfigId(), event.getBizSource(), PriceTypeEnum.DEFAULT.getCode());
        if (exist) {
            throw new PlusAbyssException("此渠道已存在对应会员类型Id：" + defaultPrice.getConfigId()
                    + "配置，请勿重复添加");
        }
        PlusProgramPricePo programPrice = new PlusProgramPricePo();
        programPrice.setPriceType(PriceTypeEnum.DEFAULT.getCode());
        programPrice.setConfigId(defaultPrice.getConfigId());
        programPrice.setChannelId(event.getChannelId());
        programPrice.setChannelName(event.getChannelName());
        programPrice.setCreateUser(event.getOptUser());
        programPrice.setCreateUserId(event.getOptUserId());
        programPrice.setBizSource(event.getBizSource());
        plusProgramPriceMapper.insert(programPrice);

        PlusDefaultProgramPricePo defaultPo = new PlusDefaultProgramPricePo();
        defaultPo.setProgramId(defaultPrice.getDefaultPrice().getProgramId());
        defaultPo.setPriceId(programPrice.getId());
        defaultPo.setCreateUser(event.getOptUser());
        defaultPo.setCreateUserId(event.getOptUserId());
        defaultProgramPriceMapper.insert(defaultPo);
        cacheDefaultPrice(event);
        return programPrice.getId();
    }

    @Override
    public void editDefaultPrice(SaveDefaultPriceEvent event) {
        PlusProgramPricePo plusProgramPrice = plusProgramPriceMapper.selectById(event.getId());
        if (plusProgramPrice == null) {
            throw new PlusAbyssException("无效的id");
        }
        plusProgramPrice.setUpdateUserId(event.getOptUserId());
        plusProgramPrice.setUpdateUser(event.getOptUser());
        plusProgramPriceMapper.update(plusProgramPrice);
        // 先删除
        defaultProgramPriceMapper.deleteByPriceId(event.getId());
        // 默认方案价编辑时list里只有1个元素，所以不需要批量插入
        PlusDefaultProgramPricePo defaultPrice = new PlusDefaultProgramPricePo();
        defaultPrice.setPriceId(event.getId());
        defaultPrice.setProgramId(event.getPriceList().get(0).getDefaultPrice().getProgramId());
        defaultPrice.setCreateUserId(event.getOptUserId());
        defaultPrice.setCreateUser(event.getOptUser());
        defaultProgramPriceMapper.insert(defaultPrice);
        // 缓存
        event.setChannelId(plusProgramPrice.getChannelId());
        event.setBizSource(plusProgramPrice.getBizSource());
        cacheDefaultPrice(event);
    }

    @Override
    public List<PlusDefaultProgramPriceEntity> getDefaultPrices(Integer priceId) {
        List<PlusDefaultProgramPricePo> list = defaultProgramPriceMapper.selectByPriceId(priceId);
        return converter.toPlusDefaultProgramPriceList(list);
    }

    @Override
    public List<PlusProgramPriceListEntity> getPriceList(PriceQueryReq req) {
        if (PriceTypeEnum.DEFAULT.getCode().equals(req.getPriceType())) {
            return plusProgramPriceMapper.selectDefaultList(req);
        }
        return plusProgramPriceMapper.selectDiffList(req);
    }

    @Override
    public Integer countPriceList(PriceQueryReq req) {
        return plusProgramPriceMapper.countList(req);
    }

    /**
     * 缓存差异化定价配置
     */
    private void cacheDiffPrice(SaveDiffPriceEvent dto, PlusProgramPricePo programPrice) {
        String redisKey = String.format(RedisConstantPrefix.DIFF_PRICE_CONFIG_KEY,
                dto.getChannelId(), dto.getConfigId());
        if (dto.getBizSource() != null && dto.getBizSource() > 1) {
            redisKey = String.format(RedisConstantPrefix.DIFF_PRICE_CONFIG_KEY_NEW,
                    dto.getChannelId(), dto.getConfigId(), dto.getBizSource());
        }
        log.info("开始缓存差异化定价配置,redisKey：{}", redisKey);
        redisUtils.delete(redisKey);
        log.info("删除缓存差异化定价配置成功,redisKey：{}", redisKey);
        List<SaveDifferenceProgramPriceEvent> diffList = dto.getDiffList();
        redisUtils.lRightPushAll(redisKey,
                diffList.stream().map(JSONObject::toJSONString).collect(Collectors.toList()));
        log.info("缓存差异化定价配置成功,redisKey：{}", redisKey);
        if (JuziPlusEnum.NEW_JUXP_CARD.getCode() == dto.getConfigId()) {
            String priceKey = String.format(RedisConstantPrefix.PRICE_GRADE_CONFIG_KEY,
                    dto.getChannelId(), dto.getConfigId());
            if (dto.getBizSource() != null && dto.getBizSource() > 1) {
                priceKey = String.format(RedisConstantPrefix.PRICE_GRADE_CONFIG_KEY_NEW,
                        dto.getChannelId(), dto.getConfigId(), dto.getBizSource());
            }
            redisUtils.delete(priceKey);
            log.info("缓存桔享卡差异化定价提额等级顺序缓存：{}", priceKey);
            redisUtils.set(priceKey, String.valueOf(programPrice.getGrade()));
        }
    }

    /**
     * 缓存默认方案价配置
     */
    private void cacheDefaultPrice(SaveDefaultPriceEvent dto) {
        DefaultPriceEvent price = dto.getPriceList().get(0);
        String redisKey = String.format(RedisConstantPrefix.DEFAULT_PRICE_CONFIG_KEY,
                dto.getChannelId(), price.getConfigId());
        if (dto.getBizSource() != null && dto.getBizSource() > BizSourceEnum.YKD.getCode()) {
            redisKey = String.format(RedisConstantPrefix.DEFAULT_PRICE_CONFIG_KEY_NEW,
                    dto.getChannelId(), price.getConfigId(), dto.getBizSource());
        }
        log.info("开始默认方案价配置,redisKey：{}", redisKey);
        String body = JSONObject.toJSONString(price.getDefaultPrice());
        redisUtils.set(redisKey, body);
        log.info("缓存默认方案价配置成功,redisKey：{}，value：{}", redisKey, body);
    }
}
