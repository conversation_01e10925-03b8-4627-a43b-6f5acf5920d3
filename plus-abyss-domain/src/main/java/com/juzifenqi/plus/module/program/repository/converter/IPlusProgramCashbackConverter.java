package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 权益返现转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/5/13 10:53
 */
@Mapper
public interface IPlusProgramCashbackConverter {

    IPlusProgramCashbackConverter instance = Mappers.getMapper(IPlusProgramCashbackConverter.class);

    List<PlusProgramCashbackEntity> toList(List<PlusProgramCashbackPo> list);

    List<PlusProgramCashbackEntity> toPlusProgramCashbackEntityList(
            List<PlusProgramCashbackPo> list);
}
