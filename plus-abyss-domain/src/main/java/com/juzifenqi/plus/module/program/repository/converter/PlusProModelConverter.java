package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 15:41
 */
@Mapper
public interface PlusProModelConverter {

    PlusProModelConverter instance = Mappers.getMapper(PlusProModelConverter.class);

    List<PlusProModelPo> toPlusProModelPoList(List<CreatePlusProModelEvent> list);

    PlusProModelPo toPlusProModelPo(CreatePlusProModelEvent event);

    List<PlusProModelEntity> toPlusProModelEntityList(List<PlusProModelPo> poList);

    PlusProModelEntity toPlusProModelEntity(PlusProModelPo po);
}
