package com.juzifenqi.plus.module.program.application.impl;

import com.juzifenqi.plus.module.program.application.IPlusChannelManageApplication;
import com.juzifenqi.plus.module.program.application.validator.PlusChannelManagerValidator;
import com.juzifenqi.plus.module.program.model.IPlusChannelManagerModel;
import com.juzifenqi.plus.module.program.model.event.CreateChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EditChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EnableChannelManageEvent;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 渠道管理配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 20:02
 */
@Slf4j
@Service
public class PlusChannelManageApplicationImpl implements IPlusChannelManageApplication {

    @Autowired
    private IPlusChannelManagerModel    managerModel;
    @Autowired
    private PlusChannelManagerValidator validator;

    @Override
    public void createChannelManager(CreateChannelManagerEvent event) {
        validator.checkCreateParam(event);
        managerModel.createChannelManager(event);
    }

    @Override
    public void editChannelManager(EditChannelManagerEvent event) {
        validator.checkEditeParam(event);
        managerModel.editChannelManager(event);
    }

    @Override
    public void enableChannelManage(EnableChannelManageEvent event) {
        validator.checkEnableParam(event);
        managerModel.enableChannelManager(event);
    }
}
