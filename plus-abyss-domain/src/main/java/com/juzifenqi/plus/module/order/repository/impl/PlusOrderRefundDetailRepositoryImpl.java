package com.juzifenqi.plus.module.order.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRefundDetailRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderRefundDetailRepositoryConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderRefundDetailMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
@Slf4j
public class PlusOrderRefundDetailRepositoryImpl implements IPlusOrderRefundDetailRepository {

    private final IPlusOrderRefundDetailRepositoryConverter convert = IPlusOrderRefundDetailRepositoryConverter.instance;

    @Autowired
    private IPlusOrderRefundDetailMapper plusOrderRefundDetailMapper;


    @Override
    public List<PlusOrderRefundDetailEntity> getRefundDetailByRefundInfoId(Long refundInfoId) {
        if (refundInfoId == null) {
            return null;
        }
        log.info("getRefundDetailByRefundInfoId  req:{}", refundInfoId);
        List<PlusOrderRefundDetailEntity> list = plusOrderRefundDetailMapper.getDetailsByInfoId(refundInfoId);
        log.info("getRefundDetailByRefundInfoId  resp:{}", JSON.toJSONString(list));
        return list;
    }
}
