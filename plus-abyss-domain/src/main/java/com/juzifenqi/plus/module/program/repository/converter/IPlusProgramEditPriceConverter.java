package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramEditPricePo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 方案改价转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 15:09
 */
@Mapper
public interface IPlusProgramEditPriceConverter {

    IPlusProgramEditPriceConverter instance = Mappers.getMapper(
            IPlusProgramEditPriceConverter.class);

    List<PlusProgramEditPriceEntity> toPlusProgramEditPriceEntityList(
            List<PlusProgramEditPricePo> list);

    PlusProgramEditPricePo toPlusProgramEditPricePo(CreateProgramEditPriceEvent event);
}
