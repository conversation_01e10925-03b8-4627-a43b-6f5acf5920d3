package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.dto.req.profits.QueryProgramCouponReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusCouponIndexEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import java.util.List;

/**
 * 方案优惠券配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 15:22
 */
public interface IPlusCouponIndexRepository {

    /**
     * 按方案id和类型获取
     */
    List<PlusCouponIndexEntity> getByProgramId(Integer programId, Integer type);

    /**
     * 批量保存-后台
     */
    void batchSaveCouponIndex(List<CreateProgramCouponEvent> list);

    /**
     * 获取权益下配置的优惠券信息
     */
    PageResultEntity<PlusProgramCouponEntity> getPlusProgramCouponList(QueryProgramCouponReq event);

    /**
     * 删除优惠券配置-后台
     */
    void delCouponIndex(CreateProgramCouponEvent event);
}
