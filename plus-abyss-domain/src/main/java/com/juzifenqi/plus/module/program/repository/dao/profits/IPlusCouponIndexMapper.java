package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Filename: plusCouponIndexWriteDao.java
 * @Version: 1.0
 * @Author:**
 * @Email: **@**.com
 * @Date: 2020-01-09 15:55:15
 * @Description: 会员权益优惠券
 */
@Mapper
public interface IPlusCouponIndexMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusCouponIndex(PlusCouponIndexPo plusCouponIndex);

    /**
     * 新增
     */
    Integer insertPlusCouponIndex(PlusCouponIndexPo plusCouponIndex);

    /**
     * 删除
     */
    Integer deletePlusCouponIndex(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusCouponIndex(PlusCouponIndexPo plusCouponIndex);

    /**
     * Load查询
     */
    PlusCouponIndexPo loadPlusCouponIndex(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusCouponIndexPo> pageList(@Param("offset") Integer offset,
            @Param("pagesize") Integer pagesize);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("offset") Integer offset, @Param("pagesize") Integer pagesize);


    /**
     * 根据方案id、类型获取会员权益优惠券
     */
    List<PlusCouponIndexPo> getCouponByProgramIdAndType(@Param("programId") Integer programId,
            @Param("type") Integer type);

    /**
     * 根据programId获取会员权益优惠券
     *
     * @param programId 方案ID
     * @param type 类型 1-开卡礼， 2-月享
     */
    List<PlusCouponIndexPo> getPlusCouponIndexByProgramId(@Param("programId") Integer programId,
            @Param("type") Integer type, @Param("modelId") Integer modelId);


    /**
     * 根据programId和优惠券类型获取会员权益优惠券
     */
    List<PlusCouponIndexPo> getCouponByProgramIdAndCouponType(@Param("programId") Integer programId,
            @Param("couponTypeList") List<Integer> couponTypeList);

    /**
     * 根据programId获取会员权益优惠券
     */
    List<PlusCouponIndexPo> getCouponByProgramId(@Param("programId") Integer programId);

    /**
     * 批量保存会员权益优惠券
     */
    Boolean batchInsertPlusCouponIndex(@Param("list") List<PlusCouponIndexPo> list);

    List<PlusCouponIndexPo> getPlusCouponIndexPage(Integer programId, Integer type);

    /**
     * 分页查询Count
     */
    Integer pageListCountByType(Integer programId, Integer type);

    Integer batchInsertPlusCouponIndexVo(@Param("list") List<PlusCouponIndexPo> list);

    List<Integer> getByCouponIds(@Param("list") List<Integer> couponIds,
            @Param("programId") Integer programId, @Param("type") Integer type);
}
