package com.juzifenqi.plus.module.program.application.impl;

import static com.juzifenqi.plus.constants.RedisConstantPrefix.PROGRAM_PRODUCT_DETAIL;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.dto.req.PlusVirtualProductReq;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.contract.entity.ProductCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualGoodsCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.event.ProductCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.VirtualGoodsCheckEvent;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.program.application.IPlusProductApplication;
import com.juzifenqi.plus.module.program.application.converter.IPlusProductApplicationConverter;
import com.juzifenqi.plus.module.program.model.IPlusProductModel;
import com.juzifenqi.plus.module.program.model.contract.entity.LyffVirtualDetailEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.LyffVirtualInfoEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProductDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusVirtualProductEntity;
import com.juzifenqi.plus.module.program.model.event.LyffVirtualDetailEvent;
import com.juzifenqi.plus.module.program.model.event.LyffVirtualInfoEvent;
import com.juzifenqi.plus.module.program.model.event.PlusProductDetailEvent;
import com.juzifenqi.plus.utils.RedisUtils;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.concurrent.TimeUnit;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 会员商品application
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/4 17:30
 */
@Slf4j
@Service
public class PlusProductApplicationImpl implements IPlusProductApplication {

    private final IPlusProductApplicationConverter converter = IPlusProductApplicationConverter.instance;

    @Autowired
    private IPlusProductModel     plusProductModel;
    @Autowired
    private IPlusOrderApplication orderApplication;
    @Resource
    private RedisUtils            redisUtils;

    @Override
    public PlusProductDetailEntity getPlusProductDetail(PlusProductDetailEvent event) {
        PlusProductDetailEntity productDetail = plusProductModel.getProductDetail(event);
        // 校验是否能购买此商品
        ProductCheckEvent checkEvent = converter.toProductCheckEvent(event);
        ProductCheckResultEntity checkResult = orderApplication.canBuyProduct(checkEvent);
        log.info("会员商品详情页校验是否能购买结果：{}", JSON.toJSONString(checkResult));
        // 赋值按钮状态
        productDetail.setBuyButtonState(checkResult.getBuyButtonState());
        // 计算售价
        if (checkResult.getProductNewPo() != null
                && checkResult.getProductNewPo().getDiscountRate() != null) {
            productDetail.setSalePrice(productDetail.getMalMobilePrice()
                    .multiply(checkResult.getProductNewPo().getDiscountRate())
                    .setScale(2, RoundingMode.DOWN));
        } else {
            // 未获取到商品配置则默认商品售价
            productDetail.setSalePrice(productDetail.getMalMobilePrice());
        }
        return productDetail;
    }

    /**
     * 查看权益0元发放虚拟商品信息
     */
    @Override
    public LyffVirtualInfoEntity getLyffVirtualInfo(LyffVirtualInfoEvent event) {
        return plusProductModel.getLyffVirtualInfo(event);
    }

    /**
     * 查看权益0元发放虚拟商品详情
     */
    @Override
    public LyffVirtualDetailEntity getLyffVirtualDetail(LyffVirtualDetailEvent event) {
        // 检验是否可以购买
        VirtualGoodsCheckEvent checkEvent = converter.toVirtualGoodsCheckEvent(event);
        VirtualGoodsCheckResultEntity checkResult = orderApplication.canBuyVirtualGoods(checkEvent);
        log.info("权益0元发放页校验是否能领取结果：{}", JSON.toJSONString(checkResult));
        // 未查询到商品信息
        if (checkResult.getProductId() == null || checkResult.getDiscountRate() == null) {
            throw new PlusAbyssException("未查询到商品详情");
        }
        // 查询商品信息
        PlusProductDetailEvent detailEvent = new PlusProductDetailEvent();
        detailEvent.setProductId(checkResult.getProductId());
        PlusProductDetailEntity productDetail = plusProductModel.getProductDetail(detailEvent);
        // 会员价 = 售价 * 会员折扣
        BigDecimal plusPrice = productDetail.getMalMobilePrice()
                .multiply(checkResult.getDiscountRate()).setScale(2, RoundingMode.DOWN);
        return converter.toLyffVirtualDetailEntity(checkResult, plusPrice, productDetail);
    }

    @Override
    public PlusVirtualProductEntity getVirtualProductDetail(PlusVirtualProductReq req) {
        checkParam(req);
        Integer userId = req.getUserId();
        PlusVirtualProductEntity entity = new PlusVirtualProductEntity();
        try {
            log.info("获取单条虚拟商品信息开始:userId={},sku={}", userId, req.getProductSku());
            String redisKey =
                    PROGRAM_PRODUCT_DETAIL + req.getProgramId() + "_" + req.getProductSku();
            if (redisUtils.hasKey(redisKey)) {
                entity = JSON.parseObject(redisUtils.get(redisKey), PlusVirtualProductEntity.class);
                log.info("缓存中获取单条虚拟商品,userId:{},sku:{},result:{}", userId,
                        req.getProductSku(), JSON.toJSONString(entity));
            } else {
                entity = plusProductModel.doGetProductDetail(req);
                //放入缓存
                redisUtils.setEx(redisKey, JSON.toJSONString(entity), 2, TimeUnit.MINUTES);
            }
            return entity;
        } catch (Exception e) {
            log.info("获取方案下关联虚拟商品信息异常programId={}", req.getProgramId(), e);
            return entity;
        }
    }

    /**
     * 校验参数是否齐全
     */
    private void checkParam(PlusVirtualProductReq req) {
        if (req.getUserId() == null || req.getProgramId() == null || StringUtils.isBlank(
                req.getProductSku()) || req.getChannelId() == null || req.getProductId() == null) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
    }
}
