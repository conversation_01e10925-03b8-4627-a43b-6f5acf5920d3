package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.dto.req.profits.QueryProgramCouponReq;
import com.juzifenqi.plus.enums.OptEventEnum;
import com.juzifenqi.plus.enums.PlusCouponTypeEnum;
import com.juzifenqi.plus.enums.PlusProgramLogNodeEnum;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.JuziCouponEntity;
import com.juzifenqi.plus.module.common.IExternalCouponRepository;
import com.juzifenqi.plus.module.common.IPlusProfitSystemLogRepository;
import com.juzifenqi.plus.module.common.IPlusProgramLogRepository;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.common.entity.PlusProfitSystemLogEntity;
import com.juzifenqi.plus.module.program.model.adapter.ProfitAdapter;
import com.juzifenqi.plus.module.program.model.contract.IPlusCouponIndexRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusCouponIndexEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusCouponIndexConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusCouponIndexMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * 方案优惠券配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 15:23
 */
@Slf4j
@Repository
public class PlusCouponIndexRepositoryImpl implements IPlusCouponIndexRepository {

    private final PlusCouponIndexConverter converter = PlusCouponIndexConverter.instance;

    @Autowired
    private IPlusCouponIndexMapper    indexMapper;
    @Autowired
    private IExternalCouponRepository couponRepository;
    @Autowired
    private IPlusProgramLogRepository programLogRepository;
    @Autowired
    private ProfitAdapter profitAdapter;
    @Autowired
    private IPlusProfitSystemLogRepository plusProfitSystemLogRepository;

    @Override
    public List<PlusCouponIndexEntity> getByProgramId(Integer programId, Integer type) {
        List<PlusCouponIndexPo> list = indexMapper.getCouponByProgramIdAndType(programId, type);
        return converter.toEntity(list);
    }

    @Override
    public void batchSaveCouponIndex(List<CreateProgramCouponEvent> list) {
        List<Integer> couponIds = list.stream().map(CreateProgramCouponEvent::getCouponId)
                .collect(Collectors.toList());
        List<Integer> existCouponIds = indexMapper.getByCouponIds(couponIds,
                list.get(0).getProgramId(), list.get(0).getType());
        // 操作人信息
        Integer optId = list.get(0).getOptId();
        String optName = list.get(0).getOptName();
        if (!CollectionUtils.isEmpty(existCouponIds)) {
            list.removeIf(e -> existCouponIds.contains(e.getCouponId()));
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<PlusCouponIndexPo> plusCouponIndexPos = converter.toPoList(list);
            indexMapper.batchInsertPlusCouponIndex(plusCouponIndexPos);
            // 保存日志
            List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(plusCouponIndexPos.stream().map(PlusCouponIndexPo::getId).collect(Collectors.toList()),
                    PlusCouponTypeEnum.getPlusModelType(list.get(0).getType()),
                    OptEventEnum.OPT_COUPON_ADD,
                    optId, optName);
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(logList)){
                plusProfitSystemLogRepository.saveBatch(logList);
            }
        }
    }

    @Override
    public PageResultEntity<PlusProgramCouponEntity> getPlusProgramCouponList(
            QueryProgramCouponReq event) {
        Integer count = indexMapper.pageListCountByType(event.getProgramId(), event.getType());
        List<PlusCouponIndexPo> list = indexMapper.getPlusCouponIndexPage(event.getProgramId(),
                event.getType());
        List<PlusProgramCouponEntity> couponEntityList = converter.toPlusProgramCouponEntity(list);
        setCouponInfo(couponEntityList);
        return new PageResultEntity<>(count, couponEntityList);
    }

    @Override
    public void delCouponIndex(CreateProgramCouponEvent event) {
        PlusCouponIndexPo plusCouponIndex = indexMapper.loadPlusCouponIndex(event.getId());
        if (plusCouponIndex != null) {
            String content = "删除了开卡礼/月享数据--" + JSON.toJSONString(plusCouponIndex);
            // 桔享Plus方案更新记录日志
            programLogRepository.saveLog(plusCouponIndex.getProgramId(), event.getOptId(),
                    event.getOptName(), PlusProgramLogNodeEnum.LOG_NODE_DEL_KKL_YX.getName(),
                    content);
        }
        indexMapper.deletePlusCouponIndex(event.getId());
    }

    /**
     * 设置优惠券信息
     */
    private void setCouponInfo(List<PlusProgramCouponEntity> list) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(coupon -> {
            JuziCouponEntity juziCoupon = couponRepository.getByCouponId(coupon.getCouponId());
            if (Objects.nonNull(juziCoupon)) {
                coupon.setCouponName(juziCoupon.getCouponName());
                coupon.setIndex(juziCoupon.getIndex());
            }
        });
    }
}
