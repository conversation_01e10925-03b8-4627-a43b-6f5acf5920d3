package com.juzifenqi.plus.module.order.model;

import com.juzifenqi.plus.enums.OrderPayActionEnum;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemAdminEntity;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;

import java.util.List;

/**
 * 订单分账model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/4 09:46
 */
public interface PlusOrderSeparateModel {

    /**
     * 支付回调分账处理
     */
    void payNotify(NewPayResultCallbackEntity entity);

    /**
     * 获取默认卡划扣申请
     */
    PlusOrderDeductResEntity getDeductApplyEventByDefaultCard(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan);

    /**
     * 获取指定卡划扣申请
     */
    PlusOrderDeductResEntity getDeductApplyEventByAppointCard(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan);

    /**
     * 查询订单清分明细
     */
    List<PlusOrderSeparateItemAdminEntity> getOrderSeparateItemList(String orderSn);

    /**
     * 同步划扣结果更新分账记录的状态和流水号
     */
    void deductUpdateOrderSeparate(PlusOrderDeductResEntity resEntity);

    /**
     * 获取会员订单支付分账信息
     */
    PlusOrderDeductResEntity getPlusOrderSeparateInfo(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan);

    /**
     * 通过订单号查询分账记录
     */
    List<PlusOrderSeparateEntity> getPlusOrderSeparate(String orderSn);

    /**
     * 获取第二期支付分账信息
     */
    PlusOrderDeductResEntity getSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity);

    /**
     * 获取分期支付分账信息
     */
    PlusOrderDeductResEntity getInstallmentPaymentSeparateInfo(PlusOrderEntity plusOrderEntity,
            PlusDeductEvent deductPlan, OrderPayActionEnum payAction);

    /**
     * 获取第一期支付分账信息
     */
    PlusOrderDeductResEntity getFirstPeriodSeparateInfo(PlusOrderEntity plusOrderEntity,
            PlusDeductEvent deductPlan);

}
