package com.juzifenqi.plus.module.program.repository.po;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 续费方案关联表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 14:50
 */
@Data
public class PlusRenewRelevancePo {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 是否可续费 1_是 0_否
     */
    private Integer isRenew;

    /**
     * 续费金额
     */
    private BigDecimal renewPrice;

    /**
     * 续费开放时间，会员有效期第n天
     */
    private Integer renewOpenTime;

    /**
     * 是否展示续费弹窗
     */
    private Integer isShowFrame;

    /**
     * 续费弹窗图片
     */
    private String frameImage;

    /**
     * 弹窗类型 0_每次打开展示 1_间隔
     */
    private Integer frameType;

    /**
     * 间隔 以小时为单位
     */
    private Integer interval;

    /**
     * 备选方案
     */
    private Integer alternateProgram;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 更新时间，BI抽数用
     */
    private Date biTime;

    /**
     * 续费表id
     */
    private Integer isRenewId;
}