package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusQjhkCouponConfigEntity;
import com.juzifenqi.plus.module.program.model.event.EditQjhkCouponConfigEvent;
import com.juzifenqi.plus.module.program.repository.po.PlusQjhkCouponConfigPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 区间还款配置转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9 17:45
 */
@Mapper
public interface IPlusQjhkCouponConfigConverter {

    IPlusQjhkCouponConfigConverter instance = Mappers.getMapper(
            IPlusQjhkCouponConfigConverter.class);

    List<PlusQjhkCouponConfigEntity> toEntityList(List<PlusQjhkCouponConfigPo> list);

    PlusQjhkCouponConfigPo toPo(PlusQjhkCouponConfigEntity entity);

    List<PlusQjhkCouponConfigEntity> toPlusQjhkCouponConfigEntityList(
            List<PlusQjhkCouponConfigPo> pageList);

    PlusQjhkCouponConfigEntity toEntity(PlusQjhkCouponConfigPo qjhkCouponConfigPo);

    PlusQjhkCouponConfigPo toPlusQjhkCouponConfigPo(EditQjhkCouponConfigEvent event);
}
