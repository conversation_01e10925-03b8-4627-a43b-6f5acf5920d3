package com.juzifenqi.plus.module.program.repository.entity;

import lombok.Data;

/**
 * 扩展信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/26 15:53
 */
@Data
public class PlusProgramProfitsCouponExtObject {

    /**
     * 订单笔数顺序
     */
    private Integer orderNum;

    /**
     * 多买多送任务id
     */
    private Integer dmdsTaskId;

    /**
     * 拒就赔任务id
     */
    private Integer rejectTaskId;

    /**
     * 拒就赔类型 1-订单拒绝 2-认证拒绝
     */
    private Integer rejectType;

    /**
     * 借款单号
     */
    private String loanOrderSn;
}
