package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeEntity;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualTypeEvent;
import com.juzifenqi.plus.module.program.model.event.EditLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import java.util.List;

/**
 * 方案下虚拟权益
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 15:31
 */
public interface IPlusProgramVirtualRepository {

    /**
     * 按方案id和sku获取虚拟权益
     */
    PlusProgramVirtualPo getByProgramAndSku(Integer programId, Integer modelId, String sku);

    /**
     * 获取虚拟权益分类信息
     */
    PlusProgramVirtualTypePo getVirtualTypeById(Integer id);

    /**
     * 获取虚拟权益分类信息
     */
    PlusProgramVirtualTypeEntity getTypeById(Integer id);

    /**
     * 根据方案获取分类
     */
    List<PlusProgramVirtualTypeEntity> getVirtualTypeByProgram(Integer programId, Integer modelId,
            Integer typeLevel);

    /**
     * 新增生活权益分类
     */
    void saveVirtualType(CreateVirtualTypeEvent event);

    /**
     * 编辑生活权益分类
     */
    void modifyVirtualTypeById(CreateVirtualTypeEvent event);

    /**
     * 删除生活权益分类
     */
    Integer removeVirtualTypeById(Integer id);

    /**
     * 删除分类下的权益数据
     * @return
     */
    void removeVirtualByType(Integer programId, Integer profitTypeId);

    /**
     * 查询分类排序是否重复
     */
    Integer checkVirtualTypeRankNumExists(CreateVirtualTypeEvent event);

    /**
     * 查询分类下的权益集合
     */
    List<PlusProgramVirtualEntity> getVirtualByType(Integer typeId);

    /**
     * 统计分类下的权益集合数量
     */
    Integer countVirtualByType(Integer typeId);

    /**
     * 根据id查询权益
     */
    PlusProgramVirtualEntity getVirtualById(CreateVirtualGoodsEvent event);

    /**
     * 根据id删除权益
     */
    Integer removeVirtualById(CreateVirtualGoodsEvent event);

    /**
     * 根据id编辑权益
     */
    void modifyVirtualById(CreateVirtualGoodsEvent event);

    /**
     * 根据sku集合批量添加权益
     */
    void saveVirtualBatch(List<PlusProgramVirtualPo> list);

    /**
     * 通过分类id查询下级分类
     *
     * @param profitTypeId 分类id
     */
    List<PlusProgramVirtualTypeEntity> getTypeByParentId(Integer profitTypeId);

    /**
     * 按sku获取虚拟权益
     */
    List<PlusProgramVirtualPo> getBySku(String sku);


    /**
     * 更新状态
     */
    int syncStatusBySku(PlusProgramVirtualPo po);

    /**
     * 根据方案id和权益id查询
     */
    List<PlusProgramVirtualEntity> getByProgramIdAndModelId(Integer programId, Integer modelId);

    /**
     * 分页查询
     */
    List<PlusProgramVirtualEntity> getPageVirtualList(Integer programId, Integer modelId,
            Integer start, Integer pageSize);

    /**
     * 分页总条数
     */
    Integer countList(Integer programId, Integer modelId);

    /**
     * 根据id查询权益
     */
    PlusProgramVirtualEntity getVirtualById(Integer id);

    /**
     * 根据id编辑权益
     */
    void modifyVirtualById(EditLyffVirtualGoodsEvent event);

    /**
     * 根据id删除权益
     */
    Integer removeVirtualById(Integer id);

    /**
     * 获取方案下的当前关联商品
     */
    public PlusProgramVirtualEntity getVirtualBySku(Integer programId, Integer modelId,
            String productSku);

    /**
     * 获取所有的权益数据
     * @param configIdList
     * @param modelId
     * @param productSku
     * @return
     */
    List<PlusProgramVirtualEntity> listBySkuAndConfig(List<Integer> configIdList, Integer modelId, String productSku);

    /**
     * 根据表ID批量更新图片地址
     * @param idList
     * @param imgUrl
     * @return
     */
    int updateImgUrlBatch(List<Integer> idList, String imgUrl);

}
