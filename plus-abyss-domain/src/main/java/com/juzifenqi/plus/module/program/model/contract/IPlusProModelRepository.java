package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 方案与权益关联信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/31 10:20
 */
public interface IPlusProModelRepository {

    /**
     * 获取会员类型的提额权益
     */
    List<PlusProModelEntity> getQuotaModelByMember(Integer userId, Integer channelId,
            Integer configId);

    /**
     * 方案的权益列表
     */
    List<PlusProModelEntity> getPlusProModelList(Integer programId);

    /**
     * 获取某个方案下是否配置了某个权益
     */
    int countProModelByProgramId(Integer programId, Integer modelId);

    /**
     * 获取方案关联的权益
     */
    PlusProModelEntity getPlusProModel(Integer programId, Integer modelId);

    /**
     * 保存/编辑方案与权益的关联信息
     */
    void editPlusProModel(CreatePlusProModelEvent event);

    /**
     * 权益列表排序修改
     */
    void editPlusProModelSort(List<CreatePlusProModelEvent> list);

    /**
     * 获取排序后的列表
     */
    List<PlusProModelEntity> getProModelByProgramIdOrderBySort(Integer programId);

    /**
     * 获取方案关联的权益信息列表
     */
    List<PlusProModelEntity> getByModelId(Integer modelId);

    /**
     * 保存方案与权益的关联关系
     */
    void saveProModel(Integer programId, List<Integer> modelIds);

    /**
     * 删除方案与权益的关联关系
     */
    void delProModel(Integer programId, List<Integer> modelIds);

    /**
     * 获取权益基础信息名称为空的数量
     */
    Integer getCountNullByProgramId(Integer programId);

}
