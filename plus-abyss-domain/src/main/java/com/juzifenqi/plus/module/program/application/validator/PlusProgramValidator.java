package com.juzifenqi.plus.module.program.application.validator;

import com.juzifenqi.plus.dto.req.admin.program.CopyProgramReq;
import com.juzifenqi.plus.enums.ChannelEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusProgramSendTypeEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleCheckProfitDataEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.program.model.IPlusProfitModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramModel;
import com.juzifenqi.plus.module.program.model.IPlusProgramQueryModel;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent.DefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDifferenceProgramPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.MultiplexChannelProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.utils.ParamCheckUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 方案配置校验器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11 10:32
 */
@Slf4j
@Component
public class PlusProgramValidator {

    @Autowired
    private IPlusProgramQueryModel plusProgramQueryModel;
    @Autowired
    private IPlusProgramModel      plusProgramModel;
    @Autowired
    private IPlusProfitModel       profitModel;
    @Autowired
    private ProfitHandlerContext   handlerContext;

    /**
     * 差异化定价入参校验
     */
    public void checkDiffParams(SaveDiffPriceEvent event) {
        if (event.getConfigId() == JuziPlusEnum.NEW_JUXP_CARD.getCode()
                && event.getGrade() == null) {
            throw new PlusAbyssException("会员提额顺序不能为空");
        }
        if (event.getConfigId() != JuziPlusEnum.NEW_JUXP_CARD.getCode()
                && event.getGrade() != null) {
            throw new PlusAbyssException("非桔享卡不能配置会员提额顺序");
        }
        for (SaveDifferenceProgramPriceEvent price : event.getDiffList()) {
            if (price.getProgramId() == null || price.getProgramId() <= 0) {
                throw new PlusAbyssException("方案id不能为空");
            }
            if (event.getConfigId() == JuziPlusEnum.NEW_JUXP_CARD.getCode() && StringUtils.isBlank(
                    price.getGrade())) {
                throw new PlusAbyssException("会员提额等级不能为空");
            }
            if (event.getConfigId() != JuziPlusEnum.NEW_JUXP_CARD.getCode()
                    && StringUtils.isNotBlank(price.getGrade())) {
                throw new PlusAbyssException("非桔享卡不能配置会员提额等级");
            }
            PlusProgramEntity program = plusProgramQueryModel.getById(price.getProgramId());
            if (program == null || program.getStatus() == null || program.getStatus() != 1) {
                throw new PlusAbyssException("方案：【" + price.getProgramId() + "】未上架");
            }
        }
    }

    /**
     * 默认方案入参校验
     */
    public void checkDefaultParams(SaveDefaultPriceEvent event) {
        for (DefaultPriceEvent price : event.getPriceList()) {
            Integer programId = price.getDefaultPrice().getProgramId();
            PlusProgramEntity program = plusProgramQueryModel.getById(programId);
            if (program == null || program.getStatus() == null || program.getStatus() != 1) {
                throw new PlusAbyssException("方案：【" + programId + "】未上架");
            }
        }
    }

    /**
     * 校验保存方案参数
     */
    public void checkSaveProgram(SavePlusProgramEvent event) {
        if (JuziPlusEnum.REPAY_CARD.getCode() == event.getConfigId()) {
            if (!Objects.isNull(event.getIsRenew()) && event.getIsRenew().equals(1)) {
                if (Objects.isNull(event.getRenewPrice())
                        || event.getRenewPrice().compareTo(BigDecimal.ZERO) <= 0) {
                    throw new PlusAbyssException("还款卡续费，续费金额不正确");
                }
            }
        }
        if (JuziPlusEnum.RDZX_CARD.getCode() == event.getConfigId()) {
            if (Objects.isNull(event.getRdSendType())) {
                throw new PlusAbyssException("融担卡，开启权益发放类型不能为空");
            }
        }
        if(Objects.nonNull(event.getSendNode()) && Objects.isNull(PlusProgramSendTypeEnum.getByValue(event.getSendNode()))){
            throw new PlusAbyssException("方案发放节点不正确");
        }
        boolean exist = plusProgramModel.existBackstageName(event.getBackstageName(), event.getId(),
                event.getChannel());
        if (exist) {
            throw new PlusAbyssException("方案后台名称已存在");
        }
    }

    /**
     * 校验生效方案参数
     */
    public void checkProgramEffective(Integer programId) {
        Integer proModelCount = profitModel.getCountNullByProgramId(programId);
        if (proModelCount != null && proModelCount > 0) {
            throw new PlusAbyssException("请先维护权益的基本信息");
        }
        List<PlusProModelEntity> list = profitModel.getPlusProModelList(programId);
        HandleCheckProfitDataEvent dataEvent = new HandleCheckProfitDataEvent();
        dataEvent.setProgramId(programId);
        for (PlusProModelEntity model : list) {
            dataEvent.setModelId(model.getModelId());
            boolean result = handlerContext.checkProfitData(dataEvent);
            if (!result) {
                throw new PlusAbyssException(
                        "未配置权益数据：" + PlusModelEnum.getNameById(model.getModelId()));
            }
        }
    }

    /**
     * 复用渠道方案入参校验
     */
    public void checkMultiplexChannelProgramParams(MultiplexChannelProgramEvent event) {
        ParamCheckUtils.checkNull(event, "参数不能为空");
        ParamCheckUtils.checkNull(event.getSourceConfigId(), "源会员类型id不能为空");
        ParamCheckUtils.checkNull(event.getSourceChannelId(), "源渠道id不能为空");
        ParamCheckUtils.checkNull(event.getTargetChannelId(), "目标渠道id不能为空");
        ParamCheckUtils.checkNull(event.getSourceProgramIds(), "源方案id集合不能为空");
        // 商城渠道暂时不能复用
//        if (ChannelEnum.A.getCode().equals(event.getSourceChannelId())
//                || ChannelEnum.A.getCode().equals(event.getTargetChannelId())) {
        if(event.getSourceChannelId() != null || event.getTargetChannelId() != null) {
            throw new PlusAbyssException("暂不支持复用方案功能！");
        }
        if (Objects.equals(event.getSourceChannelId(), event.getTargetChannelId())) {
            throw new PlusAbyssException("相同渠道不能复用！");
        }
        List<Integer> sourceProgramIds = event.getSourceProgramIds();
        if (sourceProgramIds.size() > 20) {
            throw new PlusAbyssException("最多复用20个方案！");
        }
    }

    /**
     * 复用方案入参校验
     */
    public void checkCopyProgramParams(CopyProgramReq req) {
        ParamCheckUtils.checkNull(req, "参数不能为空");
        if (req.getId() == null || req.getId() == 0) {
            throw new PlusAbyssException(VipErrorEnum.PLUS_ERR_CODE_200006);
        }
    }
}
