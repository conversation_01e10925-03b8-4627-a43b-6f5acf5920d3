package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.dto.req.admin.program.PriceQueryReq;
import com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity;
import com.juzifenqi.plus.module.program.repository.po.price.PlusProgramPricePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 会员定价主表mapper
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 17:38
 */
@Mapper
public interface IPlusProgramPriceMapper {

    /**
     * 新增
     */
    int insert(PlusProgramPricePo plusProgramPrice);

    /**
     * 差异化列表
     */
    List<PlusProgramPriceListEntity> selectDiffList(PriceQueryReq query);

    /**
     * 默认方案价列表
     */
    List<PlusProgramPriceListEntity> selectDefaultList(PriceQueryReq query);

    /**
     * 按id查询
     */
    PlusProgramPricePo selectById(Integer id);

    /**
     * 按渠道和会员类型查询
     */
    PlusProgramPricePo selectByChannelAndConfigId(Integer channelId, Integer configId,
            Integer bizSource, Integer priceType);

    /**
     * 数量
     */
    int countList(PriceQueryReq query);

    /**
     * 修改
     */
    int update(PlusProgramPricePo plusProgramPrice);
}
