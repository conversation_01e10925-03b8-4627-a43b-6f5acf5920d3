package com.juzifenqi.plus.module.program.application;

import com.juzifenqi.plus.module.program.model.event.CreateChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EditChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EnableChannelManageEvent;

/**
 * 渠道管理配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/5 20:01
 */
public interface IPlusChannelManageApplication {

    /**
     * 创建渠道管理配置
     */
    void createChannelManager(CreateChannelManagerEvent event);

    /**
     * 编辑渠道管理配置
     */
    void editChannelManager(EditChannelManagerEvent event);

    /**
     * 启用/停用渠道管理配置
     */
    void enableChannelManage(EnableChannelManageEvent event);
}
