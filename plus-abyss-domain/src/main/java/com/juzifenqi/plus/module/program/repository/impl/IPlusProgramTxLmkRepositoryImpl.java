package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramTxLmkRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramTxEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramTxEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramTxLmkConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramTxMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTxPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @createTime 2024/2/29 17:23
 * @description
 */
@Repository
@Slf4j
public class IPlusProgramTxLmkRepositoryImpl implements IPlusProgramTxLmkRepository {

    PlusProgramTxLmkConverter converter = PlusProgramTxLmkConverter.instance;

    @Autowired
    private IPlusProgramTxMapper txMapper;

    @Override
    public void saveTx(List<CreatePlusProgramTxEvent> list) {
        txMapper.insertBatch(converter.toPlusProgramTxPoList(list));
    }

    @Override
    public List<PlusProgramTxEntity> getList(Integer programId) {
        log.info("获取通信联名卡权益列表：{}", programId);
        List<PlusProgramTxPo> plusProgramTxPos = txMapper.selectByProgramId(programId);
        log.info("获取通信联名卡权益列表返回：{}", JSON.toJSONString(plusProgramTxPos));
        return converter.toPlusProgramTxEntityList(plusProgramTxPos);
    }

    @Override
    public void deleteById(Integer id) {
        txMapper.deleteById(id);
    }

    @Override
    public void update(CreatePlusProgramTxEvent event) {
        txMapper.update(converter.toPlusProgramTxPo(event));
    }
}
