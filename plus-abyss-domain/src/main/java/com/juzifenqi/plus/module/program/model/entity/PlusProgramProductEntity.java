package com.juzifenqi.plus.module.program.model.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 会员商品
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/25 15:42
 */
@Data
public class PlusProgramProductEntity {

    /**
     * id
     */
    private int id;

    /**
     * 商品id
     */
    private int productId;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品SKU
     */
    private String productSku;

    /**
     * 会员商品上架状态 0_未上架 1_已上架
     */
    private int saleState;

    /**
     * 采购价
     */
    private BigDecimal purPrice;

    /**
     * 扣减保护价
     */
    private BigDecimal protPrice;

    /**
     * 商品库存
     */
    private Integer stock;

    /**
     * 营销图片/商品图片
     */
    private String productImage;

    /**
     * 售价
     */
    private BigDecimal sellPrice;

    /**
     * 市场价/划线价
     */
    private BigDecimal marketPrice;

    /**
     * 领取状态- 0 未领取 1 本月已领 2 会员有效期内已领  3 每周期内已领 4 已领取(0元商品在用，领取标签不区分限制)
     */
    private Integer receiveState = 0;

    /**
     * 创建人id
     */
    private String createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改人id
     */
    private String updateUserId;

    /**
     * 修改人名称
     */
    private String updateUserName;

    /**
     * 修改时间
     */
    private Date updateTime;

}
