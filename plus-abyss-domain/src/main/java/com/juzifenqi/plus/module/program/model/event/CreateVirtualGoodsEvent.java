package com.juzifenqi.plus.module.program.model.event;

import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/2/27 14:45
 * @description
 */
@Data
public class CreateVirtualGoodsEvent {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型模板ID
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 商品ID(即权益ID,product表主键)
     */
    private Integer productId;

    /**
     * 商品名称(即权益名称)
     */
    private String productName;

    /**
     * 虚拟商品ID(virtual_goods表主键)
     */
    private Integer virtualGoodsId;

    /**
     * 商品sku
     */
    private String sku;

    /**
     * 会员折扣
     */
    private BigDecimal discountRate;

    /**
     * 售价
     */
    private BigDecimal sellPrice;

    /**
     * 虚拟商品上下架 0 下架 1 上架
     */
    private Integer status;

    /**
     * 排序序号
     */
    private Integer rankNum;

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 权益类型id
     */
    private Integer profitTypeId;

    /**
     * 会员价
     */
    private BigDecimal plusPrice;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作者姓名
     */
    private String optName;

}