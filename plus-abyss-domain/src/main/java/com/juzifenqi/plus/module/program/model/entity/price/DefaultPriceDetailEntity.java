package com.juzifenqi.plus.module.program.model.entity.price;

import java.util.List;
import lombok.Data;

/**
 * 默认方案价详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 18:07
 */
@Data
public class DefaultPriceDetailEntity  {

    /**
     * 定价主表id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 配置列表
     */
    private List<PlusDefaultProgramPriceEntity> defaultList;
}
