package com.juzifenqi.plus.module.program.model.entity.detail.land;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.Data;

/**
 * 新落地页-方案详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/13 18:07
 */
@Data
public class LandProgramBasicDetailEntity implements Serializable {

    private static final long serialVersionUID = 3472631687534822929L;

    /**
     * 方案名称
     */
    private String name;

    /**
     * 首单优惠
     */
    private BigDecimal firstOrderPrice;

    /**
     * 划线价
     */
    private BigDecimal memberPrice;

    /**
     * 售价
     */
    private BigDecimal mallMobilePrice;

    /**
     * 推荐标记 0 否  1 是
     */
    private int recommendedMarkup;

    /**
     * 预计可省金额
     */
    private BigDecimal estimateSaveAmount;

    /**
     * 是否设置了挽回弹窗，1-是，0-否
     */
    private Integer beSetRecovery;

    /**
     * 挽回弹窗图片
     */
    private String recoveryImg;

    /**
     * 会员等级
     */
    private Integer memberGrade;

    /**
     * 方案天数
     */
    private int programmeDays;

    /**
     * 会员主题 1-金色；2-紫色；3-铜色；4-蓝色；5-黑色
     */
    private Integer programColor;

    /**
     * 营销文案
     */
    private String marketContent;

    /**
     * 省钱文案
     */
    private String amountContent;

    /**
     * 会员说明
     */
    private String userExplain;

    /**
     * 会员规则
     */
    private String userRules;

    /**
     * 是否支持后付款 1支持 2不支持
     */
    private Integer afterPayState;

    /**
     * 合同标识
     */
    private String contractId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员类型
     */
    private Integer configId;
}
