package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusProgramProductTypeRepository;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductTypeMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo;
import groovy.util.logging.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 会员商品分类配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 10:37
 */
@Slf4j
@Repository
public class PlusProgramProductTypeRepositoryImpl implements IPlusProgramProductTypeRepository {

    @Autowired
    private IPlusProgramProductTypeMapper productTypeMapper;

    @Override
    public PlusProgramProductTypePo getProductType(Integer id) {
        return productTypeMapper.selectTypeById(id);
    }
}
