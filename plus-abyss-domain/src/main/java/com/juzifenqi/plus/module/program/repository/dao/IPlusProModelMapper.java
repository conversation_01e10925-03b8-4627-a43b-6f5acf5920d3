package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/28 15:29
 */
@Mapper
public interface IPlusProModelMapper {

    /**
     * 根据方案ID、模块ID判断是否已经关联
     */
    Integer countProModelByProgramId(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    /**
     * 新增返回ID
     */
    Integer savePlusProModel(PlusProModelPo plusProModel);

    /**
     * 删除
     */
    Integer deletePlusProModel(@Param("programId") int programId,
            @Param("modelId") Integer modelId);

    /**
     * 更新
     */
    Integer updateMemberPlusProModel(PlusProModelPo memberPlusProModel);


    /**
     * 根据方案id查数据信息
     */
    List<PlusProModelPo> getPlusProModelByProgramId(@Param("id") Integer id);

    /**
     * 获取简拼为空的列表
     */
    Integer getCountNullByProgramId(@Param("programId") Integer programId);


    /**
     * 根据方案id和权益类型id获取
     */
    PlusProModelPo getPlusProModelByVo(Integer programId, Integer modelId);

    /**
     * 获取排序后的列表
     */
    List<PlusProModelPo> getProModelByProgramIdOrderBySort(@Param("programId") Integer programId);

    /**
     * 批量新增
     */
    Boolean batchInsertPlusProModel(@Param("list") List<PlusProModelPo> list);

    /**
     * 批量修改排序
     */
    Integer batchUpdateProModelSort(List<PlusProModelPo> list);

    /**
     * 获取用户已开通的会员方案下有【会员提额】的关联信息
     */
    List<PlusProModelPo> getQuotaModelByMember(@Param("userId") Integer userId,
            @Param("channelId") Integer channelId, @Param("configId") Integer configId);

    List<PlusProModelPo> getByModelId(@Param("modelId") Integer modelId);


    /**
     * 批量删除
     */
    Integer batchDel(@Param("programId") Integer programId,
            @Param("modelIds") List<Integer> modelIds);
}
