package com.juzifenqi.plus.module.order.application.ao;

import com.juzifenqi.plus.dto.resp.EnumResp;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: axq
 * @date: 2025-01-22
 */
@Data
public class PlusOrderSeparateAo implements Serializable {
    /**
     * id
     */
    private Long id;
    /**
     * 用户id
     */
    private Integer uid;
    /**
     * 订单号
     */
    private String orderSn;

    /**
     * 支付申请流水号
     */
    private String applySerialNo;

    /**
     * 支付流水号
     */
    private String paySerialNo;

    /**
     * 支付金额
     */
    private BigDecimal amount;

    /**
     * 支付方式
     */
    private EnumResp payAction;

    /**
     * 支付结果
     */
    private EnumResp payResult;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 支付结果回调时间
     */
    private String payCallbackTime;
    /**
     * 失败原因
     */
    private String remark;

    private Integer separateFlag;

    private Integer settleFlag;

    /**
     * 支付方式
     */
    private String payWay;
}
