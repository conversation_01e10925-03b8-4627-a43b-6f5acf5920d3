package com.juzifenqi.plus.module.order.application.impl.strategy;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.enums.LogNodeEnum;
import com.juzifenqi.plus.enums.PayStateEnum;
import com.juzifenqi.plus.enums.PlusDeductLogTypeEnum;
import com.juzifenqi.plus.enums.PlusPayTypeEnum;
import com.juzifenqi.plus.enums.SupplierEnum;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.module.common.IMemberPlusSystemLogRepository;
import com.juzifenqi.plus.module.common.IPlusBlackRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.ISmsRepository;
import com.juzifenqi.plus.module.common.entity.MemberPlusSystemLogEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.common.repository.po.PlusPayBlackListTaskPo;
import com.juzifenqi.plus.module.order.model.IPlusOrderBillModel;
import com.juzifenqi.plus.module.order.model.IPlusOrderShuntQueryModel;
import com.juzifenqi.plus.module.order.model.PlusOrderDeductPlanModel;
import com.juzifenqi.plus.module.order.model.PlusOrderSeparateModel;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDeductLogEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderShuntEntity;
import com.juzifenqi.plus.module.order.model.converter.IPlusOrderModelConverter;
import com.juzifenqi.plus.module.order.model.event.HandleCommonDeductEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductExcEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductFailEvent;
import com.juzifenqi.plus.module.order.model.event.PlusDeductSuccessEvent;
import com.juzifenqi.plus.module.order.model.event.order.CreateOrderBillEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderDeductCallBackEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayDeductRespEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderPayDetailPo;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * application 层策略 ：负责流程封装
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/29 14:55
 */
@Slf4j
public abstract class ApplicationStrategyHandler {


    protected final IPlusOrderModelConverter converter = IPlusOrderModelConverter.instance;

    @Autowired
    protected PlusOrderDeductPlanModel       plusOrderDeductPlanModel;
    @Autowired
    protected IMemberPlusSystemLogRepository systemLogRepository;
    @Autowired
    protected ISmsRepository                 smsRepository;
    @Autowired
    protected CommonApplicationHandler       commonHandler;
    @Autowired
    private   IPlusBlackRepository           blackRepository;
    @Autowired
    private   IPlusOrderBillModel            orderBillModel;
    @Autowired
    private   IPlusOrderShuntQueryModel      plusOrderShuntModel;
    @Autowired
    private   IPlusShuntRepository           shuntRepository;
    @Autowired
    protected PlusOrderSeparateModel         separateModel;

    /**
     * 划扣申请成功后处理逻辑
     *
     * <AUTHOR>
     * @date 2023/9/27 15:14
     **/
    public void afterDeductSucDeal(PlusDeductSuccessEvent successEvent) {
    }

    /**
     * 划扣申请失败后处理逻辑
     *
     * <AUTHOR>
     * @date 2023/9/27 15:17
     **/
    public void afterDeductFailDeal(PlusDeductFailEvent event) {
    }

    /**
     * 划扣申请异常后处理逻辑
     *
     * <AUTHOR>
     * @date 2023/9/27 15:19
     **/
    public void afterDeductExcDeal(PlusDeductExcEvent event) {
    }

    /**
     * 划扣申请成功后处理逻辑
     *
     * <AUTHOR>
     * @date 2023/9/27 15:14
     **/
    @Deprecated
    public void afterDeductSucDealOld(PlusDeductSuccessEvent successEvent) {
    }

    /**
     * 划扣申请失败后处理逻辑
     *
     * <AUTHOR>
     * @date 2023/9/27 15:17
     **/
    @Deprecated
    public void afterDeductFailDealOld(PlusDeductFailEvent event) {
    }

    /**
     * 划扣申请异常后处理逻辑
     *
     * <AUTHOR>
     * @date 2023/9/27 15:19
     **/
    @Deprecated
    public void afterDeductExcDealOld(PlusDeductExcEvent event) {
    }


    /**
     * 划扣申请结束通用处理
     */
    protected void afterCommonHandle(HandleCommonDeductEvent paramVo, PlusPayTypeEnum payType) {
        log.info("划扣申请结束通用处理开始,info:{},type:{}", JSON.toJSONString(paramVo),
                payType.getName());
        PlusOrderEntity plusOrderEntity = paramVo.getPlusOrderEntity();
        String loanOrderSn = paramVo.getLoanOrderSn();
        Integer userId = plusOrderEntity.getUserId();
        String plusOrderSn = plusOrderEntity.getOrderSn();
        // 划扣记录表
        PlusOrderPayDetailPo plusOrderPayDetail = new PlusOrderPayDetailPo();
        plusOrderPayDetail.setUserId(userId);
        plusOrderPayDetail.setProgramName(plusOrderEntity.getProgramName());
        plusOrderPayDetail.setProgramId(plusOrderEntity.getProgramId());
        plusOrderPayDetail.setConfigId(plusOrderEntity.getConfigId());
        plusOrderPayDetail.setChannelId(plusOrderEntity.getChannelId());
        plusOrderPayDetail.setOrderAmount(plusOrderEntity.getOrderAmount());
        plusOrderPayDetail.setOrderSn(plusOrderSn);
        plusOrderPayDetail.setPayType(payType.getCode());
        plusOrderPayDetail.setRemark(payType.getName());
        plusOrderPayDetail.setOrderType(CommonConstant.ONE);
        // 划扣状态默认成功
        plusOrderPayDetail.setPayState(CommonConstant.ONE);
        // 划扣日志默认成功
        PlusDeductLogTypeEnum logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_10;
        // 支付划扣结果
        PlusOrderPayDeductRespEvent respEvent =
                paramVo.getResEntity() != null ? paramVo.getResEntity().getDeductRespEvent() : null;
        if (Objects.isNull(respEvent) || PayStateCodeEnum.F.getCode()
                .equals(respEvent.getState())) {
            log.info("划扣申请失败结束通用处理 userId:{},plusOrderSn:{}", userId, plusOrderSn);
            logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_2;
            plusOrderPayDetail.setPayState(CommonConstant.ZERO);
        }
        if (respEvent != null && !StringUtils.isEmpty(respEvent.getSerialNumber())) {
            plusOrderPayDetail.setTradeSn(respEvent.getSerialNumber());
        }
        //2、保存划扣记录表
        plusOrderDeductPlanModel.savePlusOrderPayDetail(plusOrderPayDetail);
        //3、保存划扣日志
        PlusDeductLogEntity logEntity = converter.commToDeductLogEntity(paramVo, payType.getCode(),
                logTypeEnum);
        if (Objects.nonNull(respEvent)) {
            logEntity.setDeductStatus(
                    PayStateCodeEnum.I.getCode().equals(respEvent.getState()) ? 1 : 0);
            if (StringUtils.isNotBlank(respEvent.getMessage())
                    && respEvent.getMessage().length() > 30) {
                logEntity.setDeductMsg(respEvent.getMessage().substring(30));
            } else {
                logEntity.setDeductMsg(respEvent.getMessage());
            }
        }
        plusOrderDeductPlanModel.saveDeductLog(logEntity);
        //4、划扣同步成功，保存分流对账信息
        if (respEvent != null && PayStateCodeEnum.I.getCode().equals(respEvent.getState())) {
            CreateOrderBillEvent event = converter.toCreateOrderBillEvent(plusOrderEntity);
            // 设置支付流水号
            event.setSerialNumber(plusOrderPayDetail.getTradeSn());
            orderBillModel.saveOrderBill(event);
        }
        //5、保存会员日志
        LogNodeEnum node = LogNodeEnum.LOG_NODE_PLUS_DEDUCT_FEE;
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        String remark = "通过借款订单" + loanOrderSn + "触发划扣";
        // 分流信息
        PlusOrderShuntEntity shunt = plusOrderShuntModel.getByOrderSn(plusOrderSn);
        if (shunt != null && shunt.getPlanInSupplier() != null) {
            remark += ",请求将会员费入账至" + getSupplierName(shunt.getPlanInSupplier());
        }
        plusLog.setMemberId(userId);
        plusLog.setChannelId(plusOrderEntity.getChannelId());
        plusLog.setProgramId(plusOrderEntity.getProgramId());
        plusLog.setOrderSn(plusOrderSn);
        plusLog.setCancelRemark(remark);
        systemLogRepository.saveMemberPlusLogBySystem(plusLog, node);
        // 更新分账记录的状态和流水号
        updateOrderSeparate(paramVo.getResEntity());
        log.info("划扣申请通用结束:{},{}", userId, plusOrderSn);
    }

    /**
     * 划扣结束通用处理
     */
    @Deprecated
    protected void afterCommonHandleOld(HandleCommonDeductEvent paramVo, PlusPayTypeEnum payType) {
        log.info("划扣结束通用处理开始,info:{},type:{}", JSON.toJSONString(paramVo),
                payType.getName());
        PlusOrderEntity plusOrderEntity = paramVo.getPlusOrderEntity();
        String loanOrderSn = paramVo.getLoanOrderSn();
        Integer userId = plusOrderEntity.getUserId();
        String plusOrderSn = plusOrderEntity.getOrderSn();
        // 划扣记录表
        PlusOrderPayDetailPo plusOrderPayDetail = new PlusOrderPayDetailPo();
        plusOrderPayDetail.setUserId(userId);
        plusOrderPayDetail.setProgramName(plusOrderEntity.getProgramName());
        plusOrderPayDetail.setProgramId(plusOrderEntity.getProgramId());
        plusOrderPayDetail.setConfigId(plusOrderEntity.getConfigId());
        plusOrderPayDetail.setChannelId(plusOrderEntity.getChannelId());
        plusOrderPayDetail.setOrderAmount(plusOrderEntity.getOrderAmount());
        plusOrderPayDetail.setOrderSn(plusOrderSn);
        plusOrderPayDetail.setPayType(payType.getCode());
        plusOrderPayDetail.setRemark(payType.getName());
        plusOrderPayDetail.setOrderType(CommonConstant.ONE);
        // 划扣状态默认成功
        plusOrderPayDetail.setPayState(CommonConstant.ONE);
        // 划扣日志默认成功
        PlusDeductLogTypeEnum logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_10;
        // 支付划扣结果
        PlusOrderDeductCallBackEvent payOrderVo =
                paramVo.getResEntity() != null ? paramVo.getResEntity().getCallBackEvent() : null;
        if (Objects.isNull(payOrderVo) || PayStateEnum.F.getCode().equals(payOrderVo.getStatus())) {
            log.info("划扣失败结束通用处理 userId:{},plusOrderSn:{}", userId, plusOrderSn);
            logTypeEnum = PlusDeductLogTypeEnum.LOG_TYPE_2;
            plusOrderPayDetail.setPayState(CommonConstant.ZERO);
        }
        if (payOrderVo != null && !StringUtils.isEmpty(payOrderVo.getSerialNumber())) {
            plusOrderPayDetail.setTradeSn(payOrderVo.getSerialNumber());
        }
        //2、保存划扣记录表
        plusOrderDeductPlanModel.savePlusOrderPayDetail(plusOrderPayDetail);
        //3、保存划扣日志
        PlusDeductLogEntity logEntity = converter.commToDeductLogEntity(paramVo, payType.getCode(),
                logTypeEnum);
        if (Objects.nonNull(payOrderVo)) {
            logEntity.setDeductStatus("S".equals(payOrderVo.getStatus()) ? 1 : 0);
            if (StringUtils.isNotBlank(payOrderVo.getMsg()) && payOrderVo.getMsg().length() > 30) {
                logEntity.setDeductMsg(payOrderVo.getMsg().substring(30));
            } else {
                logEntity.setDeductMsg(payOrderVo.getMsg());
            }
        }
        plusOrderDeductPlanModel.saveDeductLog(logEntity);
        //4、划扣同步成功，保存分流对账信息
        if (payOrderVo != null && PayStateEnum.S.getCode().equals(payOrderVo.getStatus())) {
            CreateOrderBillEvent event = converter.toCreateOrderBillEvent(plusOrderEntity);
            event.setSerialNumber(plusOrderPayDetail.getTradeSn());
            orderBillModel.saveOrderBill(event);
        }
        //5、保存会员日志
        LogNodeEnum node = LogNodeEnum.LOG_NODE_PLUS_DEDUCT_FEE;
        MemberPlusSystemLogEntity plusLog = new MemberPlusSystemLogEntity();
        String remark = "通过借款订单" + loanOrderSn + "触发划扣";
        // 分流信息
        PlusOrderShuntEntity shunt = plusOrderShuntModel.getByOrderSn(plusOrderSn);
        if (shunt != null && shunt.getPlanInSupplier() != null) {
            remark += ",请求将会员费入账至" + getSupplierName(shunt.getPlanInSupplier());
        }
        plusLog.setMemberId(userId);
        plusLog.setChannelId(plusOrderEntity.getChannelId());
        plusLog.setProgramId(plusOrderEntity.getProgramId());
        plusLog.setOrderSn(plusOrderSn);
        plusLog.setCancelRemark(remark);
        systemLogRepository.saveMemberPlusLogBySystem(plusLog, node);
        log.info("调用支付划扣会员订单结束:{},{}", userId, plusOrderSn);
    }

    /**
     * 获取分流主体名称
     */
    private String getSupplierName(Integer supplierId) {
        PlusShuntSupplierEntity supplierCache = shuntRepository.getSupplierCache(supplierId);
        if (supplierCache == null) {
            // 兼容上线期间，未配置分流主体的情况
            return SupplierEnum.getName(supplierId);
        }
        return supplierCache.getSupplierName();
    }

    /**
     * 加入后付款黑名单任务表
     */
    protected void saveBlackListTask(PlusOrderEntity order) {
        // 加入后付款黑名单跑批任务
        PlusPayBlackListTaskPo taskPo = new PlusPayBlackListTaskPo();
        taskPo.setConfigId(order.getConfigId());
        taskPo.setChannelId(order.getChannelId());
        taskPo.setUserId(order.getUserId());
        blackRepository.saveBlackListTask(taskPo);
    }

    /**
     * 立即划扣时当前用户在主动支付，进行延迟划扣
     */
    protected void saveDelayDeductPlan(PlusDeductFailEvent event) {
        if (event.getDeductFlag() == PlusPayTypeEnum.PAY_TYPE_1) {
            PlusOrderEntity order = event.getPlusOrderEntity();
            commonHandler.saveDeductDelayPlan(event.getPlusOrderEntity(), CommonConstant.nextDeductTime,
                    event.getOrderSn(), order.getConfigId() + "-立即划扣重试", "非余额不足延迟重试");
            log.info("立即划扣时用户主动支付,保存十分钟延迟任务成功：{}", order.getOrderSn());
        }
    }

    /**
     * 立即划扣失败，非余额不足。保存延迟划扣，10分钟后再划
     */
    protected boolean saveDelayDeductPlan(PlusDeductFailEvent event, PlusOrderEntity order,
            String loanOrderSn, String cardName) {
        if (event.getResEntity() == null) {
            log.info("立即划扣失败非余额不足保存十分钟延迟划扣,支付划扣返回结果为空,不处理：{}",
                    order.getOrderSn());
            return false;
        }
        PlusOrderPayDeductRespEvent respEvent = event.getResEntity().getDeductRespEvent();
        // 如果是非余额不足，需要加入延迟划扣表，10分钟后再划扣
        // 如果是用户无划扣银行卡，也需要进入延迟划扣表
        if (respEvent != null && respEvent.isNoBalanceError()) {
            commonHandler.saveDeductDelayPlan(order, CommonConstant.nextDeductTime, loanOrderSn,
                    cardName + "-立即划扣重试", "非余额不足延迟重试");
            log.info("立即划扣失败非余额不足保存十分钟延迟任务成功：{}", order.getOrderSn());
            return true;
        }
        log.info("立即划扣失败非余额不足保存十分钟延迟划扣,划扣结果非支付失败或余额不足,不处理：{}",
                order.getOrderSn());
        return false;
    }

    /**
     * 立即划扣失败，非余额不足。保存延迟划扣，10分钟后再划
     */
    @Deprecated
    protected boolean saveDelayDeductPlanOld(PlusDeductFailEvent event, PlusOrderEntity order,
            String loanOrderSn, String cardName) {
        if (event.getResEntity() == null) {
            log.info("立即划扣失败非余额不足保存十分钟延迟划扣,支付划扣返回结果为空,不处理：{}",
                    order.getOrderSn());
            return false;
        }
        PlusOrderDeductCallBackEvent backEvent = event.getResEntity().getCallBackEvent();
        // 如果是非余额不足，需要加入延迟划扣表，10分钟后再划扣
        if (backEvent != null && PayStateEnum.F.getCode().equals(backEvent.getStatus())
                && org.apache.commons.lang3.StringUtils.isNotBlank(backEvent.getMsg())
                && !backEvent.getMsg().contains(CommonConstant.deductMsg)) {
            commonHandler.saveDeductDelayPlan(order, CommonConstant.nextDeductTime, loanOrderSn,
                    cardName + "-立即划扣重试", "非余额不足延迟重试");
            log.info("立即划扣失败非余额不足保存十分钟延迟任务成功：{}", order.getOrderSn());
            return true;
        }
        log.info("立即划扣失败非余额不足保存十分钟延迟划扣,划扣结果非支付失败或余额不足,不处理：{}",
                order.getOrderSn());
        return false;
    }

    /**
     * 更新分账记录的状态和流水号
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/2 15:26
     */
    protected void updateOrderSeparate(PlusOrderDeductResEntity resEntity) {
        separateModel.deductUpdateOrderSeparate(resEntity);
    }
}
