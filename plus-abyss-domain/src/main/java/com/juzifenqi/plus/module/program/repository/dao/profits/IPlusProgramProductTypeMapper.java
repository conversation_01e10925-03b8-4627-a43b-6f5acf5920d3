package com.juzifenqi.plus.module.program.repository.dao.profits;


import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 会员商品分类配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 10:34
 */
@Mapper
public interface IPlusProgramProductTypeMapper {

    void saveProductType(PlusProgramProductTypePo po);

    void deleteProductTypeById(@Param("id") Integer id);

    void updateProductType(PlusProgramProductTypePo po);

    List<PlusProgramProductTypePo> getByProgramAndModel(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    List<Integer> selectRankNumList(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    PlusProgramProductTypePo getProductTypeById(@Param("id") Integer id);

    /**
     * 根据方案查询分类列表
     */
    List<PlusProgramProductTypePo> selectTypeListByProgram(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    List<PlusProgramProductTypePo> selectTypeListByProgramId(@Param("programId") Integer programId);

    /**
     * 根据id查询分类列表
     */
    PlusProgramProductTypePo selectTypeById(@Param("id") Integer id);

    Integer batchInsertType(@Param("list") List<PlusProgramProductTypePo> typeList);
}
