package com.juzifenqi.plus.module.order.model.contract;

import com.juzifenqi.plus.dto.req.admin.OrderBillQueryReq;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * 订单对账
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 10:42
 */
public interface IPlusOrderBillRepository {

    /**
     * 保存对账记录
     */
    void saveOrderBill(PlusOrderBillEntity entity);

    /**
     * 查询
     */
    PlusOrderBillEntity getByOrderSn(String orderSn);

    /**
     * 根据订单号和支付流水号查询
     */
    PlusOrderBillEntity getByOrderSnAndSerialNumber(String orderSn, String serialNumber);

    /**
     * 修改
     */
    void updatePlusOrderBill(PlusOrderBillEntity entity);

    /**
     * 分页查询Data 只查询入账成功的
     */
    List<PlusOrderBillEntity> pageList(OrderBillQueryReq req);

    /**
     * 分页查询Count 只查询入账成功的
     */
    Integer pageListCount(OrderBillQueryReq req);

    /**
     * 获取入账重试任务列表
     */
    List<PlusOrderBillEntity> getInComeRetryList(Integer size);

    /**
     * 批量修改入账状态
     */
    void updateInStateBatch(List<Integer> ids, Integer inState);

    /**
     * 获取出账重试任务列表
     */
    List<PlusOrderBillEntity> getOutComeRetryList(Integer size);

    /**
     * 批量修改出账状态
     */
    void updateOutStateBatch(List<Integer> ids, Integer outState);

    /**
     * 获取合同签署重试任务列表
     */
    List<PlusOrderBillEntity> getContractReTryList(Integer size);

    /**
     * 批量修改合同签署状态
     */
    void updateContractStateBatch(List<Integer> ids, Integer contractState);

    /**
     * 获取合同上传重试任务列表
     */
    List<PlusOrderBillEntity> getContractUploadReTryList(Integer size);

    /**
     * 批量修改合同上传状态
     */
    void updateContractUploadStateBatch(List<Integer> ids, Integer contractState);

    /**
     * 获取划扣成功回调时未获取到bill记录导致没有走入账逻辑的数据（6个小时以前的数据）
     */
    List<PlusOrderBillEntity> selectDeductIncomeRetryList(@Param("limit") Integer limit);
}
