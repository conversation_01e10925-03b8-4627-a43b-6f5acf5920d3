package com.juzifenqi.plus.module.program.application.converter;

import com.juzifenqi.plus.module.asserts.model.contract.entity.VirtualGoodsCheckResultEntity;
import com.juzifenqi.plus.module.asserts.model.event.ProductCheckEvent;
import com.juzifenqi.plus.module.asserts.model.event.VirtualGoodsCheckEvent;
import com.juzifenqi.plus.module.program.model.contract.entity.LyffVirtualDetailEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProductDetailEntity;
import com.juzifenqi.plus.module.program.model.event.LyffVirtualDetailEvent;
import com.juzifenqi.plus.module.program.model.event.PlusProductDetailEvent;
import java.math.BigDecimal;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 会员商品转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/4 18:16
 */
@Mapper
public interface IPlusProductApplicationConverter {

    IPlusProductApplicationConverter instance = Mappers.getMapper(
            IPlusProductApplicationConverter.class);

    ProductCheckEvent toProductCheckEvent(PlusProductDetailEvent event);

    VirtualGoodsCheckEvent toVirtualGoodsCheckEvent(LyffVirtualDetailEvent event);

    @Mappings({@Mapping(target = "buyButtonState", source = "checkResult.buyButtonState"),
            @Mapping(target = "imgUrl", source = "productDetailEntity.masterImg"),
            @Mapping(target = "productName", source = "productDetailEntity.productName"),
            @Mapping(target = "plusMarketPrice", source = "productDetailEntity.marketPrice"),
            @Mapping(target = "plusPrice", source = "plusPrice"),
            @Mapping(target = "productDetail", source = "productDetailEntity.productDetail")})
    LyffVirtualDetailEntity toLyffVirtualDetailEntity(VirtualGoodsCheckResultEntity checkResult,
            BigDecimal plusPrice, PlusProductDetailEntity productDetailEntity);
}
