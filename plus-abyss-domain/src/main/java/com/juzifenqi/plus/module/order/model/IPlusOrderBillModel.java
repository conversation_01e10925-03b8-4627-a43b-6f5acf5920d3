package com.juzifenqi.plus.module.order.model;

import com.juzifenqi.plus.dto.req.admin.OrderBillQueryReq;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.PayCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.event.order.CreateOrderBillEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderOutcomeNotifyEvent;
import java.util.List;

/**
 * 订单出入账
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 10:33
 */
public interface IPlusOrderBillModel {

    /**
     * 保存订单对账信息
     */
    void saveOrderBill(CreateOrderBillEvent event);

    /**
     * 订单退款通知三方出账
     */
    void outcomeNotify(OrderOutcomeNotifyEvent event);

    /**
     * 通知三方出账重试
     */
    void outcomeRetry(Integer size);

    /**
     * 订单支付成功预入账
     * <p>符合入账条件后，发起签署合同mq、等待签署结果mq回调后再上传合同文件到三方服务器，成功后再真正通知三方入账</p>
     */
    void incomeNotify(PayCallbackEntity entity);

    /**
     * 通知三方入账入账重试
     */
    void incomeRetry(Integer size);

    /**
     * 通知三方入账
     */
    void incomeExecute(PlusOrderBillEntity orderBill, String serialNumber);

    /**
     * 分页查询Data 只查询入账成功的
     */
    List<PlusOrderBillEntity> pageList(OrderBillQueryReq req);

    /**
     * 分页查询Count 只查询入账成功的
     */
    Integer pageListCount(OrderBillQueryReq req);

    /**
     * 获取对账信息
     */
    PlusOrderBillEntity getByOrderSn(String orderSn);

    /**
     * 修改对账信息
     */
    void update(PlusOrderBillEntity entity);

    /**
     * 合同签署重试
     */
    void contractSignReTry(Integer size);

    /**
     * 合同上传重试
     */
    void contractUploadReTry(Integer size);

    /**
     * 划扣成功回调时未获取到bill记录导致没有走入账逻辑的重试
     */
    void deductIncomeRetry(Integer limit);

    /**
     * 新支付系统订单支付成功预入账
     * <p>符合入账条件后，发起签署合同mq、等待签署结果mq回调后再上传合同文件到三方服务器，成功后再真正通知三方入账</p>
     */
    void newPayIncomeNotify(NewPayResultCallbackEntity entity);
}
