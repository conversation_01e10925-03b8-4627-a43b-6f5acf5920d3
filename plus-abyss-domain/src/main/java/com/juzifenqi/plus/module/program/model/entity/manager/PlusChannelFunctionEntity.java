package com.juzifenqi.plus.module.program.model.entity.manager;

import java.util.Date;
import lombok.Data;

/**
 * 渠道功能
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-12-13 13:23
 */
@Data
public class PlusChannelFunctionEntity {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 主表id
     */
    private Integer manageId;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 开通方式1,2,3以,分割（1：收银台支付，2：系统划扣，3：后付款，4：全款划扣）
     */
    private String openMode;

    /**
     * 会员权益5,8,25,18以,分割（5：生活权益，8：会员提额，25：拒就退款，18：快速审核）
     */
    private String plusProfit;

    /**
     * 定价方式1,2以,号分割（1：方案价，2：差异化定价）
     */
    private String pricingMode;

    /**
     * 后付款开通是否划扣:1是 ，0否
     */
    private Integer payLaterDeduct;

    /**
     * 是否极速退卡：1是，0否
     */
    private Integer rapidReturnCard;

    /**
     * 是否延迟退卡，1_是 0_否
     */
    private Integer delayReturnCard;

    /**
     * 延迟退款天数
     */
    private Integer delayDays;

    /**
     * 短信触达1,2,3以,分割（1：开通成功短信，2：划扣通知短信，3：退卡通知短信）
     */
    private String noteReach;

    /**
     * 支付成功回跳地址
     */
    private String paySuccessUrl;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
