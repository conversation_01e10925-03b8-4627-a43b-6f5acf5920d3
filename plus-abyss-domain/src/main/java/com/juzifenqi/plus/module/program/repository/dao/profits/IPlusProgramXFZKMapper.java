package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 息费折扣券配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 10:34
 */
@Mapper
public interface IPlusProgramXFZKMapper {

    /**
     * 删除
     */
    Integer deleteMemberPlusInDisCoupon(Integer id);

    PlusProgramXFZKPo getInDisCouponById(Integer id);

    /**
     * 更新
     */
    Integer updateMemberPlusInDisCoupon(PlusProgramXFZKPo memberPlusInDisCoupon);

    List<PlusProgramXFZKPo> getListByProgramId(Integer programId);

    List<PlusProgramXFZKPo> getListByProgramIdAndModelId(Integer programId, Integer modelId);

    Boolean batchInsertPlusProgramXFZK(@Param("list") List<PlusProgramXFZKPo> list);

    List<PlusProgramXFZKPo> pageList(Integer programI);

    Integer countXFZK(Integer programId);

    Integer batchInsertXFZKCoupon(@Param("list") List<PlusProgramXFZKPo> list);

    List<Integer> getByCouponIds(@Param("list") List<Integer> couponIds,
            @Param("programId") Integer programIdx);
}
