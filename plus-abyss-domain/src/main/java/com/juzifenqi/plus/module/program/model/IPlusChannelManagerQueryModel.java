package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.dto.req.admin.program.ChannelManagerQueryReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelManagerEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo;
import java.util.List;

/**
 * 渠道管理model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 11:31
 */
public interface IPlusChannelManagerQueryModel {


    /**
     * 是否支持发送短信
     */
    boolean supportSms(Integer channelId, Integer configId, Integer smsType);

    /**
     * 是否支持延迟退款
     */
    boolean supportDelayRefund(Integer channelId, Integer configId);

    /**
     * 获取延迟退款天数
     */
    Integer getDelayRefundDays(Integer channelId, Integer configId);

    /**
     * 是否支持急速退款
     */
    boolean supportRapidRefund(Integer channelId, Integer configId);

    /**
     * 获取配置的渠道列表信息
     */
    List<PlusChannelManagerEntity> getChannelList(ChannelManagerQueryReq req);

    /**
     * 获取渠道配置
     */
    PlusChannelManagePo getByChannelId(Integer channelId);

    /**
     * 渠道管理配置详情
     */
    PlusChannelManagerEntity getChannelManagerDetail(Integer id);

    /**
     * 渠道管理配置分页列表
     */
    PageResultEntity<PlusChannelManagerEntity> getChannelListPage(ChannelManagerQueryReq req);

    /**
     * 获取渠道支持的开通方式
     */
    List<String> getOpenModeList(Integer channelId, Integer configId, Integer afterPayState,
            Integer memberId);
}
