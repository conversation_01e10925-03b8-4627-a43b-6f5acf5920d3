package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.dto.req.profits.QueryProgramCouponReq;
import com.juzifenqi.plus.enums.OptEventEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusProgramLogNodeEnum;
import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.JuziCouponEntity;
import com.juzifenqi.plus.module.common.IExternalCouponRepository;
import com.juzifenqi.plus.module.common.IPlusProfitSystemLogRepository;
import com.juzifenqi.plus.module.common.IPlusProgramLogRepository;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.common.entity.PlusProfitSystemLogEntity;
import com.juzifenqi.plus.module.program.model.adapter.ProfitAdapter;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramCouponRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramTaskEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramCouponEntity;
import com.juzifenqi.plus.module.program.model.event.CreateProgramCouponEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramCouponConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramRejectionMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramTaskMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramXFZKMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 方案下配置的优惠券信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 10:31
 */
@Repository
@Slf4j
public class PlusProgramCouponRepositoryImpl implements IPlusProgramCouponRepository {

    private final PlusProgramCouponConverter converter = PlusProgramCouponConverter.instantiate;

    @Autowired
    private IPlusProgramXFZKMapper      xfzkMapper;
    @Autowired
    private IPlusProgramTaskMapper      taskMapper;
    @Autowired
    private IPlusProgramRejectionMapper rejectionMapper;
    @Autowired
    private IExternalCouponRepository   couponRepository;
    @Autowired
    private IPlusProgramLogRepository   programLogRepository;
    @Autowired
    private IPlusProfitSystemLogRepository plusProfitSystemLogRepository;
    @Autowired
    private ProfitAdapter                  profitAdapter;


    @Override
    public void batchSaveXFZKCoupon(List<CreateProgramCouponEvent> list) {
        List<Integer> couponIds = list.stream().map(CreateProgramCouponEvent::getCouponId)
                .collect(Collectors.toList());
        // 排除掉已经存在的
        List<Integer> existCouponList = xfzkMapper.getByCouponIds(couponIds,
                list.get(0).getProgramId());
        if (!CollectionUtils.isEmpty(existCouponList)) {
            list.removeIf(e -> existCouponList.contains(e.getCouponId()));
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<PlusProgramXFZKPo> xfzkPos = converter.toXFZFPoList(list);
            xfzkMapper.batchInsertXFZKCoupon(xfzkPos);
        }
    }

    @Override
    public void batchSaveDMDSTask(List<CreateProgramCouponEvent> list) {
        List<PlusProgramTaskPo> taskPoList = taskMapper.getTaskCouponByProgramId(
                list.get(0).getProgramId());
        // 获取操作人信息
        Integer optId = list.get(0).getOptId();
        String optName = list.get(0).getOptName();
        if (CollectionUtils.isNotEmpty(taskPoList)) {
            List<Integer> existCouponList = taskPoList.stream().map(PlusProgramTaskPo::getCouponId)
                    .collect(Collectors.toList());
            list.removeIf(e -> existCouponList.contains(e.getCouponId()));
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<PlusProgramTaskPo> taskPos = converter.toTaskPoList(list);
            taskMapper.batchInsertPlusProgramTask(taskPos);
            // 添加日志
            List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(taskPos.stream().map(PlusProgramTaskPo::getId).collect(Collectors.toList()),
                    PlusModelEnum.DMDS,
                    OptEventEnum.OPT_COUPON_ADD,
                    optId, optName);
            if (CollectionUtils.isNotEmpty(logList)){
                plusProfitSystemLogRepository.saveBatch(logList);
            }
        }
    }

    @Override
    public void batSaveRejection(List<CreateProgramCouponEvent> list) {
        List<PlusProgramRejectionPo> rejectionCoupons = rejectionMapper.getRejectionCouponByProgramId(
                list.get(0).getProgramId());
        //获取操作人信息
        Integer optId = list.get(0).getOptId();
        String optName = list.get(0).getOptName();
        if (CollectionUtils.isNotEmpty(rejectionCoupons)) {
            List<Integer> existCouponList = rejectionCoupons.stream()
                    .map(PlusProgramRejectionPo::getCouponId).collect(Collectors.toList());
            list.removeIf(e -> existCouponList.contains(e.getCouponId()));
        }
        if (!CollectionUtils.isEmpty(list)) {
            List<PlusProgramRejectionPo> pos = converter.toRejectionPoList(list);
            rejectionMapper.batchInsertPlusRejection(pos);
            // 添加日志
            List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(pos.stream().map(PlusProgramRejectionPo::getId).collect(Collectors.toList()),
                    PlusModelEnum.JJP,
                    OptEventEnum.OPT_COUPON_ADD,
                    optId, optName);
            if (CollectionUtils.isNotEmpty(logList)){
                plusProfitSystemLogRepository.saveBatch(logList);
            }
        }
    }

    @Override
    public PageResultEntity<PlusProgramCouponEntity> getXFZKCouponList(
            QueryProgramCouponReq event) {
        Integer count = xfzkMapper.countXFZK(event.getProgramId());
        List<PlusProgramXFZKPo> list = xfzkMapper.pageList(event.getProgramId());
        List<PlusProgramCouponEntity> couponEntityList = converter.toPlusProgramCouponEntity(list);
        setCouponInfo(couponEntityList);
        return new PageResultEntity<>(count, couponEntityList);
    }

    @Override
    public PageResultEntity<PlusProgramCouponEntity> getDMDSCouponList(
            QueryProgramCouponReq event) {
        Integer count = taskMapper.getCountByProgramId(event.getProgramId());
        List<PlusProgramTaskPo> list = taskMapper.pageList(event.getProgramId(),
                event.getStartPage(), event.getPageSize());
        List<PlusProgramCouponEntity> couponEntityList = converter.toPlusProgramCouponEntity2(list);
        setCouponInfo(couponEntityList);
        return new PageResultEntity<>(count, couponEntityList);
    }

    @Override
    public PageResultEntity<PlusProgramCouponEntity> getRejectCouponList(
            QueryProgramCouponReq event) {
        Integer count = rejectionMapper.pageListCount(event.getProgramId());
        List<PlusProgramRejectionPo> pos = rejectionMapper.pageList(event.getProgramId(),
                event.getStartPage(), event.getPageSize());
        List<PlusProgramCouponEntity> couponEntityList = converter.toPlusProgramCouponEntity3(pos);
        setCouponInfo(couponEntityList);
        return new PageResultEntity<>(count, couponEntityList);
    }

    @Override
    public void delXFZKCoupon(CreateProgramCouponEvent event) {
        PlusProgramXFZKPo xfzkPo = xfzkMapper.getInDisCouponById(event.getId());
        if (xfzkPo != null) {
            String content = "删除了息费折扣数据--" + JSON.toJSONString(xfzkPo);
            // 桔享Plus方案更新记录日志
            programLogRepository.saveLog(xfzkPo.getProgramId(), event.getOptId(),
                    event.getOptName(), PlusProgramLogNodeEnum.LOG_NODE_DEL_XFZK.getName(),
                    content);
        }
        xfzkMapper.deleteMemberPlusInDisCoupon(event.getId());
    }

    @Override
    public void delDMDSTask(CreateProgramCouponEvent event) {
        taskMapper.deletePlusProgramTask(event.getId());
    }

    @Override
    public void delRejectCoupon(CreateProgramCouponEvent event) {
        rejectionMapper.deletePlusProgramRejection(event.getId());
    }

    @Override
    public void updDMDSTask(CreateProgramCouponEvent event) {
        PlusProgramTaskPo task = new PlusProgramTaskPo();
        task.setId(event.getId());
        task.setOrderPrice(event.getOrderPrice());
        task.setOrdersNumber(event.getOrdersNumber());
        PlusProgramTaskPo dbInfo = taskMapper.loadPlusProgramTask(event.getId());
        taskMapper.updatePlusProgramTask(task);
        saveDMDSLog(task, dbInfo, event.getOptId(), event.getOptName());
    }

    @Override
    public void updRejectCoupon(CreateProgramCouponEvent event) {
        PlusProgramRejectionPo rejection = new PlusProgramRejectionPo();
        rejection.setId(event.getId());
        rejection.setType(event.getType());
        PlusProgramRejectionPo dbInfo = rejectionMapper.loadPlusProgramRejection(event.getId());
        rejectionMapper.updatePlusProgramRejection(rejection);
        saveJJPLog(rejection, dbInfo, event.getOptId(), event.getOptName());
    }

    /**
     * 保存日志 - 多买多送
     * @param param
     * @param dbInfo
     * @param optId
     * @param optName
     */
    public void saveDMDSLog(PlusProgramTaskPo param, PlusProgramTaskPo dbInfo, Integer optId, String optName) {
        if (Objects.isNull(param) || Objects.isNull(dbInfo)){
            return;
        }
        String content = "";
        if (ObjectUtils.compare(param.getOrderPrice(), dbInfo.getOrderPrice()) != 0){
            content += "更新订单金额;";
        }
        if (ObjectUtils.notEqual(param.getOrdersNumber(), dbInfo.getOrdersNumber())){
            content += "更新下单笔数;";
        }
        if (StringUtils.isEmpty(content)){
            return;
        }
        // 添加日志
        List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(Arrays.asList(param.getId()),
                PlusModelEnum.DMDS,
                OptEventEnum.OPT_COUPON_MDF,
                content, optId, optName);
        plusProfitSystemLogRepository.saveBatch(logList);
    }

    /**
     * 保存日志 - 拒就赔
     * @param param
     * @param dbInfo
     * @param optId
     * @param optName
     */
    public void saveJJPLog(PlusProgramRejectionPo param, PlusProgramRejectionPo dbInfo, Integer optId, String optName) {
        if (Objects.isNull(param) || Objects.isNull(dbInfo)){
            return;
        }
        String content = "";
        if (ObjectUtils.notEqual(param.getType(), dbInfo.getType())){
            content += "更新被拒类型;";
        }
        if (StringUtils.isEmpty(content)){
            return;
        }
        // 添加日志
        List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(Arrays.asList(param.getId()),
                PlusModelEnum.JJP,
                OptEventEnum.OPT_COUPON_MDF,
                content, optId, optName);
        plusProfitSystemLogRepository.saveBatch(logList);
    }

    @Override
    public List<PlusProgramTaskEntity> getProgramTaskList(Integer programId) {
        List<PlusProgramTaskPo> list = taskMapper.getTaskCouponByProgramId(programId);
        return converter.toPlusProgramTaskEntityList(list);
    }

    @Override
    public List<PlusProgramRejectionPo> getRejectList(Integer programId) {
        return rejectionMapper.getRejectionCouponByProgramId(programId);
    }

    @Override
    public List<PlusProgramRejectionPo> getRejectListByIds(List<Integer> rejectIds) {
        return rejectionMapper.getByRejectionIds(rejectIds);
    }

    @Override
    public List<PlusProgramXFZKPo> getXfzkList(Integer programId) {
        return xfzkMapper.getListByProgramId(programId);
    }

    /**
     * Load查询
     */
    @Override
    public PlusProgramRejectionPo getRejectById(Integer id) {
        return rejectionMapper.loadPlusProgramRejection(id);
    }

    /**
     * 设置优惠券信息
     */
    private void setCouponInfo(List<PlusProgramCouponEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        list.forEach(coupon -> {
            JuziCouponEntity juziCoupon = couponRepository.getByCouponId(coupon.getCouponId());
            if (Objects.nonNull(juziCoupon)) {
                coupon.setCouponName(juziCoupon.getCouponName());
                coupon.setIndex(juziCoupon.getIndex());
            }
        });
    }
}
