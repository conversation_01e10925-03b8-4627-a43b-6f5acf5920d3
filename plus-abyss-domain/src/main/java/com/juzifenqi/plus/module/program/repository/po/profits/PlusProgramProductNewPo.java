package com.juzifenqi.plus.module.program.repository.po.profits;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 新会员商品表
 *
 * <AUTHOR>
 * @date 2023/05/06 13:45
 */
@Data
public class PlusProgramProductNewPo {

    /**
     *
     */
    private Integer id;

    /**
     * 商品ID
     */
    private Integer productId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 方案类型
     */
    private Integer configId;

    /**
     * 顺序号
     */
    private Integer sort;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 定价类型 1_折扣 2_会员定价
     */
    private Integer priceType;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 营销图片
     */
    private String productImage;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 分类id
     */
    private Integer typeId;

    /**
     * 上架状态 0_未上架 1_上架
     */
    private Integer saleState;

    /**
     * 操作人
     */
    private String optUser;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     *
     */
    private Date biTime;

}