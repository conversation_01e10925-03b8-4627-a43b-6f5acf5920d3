package com.juzifenqi.plus.module.program.application;

import com.juzifenqi.plus.dto.req.admin.program.CopyProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramEditQueryReq;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramQueryReq;
import com.juzifenqi.plus.dto.req.admin.program.PriceQueryReq;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusConfigEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DefaultPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DiffPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.MultiplexChannelProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.UpProgramEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import java.util.List;

/**
 * 方案
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 14:57
 */
public interface IPlusProgramApplication {

    /**
     * 获取方案改价列表
     */
    PageResultEntity<PlusProgramEditPriceEntity> getProgramEditPriceList(
            PlusProgramEditQueryReq req);

    /**
     * 新增方案改价任务
     */
    void addProgramEditPrice(CreateProgramEditPriceEvent event);

    /**
     * 处理改价任务
     */
    void executeProgramEditPriceTask();

    /**
     * 新增差异化方案配置
     */
    void saveDiffPrice(SaveDiffPriceEvent event);

    /**
     * 修改差异化方案配置
     */
    void editDiffPrice(SaveDiffPriceEvent event);

    /**
     * 获取差异化定价详情
     */
    DiffPriceDetailEntity getDiffDetail(Integer id);

    /**
     * 保存默认方案配置
     */
    void addDefaultPrice(SaveDefaultPriceEvent event);

    /**
     * 编辑默认方案配置
     */
    void editDefaultPrice(SaveDefaultPriceEvent event);

    /**
     * 默认方案配置详情
     */
    DefaultPriceDetailEntity getDefaultPriceDetail(Integer id);

    /**
     * 获取差异化/默认方案配置分页列表
     */
    PageResultEntity<PlusProgramPriceListEntity> getPriceList(PriceQueryReq req);

    /**
     * 获取方案分页列表
     */
    PageResultEntity<PlusProgramListEntity> getProgramList(ProgramQueryReq req);

    /**
     * 新增方案
     */
    void addProgram(SavePlusProgramEvent event);

    /**
     * 编辑方案
     */
    void editProgram(SavePlusProgramEvent event);

    /**
     * 方案生效
     */
    void programEffective(SavePlusProgramEvent event);

    /**
     * 复用渠道方案
     */
    void multiplexChannelProgram(MultiplexChannelProgramEvent event);

    /**
     * 方案详情
     */
    ProgramDetailEntity getProgramDetail(Integer id);

    /**
     * 复制方案
     */
    void copyProgram(CopyProgramReq req);

    /**
     * 加载会员商品缓存
     */
    void reloadProductCache();

    /**
     * 获取备选方案列表
     */
    PageResultEntity<PlusProgramEntity> getMemberPlusProgramAll(PlusProgramQueryReq req);

    /**
     * 方案上下架
     */
    Boolean updateProgrammeUpAndDown(UpProgramEvent event);

    /**
     * 处理权益基础权益缓存
     */
    void handlePlusBasicProfitsCache();

    /**
     * 获取所有会员类型列表
     */
    List<PlusConfigEntity> loadPlusConf();

    /**
     * 通过方案id查询方案信息
     */
    PlusProgramEntity getPlusProgramById(Integer programId);
}
