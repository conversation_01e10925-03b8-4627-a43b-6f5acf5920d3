package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProModelEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;

/**
 * 提额等级
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/8/31 10:20
 */
public interface IPlusLiftAmountRepository {

    /**
     * 获取提额等级
     */
    PlusLiftAmountPo getByProgramId(Integer programId);

    /**
     * 权益基本信息和提额数据
     */
    PlusProModelEntity getPlusProModelDetail(CreatePlusProModelEvent event);

    /**
     * 添加提额数据信息+编辑基本信息
     */
    void saveLiftAmount(CreatePlusProModelEvent event);
}
