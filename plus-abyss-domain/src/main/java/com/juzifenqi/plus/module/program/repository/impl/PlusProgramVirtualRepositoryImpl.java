package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusProgramVirtualRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeEntity;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualTypeEvent;
import com.juzifenqi.plus.module.program.model.event.EditLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramVirtualConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramVirtualMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramVirtualTypeMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

/**
 * 方案下虚拟权益
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/11/10 15:33
 */
@Repository
@Slf4j
public class PlusProgramVirtualRepositoryImpl implements IPlusProgramVirtualRepository {

    private final PlusProgramVirtualConverter converter = PlusProgramVirtualConverter.instance;

    @Autowired
    private IPlusProgramVirtualMapper plusProgramVirtualMapper;

    @Autowired
    private IPlusProgramVirtualTypeMapper plusProgramVirtualTypeMapper;

    @Override
    public PlusProgramVirtualPo getByProgramAndSku(Integer programId, Integer modelId, String sku) {
        return plusProgramVirtualMapper.getByProgramAndSku(programId, modelId, sku);
    }

    @Override
    public PlusProgramVirtualTypePo getVirtualTypeById(Integer id) {
        return plusProgramVirtualTypeMapper.loadPlusProgramVirtualType(id);
    }

    @Override
    public PlusProgramVirtualTypeEntity getTypeById(Integer id) {
        return converter.toPlusProgramVirtualTypeEntity(
                plusProgramVirtualTypeMapper.loadPlusProgramVirtualType(id));
    }

    @Override
    public List<PlusProgramVirtualTypeEntity> getVirtualTypeByProgram(Integer programId,
            Integer modelId, Integer typeLevel) {
        List<PlusProgramVirtualTypePo> virtualTypeList = plusProgramVirtualTypeMapper.getVirtualTypeByLevel(
                programId, modelId, typeLevel);
        return converter.toPlusProgramVirtualTypeEntityList(virtualTypeList);
    }

    @Override
    public void saveVirtualType(CreateVirtualTypeEvent event) {
        plusProgramVirtualTypeMapper.savePlusProgramVirtualType(
                converter.toPlusProgramVirtualTypePo(event));
    }

    @Override
    public void modifyVirtualTypeById(CreateVirtualTypeEvent event) {
        plusProgramVirtualTypeMapper.updatePlusProgramVirtualType(
                converter.toPlusProgramVirtualTypePo(event));
    }

    @Override
    public Integer removeVirtualTypeById(Integer id) {
        return plusProgramVirtualTypeMapper.deletePlusProgramVirtualType(id);
    }

    @Override
    public void removeVirtualByType(Integer programId, Integer profitTypeId) {
        List<PlusProgramVirtualPo> profits = plusProgramVirtualMapper.getByProgramAndType(programId,
                profitTypeId);
        if (CollectionUtils.isEmpty(profits)) {
            log.info("当前分类下面权益为空,无法删除：{},{}", programId, profitTypeId);
            return;
        }
        // 批量删除
        plusProgramVirtualMapper.batchDelete(programId, profitTypeId);
    }

    @Override
    public Integer checkVirtualTypeRankNumExists(CreateVirtualTypeEvent event) {
        return plusProgramVirtualTypeMapper.checkVirtualTypeRankNumExists(
                converter.toPlusProgramVirtualTypePo(event));
    }

    @Override
    public List<PlusProgramVirtualEntity> getVirtualByType(Integer typeId) {
        List<PlusProgramVirtualPo> list = plusProgramVirtualMapper.getVirtualByType(typeId);
        return converter.toPlusProgramVirtualEntityList(list);
    }

    @Override
    public Integer countVirtualByType(Integer typeId) {
        return plusProgramVirtualMapper.pageListCountByQuery(typeId);
    }

    @Override
    public PlusProgramVirtualEntity getVirtualById(CreateVirtualGoodsEvent event) {
        return converter.toPlusProgramVirtualEntity(
                plusProgramVirtualMapper.getVirtualById(event.getId()));
    }

    @Override
    public Integer removeVirtualById(CreateVirtualGoodsEvent event) {
        return plusProgramVirtualMapper.deleteById(event.getId());
    }

    @Override
    public void modifyVirtualById(CreateVirtualGoodsEvent event) {
        plusProgramVirtualMapper.updateById(converter.toPlusProgramVirtualPo(event));
    }

    @Override
    public void saveVirtualBatch(List<PlusProgramVirtualPo> list) {
        plusProgramVirtualMapper.saveBatch(list);
    }

    @Override
    public List<PlusProgramVirtualTypeEntity> getTypeByParentId(Integer profitTypeId) {
        log.info("查询分类下级分类开始，参数：{}", profitTypeId);
        List<PlusProgramVirtualTypePo> list = plusProgramVirtualTypeMapper.getTypeByParentId(
                profitTypeId);
        log.info("查询分类下级分类返回：{}", list);
        return converter.toPlusProgramVirtualTypeEntityList(list);
    }

    @Override
    public List<PlusProgramVirtualPo> getBySku(String sku) {
        return plusProgramVirtualMapper.getBySku(sku);
    }

    @Override
    public int syncStatusBySku(PlusProgramVirtualPo po) {
        return plusProgramVirtualMapper.syncStatusBySku(po);
    }

    @Override
    public List<PlusProgramVirtualEntity> getByProgramIdAndModelId(Integer programId,
            Integer modelId) {
        List<PlusProgramVirtualPo> list = plusProgramVirtualMapper.getByProgram(programId, modelId);
        return converter.toPlusProgramVirtualEntityList(list);
    }

    @Override
    public List<PlusProgramVirtualEntity> getPageVirtualList(Integer programId, Integer modelId,
            Integer start, Integer pageSize) {
        List<PlusProgramVirtualPo> list = plusProgramVirtualMapper.getPageVirtualList(programId,
                modelId, start, pageSize);
        return converter.toPlusProgramVirtualEntityList(list);
    }

    @Override
    public Integer countList(Integer programId, Integer modelId) {
        return plusProgramVirtualMapper.countList(programId, modelId);
    }

    @Override
    public PlusProgramVirtualEntity getVirtualById(Integer id) {
        return converter.toPlusProgramVirtualEntity(plusProgramVirtualMapper.getVirtualById(id));
    }

    @Override
    public void modifyVirtualById(EditLyffVirtualGoodsEvent event) {
        plusProgramVirtualMapper.updateById(converter.toPlusProgramVirtualPo(event));
    }

    @Override
    public Integer removeVirtualById(Integer id) {
        return plusProgramVirtualMapper.deleteById(id);
    }

    @Override
    public PlusProgramVirtualEntity getVirtualBySku(Integer programId, Integer modelId,
            String productSku) {
        return converter.toPlusProgramVirtualEntity(plusProgramVirtualMapper.getByProgramAndSku(
                programId, modelId, productSku));
    }

    @Override
    public List<PlusProgramVirtualEntity> listBySkuAndConfig(List<Integer> configIdList, Integer modelId, String productSku) {
        return converter.toPlusProgramVirtualEntityList(plusProgramVirtualMapper.listBySkuAndConfig(configIdList, modelId, productSku));
    }

    @Override
    public int updateImgUrlBatch(List<Integer> idList, String imgUrl) {
        return plusProgramVirtualMapper.updateImgUrlBatch(idList, imgUrl);
    }
}
