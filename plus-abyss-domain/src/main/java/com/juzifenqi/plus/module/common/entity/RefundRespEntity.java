package com.juzifenqi.plus.module.common.entity;

import lombok.Data;

/**
 * 新支付系统退款结果转换实体
 * <AUTHOR>
 * @Date 2024/9/7
 */

@Data
public class RefundRespEntity {

    // 退款状态
    private String status;

    // 退款通道
    private String payChannel;

    // 支付流水号
    private String paySerialNum;

    // 加密银行卡号
    private String ctCardNo;

    // 退款失败code
    private String errorCode;

    // 退款失败原因
    private String errorMsg;

    // 收款银行名称
    private String bankName;

    // 退款时间
    private String successTime;

    private String  applyTime;
}
