package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusRenewRepository;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusRenewRelevanceMapper;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 方案续费配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 14:55
 */
@Slf4j
@Repository
public class PlusRenewRepositoryImpl implements IPlusRenewRepository {

    private final PlusProgramConverter converter = PlusProgramConverter.instance;

    @Autowired
    private IPlusRenewRelevanceMapper relevanceMapper;

    @Override
    public PlusRenewRelevancePo getByProgramId(Integer programId) {
        return relevanceMapper.getRenewInfoByProgramId(programId);
    }

    @Override
    public void addRenewRelevance(SavePlusProgramEvent event, Integer programId) {
        PlusRenewRelevancePo relevancePo = converter.toPlusRenewRelevancePo(event, programId);
        relevanceMapper.savePlusRenewRelevance(relevancePo);
    }

    @Override
    public void editRenewRelevance(SavePlusProgramEvent event, Integer programId) {
        PlusRenewRelevancePo relevancePo = converter.toPlusRenewRelevancePo(event, programId);
        if (event.getIsRenewId() == null || event.getIsRenewId() == 0) {
            relevanceMapper.savePlusRenewRelevance(relevancePo);
        } else {
            relevancePo.setId(event.getIsRenewId());
            relevanceMapper.updatePlusRenewRelevance(relevancePo);
        }
    }
}
