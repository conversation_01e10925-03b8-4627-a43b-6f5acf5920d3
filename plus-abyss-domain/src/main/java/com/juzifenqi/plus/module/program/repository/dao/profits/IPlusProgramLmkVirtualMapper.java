package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.dto.req.profits.QueryLmkVirtualReq;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 联名卡权益表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/5/6 15:24
 */
@Mapper
public interface IPlusProgramLmkVirtualMapper {

    /**
     * 新增返回ID
     */
    int insertBatch(@Param("list") List<PlusProgramLmkVirtualPo> list);

    /**
     * 删除
     */
    int delete(Integer id);

    /**
     * 更新
     */
    int update(@Param("plusProgramLmkVirtual") PlusProgramLmkVirtualPo plusProgramLmkVirtual);

    /**
     * 列表
     */
    List<PlusProgramLmkVirtualPo> pageList(@Param("vo") QueryLmkVirtualReq req);

    /**
     * 数量
     */
    int count(@Param("vo") QueryLmkVirtualReq req);

    /**
     * 按sku和方案id查询
     */
    PlusProgramLmkVirtualPo selectBySkuAndProgramId(String sku, Integer programId);

    /**
     * 方案下的所有已上架虚拟权益
     */
    List<PlusProgramLmkVirtualPo> selectByProgramId(Integer programId);

    /**
     * 方案下所有权益
     */
    int countByProgramId(Integer programId);

    /**
     * 按id获取
     */
    PlusProgramLmkVirtualPo selectById(Integer id);

    /**
     * 根据sku查询
     */
    List<PlusProgramLmkVirtualPo> selectBySku(String sku);

    /**
     * 按sku更新状态
     */
    int updateStateBySku(PlusProgramLmkVirtualPo lmkVirtual);
}
