package com.juzifenqi.plus.module.program.repository.entity;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * 会员权益-商品
 */
@Data
public class PlusProgramProfitsProductObject {
    private Long programId;

    private Long packageId;

    /**
     * 权益类型（会员商品、虚拟商品）
     */
    private Integer profitType;

    /**
     * （虚拟）商品id
     */
    private Long productId;

    /**
     * 折扣比例(只有虚拟权益有这个)
     */
    private BigDecimal discount = BigDecimal.ONE;
}
