package com.juzifenqi.plus.module.program.model;


import com.juzifenqi.plus.dto.req.detail.LandReq;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import java.util.List;

/**
 * 落地页model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/15 14:02
 */
public interface IPlusLandDetailModel {

    /**
     * 新落地页详情
     */
    LandDetailEntity getLandDetail(LandReq event);

    /**
     * 老落地页详情
     */
    LandOldDetailEntity getOldLandDetail(LandReq event);

    /**
     * 老落地页公共详情
     * <p>不返回权益明细数据，目前用于营销会员后，前端根据方案id获取方案的基础信息和折扣信息</p>
     */
    LandOldDetailEntity getLandCommonDetail(LandReq event);

    /**
     * 方案购买记录轮播数据
     */
    List<String> getPlusBuyRecords(Integer programId);
}
