package com.juzifenqi.plus.module.program.model.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 区间还款优惠券配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9 17:30
 */
@Data
public class PlusQjhkCouponConfigEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     *
     */
    private BigDecimal minAmount;

    /**
     *
     */
    private BigDecimal maxAmount;

    /**
     * 期数
     */
    private Integer periodNum;

    /**
     * 优惠券id
     */
    private Integer couponId;

    /**
     * 状态 1_有效 2_无效
     */
    private Integer state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
