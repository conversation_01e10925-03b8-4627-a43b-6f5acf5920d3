package com.juzifenqi.plus.module.program.model.entity.detail.land;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 方案营销折扣信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/7 13:46
 */
@Data
public class LandDiscountDetailEntity {

    /**
     * 是否符合 默认不符合 = 0  符合：1
     */
    private Integer showDiscountInfo = 0;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 折扣价格
     */
    private BigDecimal discountPrice;

    /**
     * 结束营销时间和当前时间的相差ms数
     */
    private Long discountEndTime;
}
