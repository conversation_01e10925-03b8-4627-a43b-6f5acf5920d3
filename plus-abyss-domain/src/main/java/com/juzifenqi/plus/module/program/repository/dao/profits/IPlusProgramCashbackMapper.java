package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * <p>
 * 购物返现权益表 Mapper 接口
 * </p>
 *
 * <AUTHOR>
 * @since 2023-05-06
 */
@Mapper
public interface IPlusProgramCashbackMapper {

    /**
     * 通过会员类型id与方案id查询配置信息
     */
    List<PlusProgramCashbackPo> queryByProgramIdAndConfigId(@Param("configId") Integer configId,
            @Param("programId") Integer programId, @Param("modelId") Integer modelId);

    /**
     * 通过会员类型id与方案id查询配置信息
     */
    List<PlusProgramCashbackPo> queryByProgramId(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    int countByProgramId(@Param("programId") Integer programId, @Param("modelId") Integer modelId);

    @Select("select * from plus_program_cashback where program_id = #{programId}")
    List<PlusProgramCashbackPo> listByProgramId(@Param("programId") Integer programId);

    Integer insertBatch(@Param("cashbacks") List<PlusProgramCashbackPo> cashbacks);

    Integer deleteByProgramIdAndConfigId(@Param("configId") Integer configId,
            @Param("programId") Integer programId, @Param("modelId") Integer modelId);

    Integer countList(@Param("configId") Integer configId, @Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    Integer insert(PlusProgramCashbackPo cashback);

    /**
     * 分页查询
     */
    List<PlusProgramCashbackPo> getPageList(@Param("configId") Integer configId,
            @Param("programId") Integer programId, @Param("modelId") Integer modelId,
            @Param("start") Integer start, @Param("size") Integer pageSize);

    /**
     * 根据id查询
     */
    PlusProgramCashbackPo getById(@Param("id") Integer id);

    /**
     * 根据id修改-还款返现权益配置
     */
    Integer updateById(@Param("cashback") PlusProgramCashbackPo plusProgramCashbackPo);

    /**
     * 根据id删除
     */
    Integer deleteById(@Param("id") Integer id);

}
