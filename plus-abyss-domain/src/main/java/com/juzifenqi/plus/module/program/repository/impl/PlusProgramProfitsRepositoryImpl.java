package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusProgramProfitsRepository;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductNewMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

@Slf4j
@Repository
public class PlusProgramProfitsRepositoryImpl implements IPlusProgramProfitsRepository {

    @Autowired
    private IPlusProgramProductNewMapper productNewMapper;

    @Override
    public boolean existNewProduct(Integer programId, Integer productId, Integer modelId) {
        PlusProgramProductNewPo po = productNewMapper.getByProductIdAndProgramId(programId,
                productId, modelId);
        return po != null;
    }
}
