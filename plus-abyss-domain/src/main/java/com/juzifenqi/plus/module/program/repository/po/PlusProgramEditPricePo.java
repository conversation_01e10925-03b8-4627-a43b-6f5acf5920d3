package com.juzifenqi.plus.module.program.repository.po;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 方案改价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 14:29
 */
@Data
public class PlusProgramEditPricePo {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 修改后的价格
     */
    private BigDecimal price;

    /**
     * 修改前的划线价
     */
    private BigDecimal oldPrice;

    /**
     * 修改后的划线价
     */
    private BigDecimal linePrice;

    /**
     * 修改前的划线价
     */
    private BigDecimal oldLinePrice;

    /**
     * 处理结果 1未处理  2处理成功  3处理失败 4失效不需要处理
     */
    private Integer result;

    /**
     * 执行建时间
     */
    private Date executeTime;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作人名称
     */
    private String optName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
