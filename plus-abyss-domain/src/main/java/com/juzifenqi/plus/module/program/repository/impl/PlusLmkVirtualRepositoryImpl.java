package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.profits.QueryLmkVirtualReq;
import com.juzifenqi.plus.module.asserts.repository.converter.IPlusLmkVirtualConverter;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.contract.IPlusLmkVirtualRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramLmkVirtualEvent;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramLmkVirtualMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramLmkVirtualPo;
import com.juzifenqi.plus.utils.RedisUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 联名卡
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/9/18 16:19
 */
@Repository
@Slf4j
public class PlusLmkVirtualRepositoryImpl implements IPlusLmkVirtualRepository {

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private IPlusProgramLmkVirtualMapper plusProgramLmkVirtualMapper;

    private final IPlusLmkVirtualConverter converter = IPlusLmkVirtualConverter.instance;

    @Override
    public PlusProgramLmkVirtualEntity getVirtualById(Integer id) {
        PlusProgramLmkVirtualPo plusProgramLmkVirtualPo = plusProgramLmkVirtualMapper.selectById(
                id);
        return converter.toPlusProgramLmkVirtualEntity(plusProgramLmkVirtualPo);
    }

    @Override
    public List<PlusProgramLmkVirtualEntity> getLmkList(Integer programId) {
        String redisKey = RedisConstantPrefix.PROGRAM_LMK_VIRTUAL + programId;
        log.info("获取缓存的联名卡虚拟权益列表：{},redisKey:{}", programId, redisKey);
        String cacheRes = redisUtils.get(redisKey);
        log.info("获取缓存的联名卡虚拟权益列表信息：{}", cacheRes);
        if (StringUtils.isBlank(cacheRes)) {
            log.info("获取缓存的联名卡权益列表为空：{}", programId);
            return null;
        }
        List<PlusProgramLmkVirtualEntity> list = JSONObject.parseArray(cacheRes,
                PlusProgramLmkVirtualEntity.class);
        log.info("获取缓存的联名卡虚拟权益列表返回:{}", JSONObject.toJSONString(list));
        return list;
    }

    @Override
    public PageResultEntity<PlusProgramLmkVirtualEntity> getListPage(QueryLmkVirtualReq req) {
        PageResultEntity<PlusProgramLmkVirtualEntity> resultEntity = new PageResultEntity<>();
        List<PlusProgramLmkVirtualPo> lmkVirtualPos = plusProgramLmkVirtualMapper.pageList(req);
        List<PlusProgramLmkVirtualEntity> returnList = converter.toPlusProgramLmkVirtualEntityList(
                lmkVirtualPos);
        int count = plusProgramLmkVirtualMapper.count(req);
        resultEntity.setList(returnList);
        resultEntity.setCount(count);
        return resultEntity;
    }

    @Override
    public void delete(Integer id) {
        plusProgramLmkVirtualMapper.delete(id);
    }

    @Override
    public void edit(CreatePlusProgramLmkVirtualEvent event) {
        plusProgramLmkVirtualMapper.update(converter.toPlusProgramLmkVirtualPo(event));
    }

    @Override
    public void saveBatch(List<PlusProgramLmkVirtualPo> list) {
        plusProgramLmkVirtualMapper.insertBatch(list);
    }

    @Override
    public int updateVirtualState(CreatePlusProgramLmkVirtualEvent event) {
        return plusProgramLmkVirtualMapper.updateStateBySku(
                converter.toPlusProgramLmkVirtualPo(event));
    }

    @Override
    public PlusProgramLmkVirtualEntity selectBySkuAndProgramId(String sku, Integer programId) {
        return converter.toPlusProgramLmkVirtualEntity(
                plusProgramLmkVirtualMapper.selectBySkuAndProgramId(sku, programId));
    }

    @Override
    public List<PlusProgramLmkVirtualEntity> selectBySku(String sku) {
        return converter.toPlusProgramLmkVirtualEntityList(
                plusProgramLmkVirtualMapper.selectBySku(sku));
    }

    @Override
    public List<PlusProgramLmkVirtualEntity> selectByProgramId(Integer programId) {
        return converter.toPlusProgramLmkVirtualEntityList(
                plusProgramLmkVirtualMapper.selectByProgramId(programId));
    }

    @Override
    public int countByProgramId(Integer programId) {
        return plusProgramLmkVirtualMapper.countByProgramId(programId);
    }
}
