package com.juzifenqi.plus.module.program.model.entity;

import java.math.BigDecimal;
import java.util.Date;
import lombok.Data;

/**
 * 新会员商品表
 *
 * <AUTHOR>
 * @date 2023/05/06 13:45
 */
@Data
public class PlusProgramProductNewEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 商品ID
     */
    private Integer productId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 方案类型
     */
    private Integer configId;

    /**
     * 顺序号
     */
    private Integer sort;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 操作人
     */
    private String optUser;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 商品spu
     */
    private String productSpu;

    /**
     * 商品分类名称
     */
    private String productCateName;

    /**
     * 商品状态
     */
    private Integer productState;

    /**
     * 会员价格
     */
    private BigDecimal mallMobilePrice;

    /**
     * 市场价
     */
    private BigDecimal marketPrice;

    /**
     * 库存
     */
    private Integer productStock;

    /**
     * 分类id
     */
    private Integer typeId;

    /**
     * 商品sku
     */
    private String productSku;

    /**
     * 营销图片
     */
    private String productImage;

    /**
     * 折扣
     */
    private BigDecimal discountRate;

    /**
     * 会员折扣价
     */
    private BigDecimal plusPrice;
}