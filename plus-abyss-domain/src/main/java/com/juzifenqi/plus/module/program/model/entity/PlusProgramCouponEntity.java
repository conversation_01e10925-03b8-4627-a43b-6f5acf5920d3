package com.juzifenqi.plus.module.program.model.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 方案下优惠券配置列表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/9 13:47
 */
@Data
public class PlusProgramCouponEntity {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 拒就陪类型：1、审核不通过；2、认证不通过
     */
    private Integer type;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 下单笔数
     */
    private int ordersNumber;

    /**
     * 订单满足金额
     */
    private BigDecimal orderPrice;

    /**
     * 优惠券名称
     */
    private String couponName;

    /**
     * 优惠券唯一索引标识
     */
    private Integer index;

    /**
     * 优惠券ID
     */
    private Integer couponId;

    /**
     * 优惠券类别：0-满减优惠券，1-免息优惠券，2、折扣优惠券，3、还款优惠券，4、息费折扣券
     */
    private Integer couponCategory;

    /**
     * 操作人id
     */
    private Integer optId;

    /**
     * 操作人
     */
    private String optName;
}
