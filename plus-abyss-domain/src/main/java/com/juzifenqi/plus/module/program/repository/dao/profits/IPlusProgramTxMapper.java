package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTxPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
@Mapper
public interface IPlusProgramTxMapper {

    /**
     * 新增
     */
    int insertBatch(@Param("list") List<PlusProgramTxPo> list);

    /**
     * 按id删除
     */
    int deleteById(Integer id);

    /**
     * 按方案id查询
     */
    List<PlusProgramTxPo> selectByProgramId(Integer programId);

    /**
     * 按方案id统计
     */
    int countByProgramId(Integer programId);

    /**
     * 修改
     */
    int update(PlusProgramTxPo po);
}
