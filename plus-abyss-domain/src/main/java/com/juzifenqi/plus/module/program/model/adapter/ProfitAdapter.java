package com.juzifenqi.plus.module.program.model.adapter;

import static com.juzifenqi.plus.constants.RedisConstantPrefix.PROGRAM_PLUS_PRODUCTS;
import static com.juzifenqi.plus.enums.PlusModelEnum.LYSP;
import static java.util.Objects.isNull;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.OptEventEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.ProfitSystemLogTypeEnum;
import com.juzifenqi.plus.enums.VipErrorEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.contract.IMemberPlusCashbackRepository;
import com.juzifenqi.plus.module.asserts.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.PlusProductEntity;
import com.juzifenqi.plus.module.asserts.model.entity.profit.PlusProductTypeEntity;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitLandEvent;
import com.juzifenqi.plus.module.asserts.model.impl.strategy.profits.ProfitHandlerContext;
import com.juzifenqi.plus.module.common.IProductExternalRepository;
import com.juzifenqi.plus.module.common.entity.PlusProfitSystemLogEntity;
import com.juzifenqi.plus.module.common.repository.external.acl.ProductExternalRepositoryAcl;
import com.juzifenqi.plus.module.common.repository.po.ProductPo;
import com.juzifenqi.plus.module.program.model.contract.IPlusLmkVirtualRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramProductRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramVirtualRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramLmkVirtualEntity;
import com.juzifenqi.plus.module.program.model.converter.IPlusProfitModelConverter;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductNewEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramProductTypeEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeProfitLevelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusYygProductEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandModelBasicDetailEntity;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualTypeEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.product.entity.Product;
import com.juzifenqi.product.util.MonthPaymentUtil;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 权益适配层
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/25 16:10
 */
@Slf4j
@Component
public class ProfitAdapter {

    private final IPlusProfitModelConverter converter = IPlusProfitModelConverter.instance;


    @Autowired
    private IPlusProModelRepository       plusProModelRepository;
    @Autowired
    private IPlusProgramVirtualRepository plusProgramVirtualRepository;
    @Autowired
    private IPlusProgramProductRepository plusProgramProductRepository;
    @Autowired
    private RedisUtils                    redisUtils;
    @Autowired
    private ProductExternalRepositoryAcl  productExternalRepositoryAcl;
    @Autowired
    private IProductExternalRepository    productExternalRepository;
    @Autowired
    private IPlusLmkVirtualRepository     plusLmkVirtualRepository;
    @Autowired
    private ProfitHandlerContext          profitHandlerContext;
    @Autowired
    private IMemberPlusCashbackRepository plusCashbackRepository;


    public List<PlusProfitSystemLogEntity> buildBatchLog(List<PlusProgramProductNewEntity> list,
            OptEventEnum typeEnum, Integer optId, String optNm, Integer logType) {
        List<PlusProfitSystemLogEntity> logList = new ArrayList<>();
        list.forEach(
                profits -> logList.add(buildLog(profits.getId(), typeEnum, optId, optNm, logType)));
        return logList;
    }

    public List<PlusProfitSystemLogEntity> buildVirtualBatchLog(List<PlusProgramVirtualPo> list,
            OptEventEnum typeEnum, Integer optId, String optNm, Integer logType) {
        List<PlusProfitSystemLogEntity> logList = new ArrayList<>();
        list.forEach(
                profits -> logList.add(buildLog(profits.getId(), typeEnum, optId, optNm, logType)));
        return logList;
    }
    /**
     * 批量保存日志
     * @param recordIdList
     */
    public List<PlusProfitSystemLogEntity> buildCouponLog(List<Integer> recordIdList, PlusModelEnum plusModelEnum ,OptEventEnum optEventEnum, Integer optId, String optName) {
        if (CollectionUtils.isEmpty(recordIdList) || Objects.isNull(optEventEnum)){
            return Collections.emptyList();
        }
        return buildCouponLog(recordIdList, plusModelEnum, optEventEnum, optEventEnum.getName(), optId, optName);
    }

    /**
     * 批量保存日志
     * @param recordIdList
     */
    public List<PlusProfitSystemLogEntity> buildCouponLog(List<Integer> recordIdList, PlusModelEnum plusModelEnum ,OptEventEnum optEventEnum, String optDesc, Integer optId, String optName) {
        if (CollectionUtils.isEmpty(recordIdList) || Objects.isNull(plusModelEnum)){
            return Collections.emptyList();
        }
        ProfitSystemLogTypeEnum profitSystemLogTypeEnum = null;
        switch (plusModelEnum) {
            case KKL:
                profitSystemLogTypeEnum = ProfitSystemLogTypeEnum.KKL_DATA;
                break;
            case YX:
                profitSystemLogTypeEnum = ProfitSystemLogTypeEnum.YXHB_DATA;
                break;
            case DMDS:
                profitSystemLogTypeEnum = ProfitSystemLogTypeEnum.DMDS_DATA;
                break;
            case JJP:
                profitSystemLogTypeEnum = ProfitSystemLogTypeEnum.JJP_DATA;
                break;
            default:
                break;
        }
        if (Objects.isNull(profitSystemLogTypeEnum)){
            log.info("日志保存优惠券类型为空 优惠券信息 {}", JSON.toJSONString(recordIdList.get(0)));
            return Collections.emptyList();
        }
        ProfitSystemLogTypeEnum profitSystemLogTypeFinal = profitSystemLogTypeEnum;
        return recordIdList.stream()
                .map(item -> buildLog(item,
                        optEventEnum,
                        optDesc,
                        optId,
                        optName,
                        profitSystemLogTypeFinal.getCode()))
                .collect(Collectors.toList());
    }

    public PlusProfitSystemLogEntity buildLog(Integer recordId, OptEventEnum optEnum, String optDesc, Integer optId,
            String optNm, Integer logType) {
        PlusProfitSystemLogEntity plusLog = new PlusProfitSystemLogEntity();
        plusLog.setRecordId(recordId);
        plusLog.setLogType(logType);
        plusLog.setOptEvent(optEnum.getCode());
        plusLog.setRemark(optDesc);
        plusLog.setOptUserId(optId);
        plusLog.setOptUserName(optNm);
        return plusLog;
    }

    public PlusProfitSystemLogEntity buildLog(Integer recordId, OptEventEnum optEnum, Integer optId,
            String optNm, Integer logType) {
        return buildLog(recordId, optEnum, optEnum.getName(), optId, optNm, logType);
    }

    /**
     * 重新加载联名卡虚拟权益缓存
     */
    public void reloadLmkCache(Integer programId) {
        //刷新会员虚拟商品权益列表缓存
        String redisKey = RedisConstantPrefix.PROGRAM_LMK_VIRTUAL + programId;
        log.info("刷新联名卡虚拟权益缓存开始，programId：{},redisKey：{}", programId, redisKey);
        // 先删缓存
        redisUtils.delete(redisKey);
        List<PlusProgramLmkVirtualEntity> list = this.getLmkVirtualList(programId);
        if (CollectionUtils.isEmpty(list)) {
            log.info("刷新联名卡虚拟权益缓存数据为空，programId：{}", programId);
            return;
        }
        //放入缓存
        redisUtils.set(redisKey, JSONObject.toJSONString(list));
        log.info("刷新联名卡虚拟权益缓存结束，programId：{}", programId);
    }

    /**
     * 获取联名卡虚拟权益列表
     */
    public List<PlusProgramLmkVirtualEntity> getLmkVirtualList(Integer programId) {
        log.info("获取方案下的所有已上架的联名卡虚拟权益：{}", programId);
        List<PlusProgramLmkVirtualEntity> list = plusLmkVirtualRepository.selectByProgramId(
                programId);
        if (CollectionUtils.isEmpty(list)) {
            log.info("获取方案下的所有已上架的联名卡虚拟权益为空：{}", programId);
            return null;
        }
        // 获取商品id
        List<Integer> productIds = list.stream().map(PlusProgramLmkVirtualEntity::getProductId)
                .collect(Collectors.toList());
        List<Product> productByIds = productExternalRepositoryAcl.getProductByIds(productIds);
        if (CollectionUtils.isEmpty(productByIds)) {
            log.info("获取方案下的所有已上架的联名卡虚拟权益,商品为空：{}", productIds);
            return null;
        }
        //处理商品价格
        Map<Integer, Product> productMap = new HashMap<>();
        productByIds.forEach(product -> productMap.put(product.getId(), product));
        list.forEach(virtual -> {
            Product product = productMap.get(virtual.getProductId());
            if (product != null) {
                virtual.setProductName(product.getName1());
                virtual.setMarketPrice(product.getMarketPrice());
                virtual.setMallMobilePrice(product.getMallMobilePrice());
            }
        });
        log.info("获取方案下的所有已上架的联名卡虚拟权益返回：{}", JSONObject.toJSONString(list));
        return list;
    }

    /**
     * 重新加载0元商品缓存
     */
    public void reloadPlusProductCache() {
        // 获取所有已配置0元商品权益方案
        List<PlusProModelEntity> models = plusProModelRepository.getByModelId(LYSP.getModelId());
        if (CollectionUtils.isEmpty(models)) {
            return;
        }
        for (PlusProModelEntity model : models) {
            reloadPlusProductToCache(model.getProgramId());
        }
    }


    /**
     * 重新加载会员商品缓存
     * <p>目前是0元商品在用</p>
     */
    public void reloadPlusProductToCache(Integer programId) {
        try {
            // 获取方案下已上架的0元商品配置信息
            List<PlusProgramProductNewEntity> productNews = plusProgramProductRepository.getProductNewByProgramAndModel(
                    programId, LYSP.getModelId());
            // 按商品分类分组
            Map<Integer, List<PlusProgramProductNewEntity>> map = productNews.stream()
                    .collect(Collectors.groupingBy(PlusProgramProductNewEntity::getTypeId));
            List<PlusProductTypeEntity> lygPlusProductList = new ArrayList<>();
            for (Entry<Integer, List<PlusProgramProductNewEntity>> entry : map.entrySet()) {
                List<PlusProductEntity> plusProductDtoList = new ArrayList<>();
                Integer typeId = entry.getKey();
                // 获取分类信息
                PlusProgramProductTypeEntity typeInfo = plusProgramProductRepository.getProductTypeById(
                        typeId);
                PlusProductTypeEntity plusProductTypeDto = new PlusProductTypeEntity();
                plusProductTypeDto.setId(typeId);
                plusProductTypeDto.setSort(typeInfo.getRankNum());
                plusProductTypeDto.setTypeLimitNum(typeInfo.getTypeLimitNum());
                plusProductTypeDto.setTypeLimit(typeInfo.getTypeLimit());
                plusProductTypeDto.setNumLimit(typeInfo.getNumLimit());
                plusProductTypeDto.setTypeName(typeInfo.getTypeName());
                List<PlusProgramProductNewEntity> news = map.get(typeId);
                if (!CollectionUtils.isEmpty(news)) {
                    // 批量获取商品信息
                    List<Integer> productIdList = news.stream()
                            .map(PlusProgramProductNewEntity::getProductId)
                            .collect(Collectors.toList());
                    List<Product> productList = productExternalRepositoryAcl.getProductByIds(
                            productIdList);
                    // 按商品id分组
                    Map<Integer, List<Product>> productMap = productList.stream()
                            .collect(Collectors.groupingBy(Product::getId));
                    for (PlusProgramProductNewEntity productNew : news) {
                        List<Product> products = productMap.get(productNew.getProductId());
                        if (!CollectionUtils.isEmpty(products)) {
                            Product product = products.get(0);
                            PlusProductEntity plusProductDto = new PlusProductEntity();
                            plusProductDto.setProductId(product.getId());
                            plusProductDto.setProductImage(productNew.getProductImage());
                            plusProductDto.setProductSku(productNew.getProductSku());
                            plusProductDto.setProductName(product.getName1());
                            plusProductDto.setSellPrice(product.getMallMobilePrice()
                                    .multiply(productNew.getDiscountRate())
                                    .setScale(2, RoundingMode.DOWN));
                            plusProductDto.setMarketPrice(product.getMarketPrice());
                            plusProductDtoList.add(plusProductDto);
                        }
                    }
                }
                plusProductTypeDto.setProductDtoList(plusProductDtoList);
                lygPlusProductList.add(plusProductTypeDto);
            }
            // 升序排
            lygPlusProductList.sort(Comparator.comparing(PlusProductTypeEntity::getSort));
            if (!CollectionUtils.isEmpty(lygPlusProductList)) {
                String key = PROGRAM_PLUS_PRODUCTS + programId + "_" + LYSP.getModelId();
                redisUtils.set(key, JSON.toJSONString(lygPlusProductList));
            }
        } catch (Exception e) {
            LogUtil.printLog(e, "缓存0元商品方案配置异常");
        }
    }

    /**
     * 将方案的一元购商品加入缓存
     */
    public void reloadYygProductToCache(Integer programId) {
        //获取方案关联的一元购商品id
        List<Integer> productIdList = plusProgramProductRepository.yygProductIdList(programId,
                PlusModelEnum.YYG.getModelId());
        //获取商品信息
        List<Product> productByIds = productExternalRepositoryAcl.getProductByIds(productIdList);
        if (!org.springframework.util.CollectionUtils.isEmpty(productByIds)) {
            List<PlusYygProductEntity> list = new ArrayList<>();
            productByIds.forEach(product -> {
                PlusYygProductEntity entity = new PlusYygProductEntity();
                BeanUtils.copyProperties(product, entity);
                list.add(entity);
            });
            String redisKey = RedisConstantPrefix.PROGRAM_ONE_PRODUCTS_KEY + programId;
            log.info("将方案的一元购商品加入缓存,redis key:{}", redisKey);
            redisUtils.set(redisKey, JSON.toJSONString(list));
        }
    }

    /**
     * 重新加载权益列表缓存
     */
    public void reloadVirtualCache(Integer programId) {
        //刷新会员虚拟商品权益列表缓存
        log.info("刷新会员虚拟商品权益列表缓存开始，programId：{}", programId);
        // 生活权益
        String redisKey = RedisConstantPrefix.PROGRAM_VIRTUAL_PRODUCTS_NEW + programId;
        if (redisUtils.hasKey(redisKey)) {
            redisUtils.delete(redisKey);
        }// todo 会员权益改版-虚拟商品缓存
        List<PlusProgramVirtualTypeEntity> virtuals = doGetVirtualProducts(programId);
        if (CollectionUtils.isEmpty(virtuals)) {
            log.info("刷新会员虚拟商品权益列表缓存数据为空，programId：{}", programId);
            return;
        }
        //放入缓存
        redisUtils.set(redisKey, JSON.toJSONString(virtuals));
        log.info("刷新会员虚拟商品权益列表缓存结束，programId：{}", programId);
    }

    /**
     * 通过查询获取虚拟商品信息
     */
    public List<PlusProgramVirtualTypeEntity> doGetVirtualProducts(Integer programId) {
        //获取方案下的分类数据
        //huxf 2023.11.20 增加权益ID入参
        List<PlusProgramVirtualTypeEntity> virtualTypes = plusProgramVirtualRepository.getVirtualTypeByProgram(
                programId, PlusModelEnum.SHQY.getModelId(), 1);
        log.info("数据库获取方案下关联分类数据返回:{}", JSON.toJSONString(virtualTypes));
        if (CollectionUtils.isEmpty(virtualTypes)) {
            return null;
        }
        //获取分类下的商品信息
        virtualTypes.forEach(type -> {
            type.setProductsInfo(getVirtualProducts(type));
        });
        log.info("通过查询方式获取虚拟商品信息:{}", JSON.toJSONString(virtualTypes));
        return virtualTypes;
    }

    /**
     * 重新加载多级分类生活权益列表缓存-按权益id
     */
    public void reloadVirtualProfitCacheByModelId(Integer programId, Integer modelId) {
        // 刷新会员生活权益虚拟商品权益列表缓存
        log.info("刷新会员多级分类虚拟商品权益列表缓存开始，programId：{} modelId:{}", programId,
                modelId);
        // 多级生活权益
        String redisKey =
                RedisConstantPrefix.PROGRAM_VIRTUAL_PRODUCTS_LEVEL + programId + "_" + modelId;
        if (redisUtils.hasKey(redisKey)) {
            redisUtils.delete(redisKey);
        }
        //获取方案下的所有分类数据 然后再进行分组排序
        List<PlusProgramVirtualTypeEntity> virtualTypelist = plusProgramVirtualRepository.getVirtualTypeByProgram(
                programId, modelId, null);
        log.info("数据库获取方案下关联所有分类数据返回:{}", JSON.toJSONString(virtualTypelist));
        List<PlusProgramVirtualTypeProfitLevelEntity> result = new ArrayList<>();
        if (!CollectionUtils.isEmpty(virtualTypelist)) {
            // 分类处理
            List<PlusProgramVirtualTypeEntity> list = virtualTypelist.stream()
                    .filter(v -> v.getTypeLevel().equals(1)).collect(Collectors.toList());
            // 排序
            list.sort(Comparator.comparing(PlusProgramVirtualTypeEntity::getRankNum));
            if (!CollectionUtils.isEmpty(list)) {
                // 按父分类ID分组
                Map<Integer, List<PlusProgramVirtualTypeEntity>> map = virtualTypelist.stream()
                        .filter(v -> v.getParentId() != null && v.getParentId() > 0)
                        .collect(Collectors.groupingBy(PlusProgramVirtualTypeEntity::getParentId));
                list.forEach(v -> {
                    PlusProgramVirtualTypeProfitLevelEntity profitVo = converter.toPlusProgramVirtualTypeProfitLevelEntity(
                            v);
                    result.add(profitVo);
                    if (map != null) {
                        List<PlusProgramVirtualTypeEntity> subList = map.get(v.getId());
                        if (!CollectionUtils.isEmpty(subList)) {
                            profitVo.setVirtualTypeVos(new ArrayList<>());
                            // 排序
                            subList.sort(
                                    Comparator.comparing(PlusProgramVirtualTypeEntity::getRankNum));
                            // 获取分类下的商品信息
                            subList.forEach(type -> {
                                PlusProgramVirtualTypeProfitLevelEntity profitItemVo = converter.toPlusProgramVirtualTypeProfitLevelEntity(
                                        type);
                                profitVo.getVirtualTypeVos().add(profitItemVo);
                                profitItemVo.setProductsInfo(getVirtualProducts(type));
                            });
                        }
                    }
                });
                log.info("通过查询方式获取分类虚拟商品信息:{}", JSON.toJSONString(result));
            }
        }
        if (CollectionUtils.isEmpty(result)) {
            log.info("刷新会员多级分类虚拟商品权益列表缓存数据为空，programId：{} modelId:{}",
                    programId, modelId);
            return;
        }
        //放入缓存
        redisUtils.set(redisKey, JSON.toJSONString(result));
        log.info("刷新会员多级分类虚拟商品权益列表缓存结束，programId：{} modelId:{}", programId,
                modelId);
    }

    /**
     * 通过分类ID查询虚拟商品信息
     */
    public List<PlusProgramVirtualEntity> getVirtualProducts(
            PlusProgramVirtualTypeEntity virtualTypeVo) {
        //获取分类下的商品信息
        List<PlusProgramVirtualEntity> products = plusProgramVirtualRepository.getVirtualByType(
                virtualTypeVo.getId());
        if (!CollectionUtils.isEmpty(products)) {
            //获取商品id
            List<Integer> productIds = products.stream().map(PlusProgramVirtualEntity::getProductId)
                    .collect(Collectors.toList());
            List<Product> productByIds = productExternalRepositoryAcl.getProductByIds(productIds);
            if (productByIds == null || CollectionUtils.isEmpty(productIds)) {
                return null;
            }
            //处理商品价格
            Map<Integer, Product> productMap = new HashMap<>(32);
            productByIds.forEach(product -> productMap.put(product.getId(), product));
            products.forEach(virtual -> {
                Product product = productMap.get(virtual.getProductId());
                if (product != null) {
                    virtual.setProductName(product.getName1());
                    virtual.setMarketPrice(product.getMarketPrice());
                    virtual.setDiscountPrice(
                            product.getMallMobilePrice().multiply(virtual.getDiscountRate())
                                    .setScale(2, RoundingMode.DOWN));
                }
            });
        }
        return products;
    }

    public void optPreCheck(CreateVirtualTypeEvent event, boolean numNotNull) {
        if (Objects.isNull(event.getNumLimit())) {
            throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
        }
        //限制购买次数 需要限制类型的数量
        if (event.getNumLimit() != 0) {
            if (Objects.isNull(event.getTypeLimit())) {
                throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
            }
            // 限制分类下权益种类数量 需要限制的数量
            if (event.getTypeLimit() != 0 && (numNotNull && Objects.isNull(
                    event.getTypeLimitNum()))) {
                throw new PlusAbyssException(VipErrorEnum.ERROR_100002);
            }
        } else {
            event.setTypeLimit(0);
        }
    }

    /**
     * 刷新权益0元发放虚拟商品列表缓存
     */
    public void refreshLyffVirtualGoodsCache(Integer programId) {
        log.info("刷新权益0元发放虚拟商品列表缓存开始，programId：{}", programId);
        // 生活权益
        String redisKey = String.format(RedisConstantPrefix.PROGRAM_VIRTUAL_GOODS, programId,
                PlusModelEnum.LYFF.getModelId());
        List<PlusProgramVirtualEntity> virtualList = plusProgramVirtualRepository.getByProgramIdAndModelId(
                programId, PlusModelEnum.LYFF.getModelId());
        if (CollectionUtils.isEmpty(virtualList)) {
            redisUtils.delete(redisKey);
            log.info("刷新权益0元发放虚拟商品列表为空，programId：{}", programId);
            return;
        }
        // 放入缓存
        redisUtils.set(redisKey, JSON.toJSONString(virtualList));
        log.info("刷新权益0元发放虚拟商品列表缓存结束，programId：{}", programId);
    }


    /**
     * 转换半价商品信息
     */
    public Map<Integer, ProductPo> convertProductForHalfPrice(List<Product> list) {
        Map<Integer, ProductPo> resProductInfo = new HashMap<>();
        list.forEach(product -> {
            if (product.getTagFall() == null) {
                product.setTagFree("");
            }
            if (product.getIsSelf() != null && product.getIsSelf() == 2) {
                //免息
                product.setTagFree("");
            } else {
                String tagFree = productExternalRepository.getTagFreeByProductId(product);
                //免息
                product.setTagFree(tagFree);
            }
            product.setMasterLittleImg(product.getMasterImg());
            //最小月供
            String fqfl = productExternalRepository.getMaxPeriodInformation(product);
            if (!isNull(fqfl)) {
                MonthPaymentUtil mp = new MonthPaymentUtil();
                product.setProductPaymentMinInstallment(
                        mp.trial(Integer.valueOf(fqfl.split("_")[0]),
                                product.getMallMobilePrice().toString(), fqfl.split("_")[2]));
            } else {
                //最小月供
                product.setProductPaymentMinInstallment("");
            }
            ProductPo productDto = new ProductPo();
            BeanUtils.copyProperties(product, productDto);
            productDto.setMalMobilePrice(product.getMallMobilePrice());
            resProductInfo.put(productDto.getId(), productDto);
        });
        log.info("设置商品属性返回：{}", JSON.toJSONString(resProductInfo));
        return resProductInfo;
    }

    /**
     * 重新加载方案的基础权益到缓存
     *
     * <AUTHOR>
     * @date 2020/12/22 2:56 下午
     */
    public void reloadBasicToCache(List<LandModelBasicDetailEntity> basicInfoVos,
            List<LandModelBasicDetailEntity> resultBasic, Integer programId, Integer configId) {
        String redisKey = RedisConstantPrefix.PROGRAM_BASIC_PROFIT_KEY + programId;
        // 20230510 子轩联名卡权益不落data缓存
        for (LandModelBasicDetailEntity basicInfoVo : basicInfoVos) {
            if (basicInfoVo.getModelId() != PlusModelEnum.HYTE.getModelId()
                    && basicInfoVo.getModelId() != PlusModelEnum.ZXTE.getModelId()
                    && basicInfoVo.getModelId() != PlusModelEnum.LMQY.getModelId()
                    && basicInfoVo.getModelId() != PlusModelEnum.LYSP.getModelId()) {
                int modelId = basicInfoVo.getModelId();
                int baseProgramId = basicInfoVo.getProgramId();
                basicInfoVo.setShortPY(PlusModelEnum.getShortPYById(modelId));
                HandleProfitLandEvent vo = new HandleProfitLandEvent();
                vo.setProgramId(baseProgramId);
                vo.setModelId(modelId);
                vo.setConfigId(configId);
                basicInfoVo.setData(profitHandlerContext.getProfitForOpen(vo));
                resultBasic.add(basicInfoVo);
            }
        }
        if (!CollectionUtils.isEmpty(resultBasic)) {
            log.info("将方案基础权益信息设置到缓存redisKey={}", redisKey);
            redisUtils.set(redisKey, JSON.toJSONString(resultBasic));
        }
    }

    /**
     * 刷新还款返现配置缓存
     */
    public void refreshRepayCashBackConfigCache(Integer programId) {
        log.info("刷新还款返现配置列表缓存开始，programId：{}", programId);
        // 还款返现
        String redisKey = String.format(RedisConstantPrefix.PROGRAM_REPAY_CASH_BACK_CONFIG,
                programId);
        // 查询数据
        List<PlusProgramCashbackEntity> repayList = plusCashbackRepository.getByParam(
                JuziPlusEnum.RDZX_CARD.getCode(), programId, PlusModelEnum.HKFX.getModelId());
        if (CollectionUtils.isEmpty(repayList)) {
            redisUtils.delete(redisKey);
            log.info("刷新还款返现配置列表缓存为空，programId：{}", programId);
            return;
        }
        // 放入缓存
        redisUtils.set(redisKey, JSON.toJSONString(repayList));
        log.info("刷新还款返现配置列表缓存结束，programId：{}", programId);
    }

}
