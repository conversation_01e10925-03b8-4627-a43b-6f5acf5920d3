package com.juzifenqi.plus.module.order.model.impl;

import com.alibaba.fastjson.JSON;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.admin.OrderBillQueryReq;
import com.juzifenqi.plus.enums.PayStateEnum;
import com.juzifenqi.plus.enums.PayTypeEnum;
import com.juzifenqi.plus.enums.PlusCancelTypeEnum;
import com.juzifenqi.plus.enums.PlusOrderStateEnum;
import com.juzifenqi.plus.enums.SupplierEnum;
import com.juzifenqi.plus.enums.bill.ContractUploadStateEnum;
import com.juzifenqi.plus.enums.bill.InStateEnum;
import com.juzifenqi.plus.enums.bill.OutStateEnum;
import com.juzifenqi.plus.enums.bill.SignStateEnum;
import com.juzifenqi.plus.enums.bill.TrailStateEnum;
import com.juzifenqi.plus.enums.pay.PayProductCodeEnum;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.module.common.entity.TraderInfoRespEntity;
import com.juzifenqi.plus.module.order.adapter.IPlusOrderAdapter;
import com.juzifenqi.plus.module.order.model.IPlusContractUploadModel;
import com.juzifenqi.plus.module.order.model.IPlusOrderBillModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderBillRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderContractRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderShuntRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.OrderPayResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.ShuntNotifyEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.ShuntNotifyResultEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.PayCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.event.order.CreateOrderBillEvent;
import com.juzifenqi.plus.module.order.model.event.order.OrderOutcomeNotifyEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderContractEvent;
import com.juzifenqi.plus.module.order.model.impl.strategy.bill.ShuntHandlerContext;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderConverter;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo;
import com.juzifenqi.plus.utils.RedisLock;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * 订单对账
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 10:34
 */
@Slf4j
@Service
public class PlusOrderBillModelImpl implements IPlusOrderBillModel {

    private final IPlusOrderConverter converter = IPlusOrderConverter.instance;

    @Autowired
    private IPlusOrderBillRepository     billRepository;
    @Autowired
    private IPlusOrderShuntRepository    shuntRepository;
    @Autowired
    private IPlusOrderRepository         plusOrderRepository;
    @Autowired
    private ShuntHandlerContext          handlerContext;
    @Autowired
    private IPlusOrderContractRepository contractRepository;
    @Autowired
    private IPlusContractUploadModel     uploadModel;
    @Autowired
    private RedisLock                    redisLock;
    @Autowired
    private ConfigProperties             configProperties;
    @Autowired
    private IPlusOrderAdapter            plusOrderAdapter;
    @Autowired
    private IPlusShuntRepository         plusShuntRepository;

    @Override
    public void saveOrderBill(CreateOrderBillEvent event) {
        try {
            log.info("保存会员对账记录开始：{}", JSON.toJSONString(event));
            String plusOrderSn = event.getPlusOrderSn();
            Integer configId = event.getConfigId();
            if (!PlusConstant.SHUNT_CARD_LIST.contains(configId)) {
                log.info("保存对账信息，会员类型不匹配，不处理：{}，{}", configId, plusOrderSn);
                return;
            }
            PlusOrderShuntPo shunt = shuntRepository.getByOrderSn(plusOrderSn);
            if (shunt == null || shunt.getPlanInSupplier() == null) {
                log.info("保存对账信息，计划入账方为空，不处理：{}", plusOrderSn);
                return;
            }
            PlusOrderBillEntity orderBill = billRepository.getByOrderSnAndSerialNumber(plusOrderSn, event.getSerialNumber());
            if (orderBill != null) {
                log.info("保存会员对账记录，已存在对账记录，不处理：{}", plusOrderSn);
                return;
            }
            // 使用订单号加锁，避免划扣和主动支付时生成双份订单对账数据
            String key = RedisConstantPrefix.PLUS_INSERT_ORDER_BILL + plusOrderSn;
            // 对账信息只能存在一份数据，出现加锁失败时，意味着已经有对账数据在保存中了
            if (!redisLock.lock(key, "1", 5)) {
                log.info("保存会员对账信息，加锁失败，不处理：{}", plusOrderSn);
                return;
            }
            // 新增记录
            orderBill = converter.toPlusOrderBillEntity(shunt, event.getProgramId());
            billRepository.saveOrderBill(orderBill);
            // 更新分流记录表的实际入账方
            shunt.setInSupplier(orderBill.getInSupplier());
            shuntRepository.update(shunt);
        } catch (Exception e) {
            LogUtil.printLog(e, "保存会员订单对账信息异常");
        }
    }

    @Override
    public void outcomeNotify(OrderOutcomeNotifyEvent event) {
        String orderSn = event.getPlusOrderSn();
        try {
            log.info("出账通知开始：{}", JSON.toJSONString(event));
            Integer refundType = event.getRefundType();
            PlusOrderBillEntity orderBill = billRepository.getByOrderSn(orderSn);
            if (orderBill == null) {
                log.info("出账通知，未查询到对账记录：{}", orderSn);
                return;
            }
            // 未入账成功，不做处理
            if (orderBill.getInState() == null
                    || InStateEnum.IN_SUCCESS.getCode() != orderBill.getInState()) {
                log.info("出账通知，记录未入账成功，不处理：{}", orderSn);
                return;
            }
            // 已经出账成功，不做处理
            if (OutStateEnum.OUT_STATE_1.getCode().equals(orderBill.getOutState())) {
                log.info("出账通知，记录已出账无需重复处理：{}", orderSn);
                return;
            }
            // 流转状态：退款失败，出账方不记录
            if (event.getRefundState() == CommonConstant.TWO) {
                orderBill.setTrailState(TrailStateEnum.REFUND_FAIL.getCode());
                orderBill.setOutRemark("退款失败，不通知三方出账");
                billRepository.updatePlusOrderBill(orderBill);
                log.info("出账通知，退款失败设置出账状态，不需要通知三方出账，流程结束：{}", orderSn);
                return;
            }
            // 出账方
            if (refundType == CommonConstant.ONE) {
                // 20240705 zjf 代付取消：非黑卡，出账方=实际入账方
                if (orderBill.getInSupplier() != SupplierEnum.XSHK.getCode()) {
                    orderBill.setOutSupplier(orderBill.getInSupplier());
                    log.info("出账通知，非黑卡代付取消出账方设置为三方，code为：{}",
                            SupplierEnum.NY.getCode());
                } else {
                    orderBill.setOutSupplier(configProperties.defaultSupplierId);
                    log.info("出账通知，黑卡代付取消出账方设置为兜底主体，code为：{}",
                            configProperties.defaultSupplierId);
                }
            } else {
                orderBill.setOutSupplier(orderBill.getInSupplier());
                log.info("出账通知，原路退回出账方设置为入账方，code为：{}",
                        orderBill.getOutSupplier());
            }
            // 流转状态：成功
            orderBill.setTrailState(TrailStateEnum.REFUND_SUCCESS.getCode());
            orderBill.setOutAmount(event.getRefundAmount());
            log.info("出账通知，退款成功设置流转状态：{}", orderSn);
            outcomeNotifyExecute(orderBill, event.getRefundAmount());
            billRepository.updatePlusOrderBill(orderBill);
            log.info("出账通知结束：{}", orderSn);
        } catch (Exception e) {
            LogUtil.printLog(e, "出账通知异常");
        }
    }

    @Override
    public void outcomeRetry(Integer size) {
        size = size == null || size == 0 ? 50 : size;
        log.info("出账通知重试开始，数量：{}", size);
        List<PlusOrderBillEntity> plusOrderBills = billRepository.getOutComeRetryList(size);
        if (CollectionUtils.isEmpty(plusOrderBills)) {
            log.info("出账通知重试列表为空");
            return;
        }
        // 批量修改为重试中
        List<Integer> ids = plusOrderBills.stream().map(PlusOrderBillEntity::getId)
                .collect(Collectors.toList());
        billRepository.updateOutStateBatch(ids, OutStateEnum.OUT_STATE_3.getCode());
        for (PlusOrderBillEntity orderBill : plusOrderBills) {
            String orderId = orderBill.getOrderSn();
            try {
                log.info("处理单个出账通知重试数据开始，单号：{}", orderId);
                // 通知三方出账
                outcomeNotifyExecute(orderBill, orderBill.getOutAmount());
                // 重试次数+1
                orderBill.setOutRetryCount(orderBill.getInRetryCount() + 1);
                billRepository.updatePlusOrderBill(orderBill);
                log.info("处理单个出账通知重试数据完成，单号：{}", orderId);
            } catch (Exception e) {
                LogUtil.printLog(e, "处理单个出账通知重试数据异常: " + orderId);
            }
        }
        log.info("出账通知重试完成，数量：{}", size);
    }

    @Override
    public void incomeNotify(PayCallbackEntity entity) {
        try {
            String orderId = entity.getOrderId();
            log.info("会员订单划扣成功三方预入账开始：{}", orderId);
            PlusOrderBillEntity orderBill = billRepository.getByOrderSn(orderId);
            if (orderBill == null) {
                log.info("保存预入账信息，未查询到对账记录：{}", orderId);
                return;
            }
            // 已经入账成功，不做处理
            if (orderBill.getInState() != null
                    && InStateEnum.IN_SUCCESS.getCode() == orderBill.getInState()) {
                log.info("保存预入账信息，记录已入账无需重复处理：{}", orderId);
                return;
            }
            // 流转状态：成功/失败
            orderBill.setTrailState(PayStateEnum.S.getCode().equals(entity.getStatus())
                    ? TrailStateEnum.DUCT_SUCCESS.getCode() : TrailStateEnum.DUCT_FAIL.getCode());
            // 划扣成功才发起签章，记录流水号，入账金额，占用额度和笔数
            if (orderBill.getTrailState() == TrailStateEnum.DUCT_SUCCESS.getCode()) {
                // 支付流水号
                if (StringUtils.isNotBlank(entity.getSerialNumber())) {
                    orderBill.setSerialNumber(entity.getSerialNumber());
                }
                // 入账金额
                if (entity.getAmount() != null) {
                    orderBill.setInAmount(entity.getAmount().divide(new BigDecimal("100")));
                }
                // 增加分流实际额度占用和订单笔数占用
                shuntRepository.addShuntOrderCount(orderBill.getInSupplier(),
                        orderBill.getInAmount(), orderId);
                // 增加分流主体渠道业务场景当日金额
                try {
                    String businessScene = plusShuntRepository.getBusinessScene(orderId);
                    shuntRepository.addShuntOrderChannelSceneAmount(orderBill.getInSupplier(),
                            orderBill.getChannelId(), businessScene, orderBill.getInAmount(), orderId);
                } catch (Exception e) {
                    log.warn("增加分流主体渠道业务场景当日金额异常：{}", orderId, e);
                }
                // 2040705 zjf 只有黑卡划扣成功才需要签署合同
                if (orderBill.getInSupplier() == SupplierEnum.XSHK.getCode()) {
                    try {
                        log.info("预入账-黑卡分流-签署合同开始：{}", orderId);
                        //发起合同签章
                        PlusOrderShuntPo plusOrderShunt = shuntRepository.getByOrderSn(orderId);
                        log.info("保存预入账信息-获取分流信息：{}",
                                JSON.toJSONString(plusOrderShunt));
                        if (plusOrderShunt == null || StringUtils.isEmpty(
                                plusOrderShunt.getContractNo())) {
                            log.info("保存预入账信息-未获取到分流信息或合同为空：{}", orderId);
                            // 这种情况不用处理签章状态
                            billRepository.updatePlusOrderBill(orderBill);
                            return;
                        }
                        PlusOrderContractEvent event = converter.toPlusOrderContractEvent(
                                plusOrderShunt);
                        contractRepository.signThirdPartyContract(event);
                        //修改合同状态-已发起签章
                        orderBill.setSignState(SignStateEnum.SIGN_STATE_2.getCode());
                    } catch (Exception e) {
                        log.info("保存预入账信息-通知生成合同异常：orderId:{}", orderId, e);
                        //修改合同状态-签章失败
                        orderBill.setSignState(SignStateEnum.SIGN_STATE_4.getCode());
                    }
                } else {
                    log.info("预入账-非黑卡分流-入账开始：{}", orderId);
                    this.incomeExecute(orderBill, orderBill.getSerialNumber());
                }
            }
            billRepository.updatePlusOrderBill(orderBill);
            log.info("会员订单划扣成功三方预入账结束：{}", orderId);
        } catch (Exception e) {
            LogUtil.printLog(e, "会员订单划扣成功三方预入账异常");
        }
    }

    @Override
    public void incomeRetry(Integer size) {
        size = size == null || size == 0 ? 50 : size;
        log.info("入账通知重试开始，数量：{}", size);
        List<PlusOrderBillEntity> plusOrderBills = billRepository.getInComeRetryList(size);
        if (CollectionUtils.isEmpty(plusOrderBills)) {
            log.info("入账通知重试列表为空");
            return;
        }
        // 批量修改为重试中
        List<Integer> ids = plusOrderBills.stream().map(PlusOrderBillEntity::getId)
                .collect(Collectors.toList());
        billRepository.updateInStateBatch(ids, InStateEnum.IN_RETRY.getCode());
        for (PlusOrderBillEntity orderBill : plusOrderBills) {
            String orderId = orderBill.getOrderSn();
            try {
                log.info("处理单个入账通知重试数据开始，单号：{}", orderId);
                // 通知三方入账
                incomeExecute(orderBill, orderBill.getSerialNumber());
                // 重试次数+1
                orderBill.setInRetryCount(orderBill.getInRetryCount() + 1);
                billRepository.updatePlusOrderBill(orderBill);
                log.info("处理单个入账通知重试数据完成，单号：{}", orderId);
            } catch (Exception e) {
                LogUtil.printLog(e, "处理单个入账通知重试数据异常: " + orderId);
            }
        }
        log.info("入账通知重试完成，数量：{}", size);
    }

    @Override
    public void incomeExecute(PlusOrderBillEntity orderBill, String serialNumber) {
        String orderId = orderBill.getOrderSn();
        // 入账防重提key
        String lockKey = RedisConstantPrefix.PLUS_INCOME_LOCK + orderId;
        try {
            log.info("通知三方入账开始：orderId：{}", orderId);
            // 20240707 zjf 诺壹分流：只有黑卡入账才校验支付状态
            if (orderBill.getInSupplier() != null
                    && orderBill.getInSupplier() == SupplierEnum.XSHK.getCode()) {
                // 订单是否支付成功，可能在入账重试/合同上传重试/合同签章结果回调过程中，用户取消了订单
                PlusOrderEntity orderInfo = plusOrderRepository.getByPlusOrderSn(orderId);
                if (orderInfo == null || orderInfo.getOrderState() == null
                        || PlusOrderStateEnum.PAY_SUCCESS.getCode() != orderInfo.getOrderState()) {
                    log.info("通知三方入账，会员订单查询为空或非支付成功状态：{}", orderId);
                    orderBill.setInState(InStateEnum.IN_FAIL.getCode());
                    orderBill.setInRemark("订单为空或非支付成功状态，不通知三方入账");
                    return;
                }
            }
            // 20230825 zjf 入账防重提
            if (!redisLock.lock(lockKey, "1", 30)) {
                log.info("通知三方入账，短时间内重复入账：{}", orderId);
                return;
            }
            // 20230825 zjf 重复入账校验
            if (orderBill.getInState() != null
                    && orderBill.getInState() == InStateEnum.IN_SUCCESS.getCode()) {
                log.info("通知三方入账，对账信息已经入账成功，无需重复入账：{}", orderId);
                return;
            }
            // 构建通知参数
            ShuntNotifyEntity notifyDto = new ShuntNotifyEntity(orderBill.getUserId(),
                    orderBill.getOrderSn(), orderBill.getChannelId(), serialNumber,
                    String.valueOf(orderBill.getInAmount()), orderBill.getInSupplier(),
                    orderBill.getConfigId());
            // 通知三方入账
            ShuntNotifyResultEntity shuntNotifyVo = handlerContext.incomeNotify(notifyDto);
            log.info("通知三方入账结果：{}", JSON.toJSONString(shuntNotifyVo));
            if (shuntNotifyVo == null) {
                orderBill.setInRemark("入账成功");
                orderBill.setInState(InStateEnum.IN_SUCCESS.getCode());
                orderBill.setInTime(new Date());
                log.info("通知三方入账策略为空，默认入账成功：{}", orderId);
                return;
            }
            if (shuntNotifyVo.getNotifyState()) {
                orderBill.setInRemark("入账成功");
                // 更新通知状态
                orderBill.setInState(InStateEnum.IN_SUCCESS.getCode());
                orderBill.setInTime(new Date());
                log.info("通知三方入账成功：{}", orderId);
            } else {
                if (shuntNotifyVo.getFailCode() == CommonConstant.ZERO) {
                    log.info("通知三方入账，返回无需通知，不处理：{}", orderId);
                } else {
                    // 更新通知状态和通知失败原因
                    if (StringUtils.isNotBlank(shuntNotifyVo.getFailMessage())) {
                        if (shuntNotifyVo.getFailMessage().length() > 500) {
                            orderBill.setInRemark(shuntNotifyVo.getFailMessage().substring(0, 500));
                            log.info("通知三方入账，失败原因长度超过500：{}，{}",
                                    orderBill.getUserId(), shuntNotifyVo.getFailMessage());
                        } else {
                            orderBill.setInRemark(shuntNotifyVo.getFailMessage());
                        }
                    }
                    orderBill.setInState(InStateEnum.IN_FAIL.getCode());
                    log.info("通知三方入账失败：{}", orderId);
                }
            }
            log.info("通知三方入账完成：{}", orderId);
        } catch (Exception e) {
            orderBill.setInState(InStateEnum.IN_FAIL.getCode());
            LogUtil.printLog(e, "通知三方入参异常");
        } finally {
            redisLock.unLock(lockKey);
        }
    }

    /**
     * 通知三方入账
     */
    private void outcomeNotifyExecute(PlusOrderBillEntity orderBill, BigDecimal refundAmount) {
        String orderSn = orderBill.getOrderSn();
        try {
            // 构建通知参数（supplierId需要设置为入账方，比如黑卡入账的，取消类型是代付取消，上面出账方已经设置为桔多多，还是需要通知黑卡）
            ShuntNotifyEntity notifyDto = new ShuntNotifyEntity(orderBill.getUserId(),
                    orderBill.getOrderSn(), orderBill.getChannelId(), String.valueOf(refundAmount),
                    orderBill.getInSupplier(), orderBill.getConfigId(), orderBill.getOutSupplier());
            // 通知三方出账
            ShuntNotifyResultEntity shuntNotifyVo = handlerContext.outcomeNotify(notifyDto);
            log.info("通知三方出账结果：{}", JSON.toJSONString(shuntNotifyVo));
            if (shuntNotifyVo == null) {
                orderBill.setOutRemark("出账成功");
                orderBill.setOutState(OutStateEnum.OUT_STATE_1.getCode());
                orderBill.setOutTime(new Date());
                // 恢复下单日期为当天的订单：实际占用的总订单笔数和金额
                PlusOrderEntity plusOrder = plusOrderRepository.getByPlusOrderSn(orderSn);
                shuntRepository.revertShuntAmountAndCount(plusOrder, orderBill);
                // 恢复分流主体渠道业务场景当日金额
                try {
                    String businessScene = plusShuntRepository.getBusinessScene(orderSn);
                    shuntRepository.revertShuntOrderChannelSceneAmount(orderBill.getInSupplier(),
                            orderBill.getChannelId(), businessScene, orderBill.getOutAmount(), orderSn);
                } catch (Exception e) {
                    log.warn("恢复分流主体渠道业务场景当日金额异常：{}", orderSn, e);
                }
                log.info("通知三方出账策略为空，默认出账成功：{}", orderSn);
                return;
            }
            if (shuntNotifyVo.getNotifyState()) {
                orderBill.setOutRemark("出账成功");
                // 更新通知状态
                orderBill.setOutState(OutStateEnum.OUT_STATE_1.getCode());
                orderBill.setOutTime(new Date());
                log.info("通知三方出账成功：{}", orderSn);
                // 恢复下单日期为当天的订单：实际占用的总订单笔数和金额
                PlusOrderEntity plusOrder = plusOrderRepository.getByPlusOrderSn(orderSn);
                shuntRepository.revertShuntAmountAndCount(plusOrder, orderBill);
                // 恢复分流主体渠道业务场景当日金额
                try {
                    String businessScene = plusShuntRepository.getBusinessScene(orderSn);
                    shuntRepository.revertShuntOrderChannelSceneAmount(orderBill.getInSupplier(),
                            orderBill.getChannelId(), businessScene, orderBill.getOutAmount(), orderSn);
                } catch (Exception e) {
                    log.warn("恢复分流主体渠道业务场景当日金额异常：{}", orderSn, e);
                }
            } else {
                if (shuntNotifyVo.getFailCode() == CommonConstant.ZERO) {
                    log.info("通知三方出账，返回无需通知，不处理：{}", orderSn);
                } else {
                    // 更新通知状态
                    if (StringUtils.isNotBlank(shuntNotifyVo.getFailMessage())) {
                        if (shuntNotifyVo.getFailMessage().length() > 500) {
                            orderBill.setOutRemark(
                                    shuntNotifyVo.getFailMessage().substring(0, 500));
                            log.info("通知三方出账，失败原因长度超过500：{}，{}",
                                    orderBill.getUserId(), shuntNotifyVo.getFailMessage());
                        } else {
                            orderBill.setOutRemark(shuntNotifyVo.getFailMessage());
                        }
                    }
                    orderBill.setOutState(OutStateEnum.OUT_STATE_2.getCode());
                    log.info("通知三方出账失败：{}", orderSn);
                }
            }
            log.info("通知三方出账完成：{}", orderSn);
        } catch (Exception e) {
            orderBill.setOutState(OutStateEnum.OUT_STATE_2.getCode());
            LogUtil.printLog(e, "通知三方出账异常");
        }
    }

    /**
     * 分页查询Data 只查询入账成功的
     */
    @Override
    public List<PlusOrderBillEntity> pageList(OrderBillQueryReq req) {
        return billRepository.pageList(req);
    }

    /**
     * 分页查询Count 只查询入账成功的
     */
    @Override
    public Integer pageListCount(OrderBillQueryReq req) {
        return billRepository.pageListCount(req);
    }

    @Override
    public PlusOrderBillEntity getByOrderSn(String orderSn) {
        return billRepository.getByOrderSn(orderSn);
    }

    @Override
    public void update(PlusOrderBillEntity entity) {
        billRepository.updatePlusOrderBill(entity);
    }

    @Override
    public void contractSignReTry(Integer size) {
        size = size == null || size == 0 ? 50 : size;
        log.info("黑卡合同生成重试开始，数量：{}", size);
        List<PlusOrderBillEntity> plusOrderBills = billRepository.getContractReTryList(size);
        if (CollectionUtils.isEmpty(plusOrderBills)) {
            log.info("黑卡合同生成重试列表为空");
            return;
        }
        // 批量修改为-已发起
        List<Integer> ids = plusOrderBills.stream().map(PlusOrderBillEntity::getId)
                .collect(Collectors.toList());
        billRepository.updateContractStateBatch(ids, SignStateEnum.SIGN_STATE_2.getCode());
        for (PlusOrderBillEntity orderBill : plusOrderBills) {
            String orderId = orderBill.getOrderSn();
            try {
                try {
                    log.info("处理单个黑卡合同生成重试数据开始，单号：{}", orderId);
                    //发起合同签章
                    PlusOrderShuntPo plusOrderShunt = shuntRepository.getByOrderSn(orderId);
                    log.info("黑卡合同生成重试-获取分流信息：{}", JSON.toJSONString(plusOrderShunt));
                    if (plusOrderShunt == null || StringUtils.isEmpty(
                            plusOrderShunt.getContractNo())) {
                        log.info("黑卡合同生成重试-未获取到分流信息或合同为空：{}", orderId);
                        // 重试次数+1
                        orderBill.setSignRetryCount(orderBill.getSignRetryCount() + 1);
                        orderBill.setSignState(SignStateEnum.SIGN_STATE_4.getCode());
                        billRepository.updatePlusOrderBill(orderBill);
                        continue;
                    }
                    // 合同签署
                    PlusOrderContractEvent event = converter.toPlusOrderContractEvent(
                            plusOrderShunt);
                    contractRepository.signThirdPartyContract(event);
                    orderBill.setSignState(SignStateEnum.SIGN_STATE_2.getCode());
                } catch (Exception e) {
                    log.info("黑卡合同生成重试-通知生成合同异常：orderId:{}", orderId, e);
                    //修改合同状态-签章失败
                    orderBill.setSignState(SignStateEnum.SIGN_STATE_4.getCode());
                }
                // 重试次数+1
                orderBill.setSignRetryCount(orderBill.getSignRetryCount() + 1);
                billRepository.updatePlusOrderBill(orderBill);
                log.info("处理单个黑卡合同生成重试数据完成，单号：{}", orderId);
            } catch (Exception e) {
                LogUtil.printLog(e, "处理单个黑卡合同生成重试数据异常: " + orderId);
            }
        }
        log.info("黑卡合同生成重试完成，数量：{}", size);
    }

    @Override
    public void contractUploadReTry(Integer size) {
        size = size == null || size == 0 ? 50 : size;
        log.info("合同上传重试开始，数量：{}", size);
        List<PlusOrderBillEntity> plusOrderBills = billRepository.getContractUploadReTryList(size);
        if (CollectionUtils.isEmpty(plusOrderBills)) {
            log.info("合同上传重试列表为空");
            return;
        }
        // 批量修改为-已发起
        List<Integer> ids = plusOrderBills.stream().map(PlusOrderBillEntity::getId)
                .collect(Collectors.toList());
        billRepository.updateContractUploadStateBatch(ids,
                ContractUploadStateEnum.UPLOAD_ING.getCode());
        for (PlusOrderBillEntity orderBill : plusOrderBills) {
            String orderId = orderBill.getOrderSn();
            try {
                try {
                    log.info("处理单个合同上传重试数据开始，单号：{}", orderId);
                    // 获取分流记录信息
                    PlusOrderShuntPo plusOrderShunt = shuntRepository.getByOrderSn(orderId);
                    log.info("单个合同上传生成重试-获取分流信息：{}",
                            JSON.toJSONString(plusOrderShunt));
                    if (plusOrderShunt == null || StringUtils.isEmpty(
                            plusOrderShunt.getContractNo())) {
                        log.info("单个合同上传生成重试-未获取到分流信息或合同为空：{}", orderId);
                        // 重试次数+1
                        orderBill.setContractUploadState(
                                ContractUploadStateEnum.UPLOAD_FAIL.getCode());
                        orderBill.setContractUploadRetryCount(
                                orderBill.getContractUploadRetryCount() + 1);
                        billRepository.updatePlusOrderBill(orderBill);
                        continue;
                    }
                    // 合同上传
                    uploadModel.contractUpload(orderBill,
                            Arrays.asList(plusOrderShunt.getContractNo().split(",")), "重试");
                } catch (Exception e) {
                    log.info("合同上传重试-通知生成合同异常：orderId:{}", orderId, e);
                    //修改合同状态-签章失败
                    orderBill.setContractUploadState(ContractUploadStateEnum.UPLOAD_FAIL.getCode());
                }
                // 重试次数+1
                orderBill.setContractUploadRetryCount(orderBill.getContractUploadRetryCount() + 1);
                Integer contractUploadState = orderBill.getContractUploadState();
                if (Objects.equals(ContractUploadStateEnum.UPLOAD_SUCCESS.getCode(),
                        contractUploadState)) {
                    log.info("合同上传重试-上传成功，开始通知三方入账：{}", orderId);
                    //通知三方入账
                    incomeExecute(orderBill, orderBill.getSerialNumber());
                }
                billRepository.updatePlusOrderBill(orderBill);
                log.info("处理单个合同上传重试数据完成，单号：{}", orderId);
            } catch (Exception e) {
                LogUtil.printLog(e, "处理单个合同上传重试数据异常: " + orderId);
            }
        }
        log.info("合同上传重试完成，数量：{}", size);
    }

    @Override
    public void deductIncomeRetry(Integer limit) {
        log.info("划扣中数据重试入账开始：{}", limit);
        List<PlusOrderBillEntity> plusOrderBills = billRepository.selectDeductIncomeRetryList(
                limit);
        if (CollectionUtils.isEmpty(plusOrderBills)) {
            log.info("划扣中数据重试入账列表为空");
            return;
        }
        for (PlusOrderBillEntity plusOrderBill : plusOrderBills) {
            String orderSn = plusOrderBill.getOrderSn();
            try {
                log.info("开始重试划扣中单子：{}", orderSn);
                PlusOrderEntity orderInfo = plusOrderRepository.getByPlusOrderSn(orderSn);
                if (orderInfo == null
                        || orderInfo.getOrderState() != PlusOrderStateEnum.PAY_SUCCESS.getCode()) {
                    log.info("重试划扣中单子未查询到会员单/未支付成功：{}", orderSn);
                    // 支付成功过，则 入账状态=入账成功 轨迹状态=退款成功 出账状态=出账成功
                    if (orderInfo != null && orderInfo.getPayTime() != null) {
                        plusOrderBill.setInState(InStateEnum.IN_SUCCESS.getCode());
                        plusOrderBill.setInAmount(orderInfo.getOrderAmount());
                        plusOrderBill.setInTime(orderInfo.getPayTime());
                        plusOrderBill.setOutAmount(orderInfo.getRefundAmount());
                        plusOrderBill.setOutTime(orderInfo.getUpdateTime());
                        plusOrderBill.setTrailState(TrailStateEnum.REFUND_SUCCESS.getCode());
                        plusOrderBill.setOutState(OutStateEnum.OUT_STATE_1.getCode());
                        // 代付退的，橡树黑卡走桔子
                        Integer cancelType = orderInfo.getCancelType();
                        if (plusOrderBill.getInSupplier() == SupplierEnum.XSHK.getCode()
                                && cancelType != null && (
                                cancelType == PlusCancelTypeEnum.EXPIRE_PAYMENT.getValue()
                                        || cancelType
                                        == PlusCancelTypeEnum.NO_EXPIRE_PAYMENT.getValue())) {
                            plusOrderBill.setOutSupplier(configProperties.defaultSupplierId);
                        } else {
                            plusOrderBill.setOutSupplier(plusOrderBill.getInSupplier());
                        }
                    } else {
                        plusOrderBill.setTrailState(TrailStateEnum.DUCT_FAIL.getCode());
                    }
                    billRepository.updatePlusOrderBill(plusOrderBill);
                    continue;
                }
                // 获取支付流水号 兼容新老支付系统
                OrderPayResultEntity orderPayResultEntity = plusOrderAdapter.queryPayRecord(orderSn);
                TraderInfoRespEntity traderInfoRespEntity = orderPayResultEntity.getTraderInfoRespEntity();
                if (!PayProductCodeEnum.HK.getCode()
                        .equals(traderInfoRespEntity.getPayProductCode())
                        || !PayStateCodeEnum.S.getCode()
                        .equals(traderInfoRespEntity.getState())) {
                    log.info("重试划扣中单子未查询到划扣成功支付记录,orderSn{}", orderSn);
                    continue;
                }
                String serialNumber = traderInfoRespEntity.getSerialNumber();
                PayCallbackEntity dto = new PayCallbackEntity();
                dto.setPayType(PayTypeEnum.DEDUCT.getCode());
                dto.setOrderId(orderSn);
                dto.setAmount(orderInfo.getOrderAmount().multiply(new BigDecimal("100")));
                dto.setStatus(PayStateEnum.S.getCode());
                dto.setSerialNumber(serialNumber);
                this.incomeNotify(dto);
            } catch (Exception e) {
                LogUtil.printLog(e, "重试划扣中单子异常：" + orderSn);
            }
        }
        log.info("划扣中数据重试入账结束");
    }

    /**
     * 新支付系统订单支付成功预入账
     * <p>符合入账条件后，发起签署合同mq、等待签署结果mq回调后再上传合同文件到三方服务器，成功后再真正通知三方入账</p>
     */
    @Override
    public void newPayIncomeNotify(NewPayResultCallbackEntity entity) {
        try {
            String orderId = entity.getOrderId();
            log.info("新支付系统会员订单划扣成功三方预入账开始：{}", orderId);
            PlusOrderBillEntity orderBill = billRepository.getByOrderSnAndSerialNumber(orderId, entity.getSerialNumber());
            if (orderBill == null) {
                log.info("新支付系统保存预入账信息，未查询到对账记录：{}", orderId);
                return;
            }
            // 已经入账成功，不做处理
            if (orderBill.getInState() != null
                    && InStateEnum.IN_SUCCESS.getCode() == orderBill.getInState()) {
                log.info("新支付系统保存预入账信息，记录已入账无需重复处理：{}", orderId);
                return;
            }
            // 流转状态：成功/失败
            orderBill.setTrailState(PayStateCodeEnum.S.getCode().equals(entity.getState())
                    ? TrailStateEnum.DUCT_SUCCESS.getCode() : TrailStateEnum.DUCT_FAIL.getCode());
            // 划扣成功才发起签章，记录流水号，入账金额，占用额度和笔数
            if (orderBill.getTrailState() == TrailStateEnum.DUCT_SUCCESS.getCode()) {
                // 支付流水号
                if (StringUtils.isNotBlank(entity.getSerialNumber())) {
                    orderBill.setSerialNumber(entity.getSerialNumber());
                }
                // 入账金额
                // 新支付系统、回调的是元
                if (entity.getAmount() != null) {
                    orderBill.setInAmount(entity.getAmount());
                }
                // 增加分流实际额度占用和订单笔数占用
                shuntRepository.addShuntOrderCount(orderBill.getInSupplier(),
                        orderBill.getInAmount(), orderId);
                // 增加分流主体渠道业务场景当日金额
                try {
                    String businessScene = plusShuntRepository.getBusinessScene(orderId);
                    shuntRepository.addShuntOrderChannelSceneAmount(orderBill.getInSupplier(),
                            orderBill.getChannelId(), businessScene, orderBill.getInAmount(), orderId);
                } catch (Exception e) {
                    log.warn("增加分流主体渠道业务场景当日金额异常：{}", orderId, e);
                }
                // 2040705 zjf 只有黑卡划扣成功才需要签署合同
                if (orderBill.getInSupplier() == SupplierEnum.XSHK.getCode()) {
                    try {
                        log.info("新支付系统预入账-黑卡分流-签署合同开始：{}", orderId);
                        //发起合同签章
                        PlusOrderShuntPo plusOrderShunt = shuntRepository.getByOrderSn(orderId);
                        log.info("新支付系统保存预入账信息-获取分流信息：{}",
                                JSON.toJSONString(plusOrderShunt));
                        if (plusOrderShunt == null || StringUtils.isEmpty(
                                plusOrderShunt.getContractNo())) {
                            log.info("新支付系统保存预入账信息-未获取到分流信息或合同为空：{}",
                                    orderId);
                            // 这种情况不用处理签章状态
                            billRepository.updatePlusOrderBill(orderBill);
                            return;
                        }
                        PlusOrderContractEvent event = converter.toPlusOrderContractEvent(
                                plusOrderShunt);
                        contractRepository.signThirdPartyContract(event);
                        //修改合同状态-已发起签章
                        orderBill.setSignState(SignStateEnum.SIGN_STATE_2.getCode());
                    } catch (Exception e) {
                        log.info("新支付系统保存预入账信息-通知生成合同异常：orderId:{}", orderId,
                                e);
                        //修改合同状态-签章失败
                        orderBill.setSignState(SignStateEnum.SIGN_STATE_4.getCode());
                    }
                } else {
                    log.info("新支付系统预入账-非黑卡分流-入账开始：{}", orderId);
                    this.incomeExecute(orderBill, orderBill.getSerialNumber());
                }
            }
            billRepository.updatePlusOrderBill(orderBill);
            log.info("新支付系统会员订单划扣成功三方预入账结束：{}", orderId);
        } catch (Exception e) {
            LogUtil.printLog(e, "新支付系统会员订单划扣成功三方预入账异常");
        }
    }
}
