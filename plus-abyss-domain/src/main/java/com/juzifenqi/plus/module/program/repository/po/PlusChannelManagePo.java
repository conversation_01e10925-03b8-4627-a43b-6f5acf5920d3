package com.juzifenqi.plus.module.program.repository.po;

import java.util.Date;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022-12-13 13:23
 */
@Data
@Accessors(chain = true)
public class PlusChannelManagePo {

    /**
     * 主键id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 启用状态  1：新建 2：停用3：启用
     */
    private Integer state;

    /**
     * 营销会员类型id，以逗号分割
     */
    private String plusConfigIds;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 应用ID
     */
    private String publicKey;

    /**
     * 应用密钥
     */
    private String privateKey;
}