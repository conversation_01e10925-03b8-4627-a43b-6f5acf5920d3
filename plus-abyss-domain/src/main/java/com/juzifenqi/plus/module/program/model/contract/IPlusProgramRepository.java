package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramQueryReq;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.model.event.program.CopyProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.MultiplexChannelProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramPo;
import java.math.BigDecimal;
import java.util.List;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface IPlusProgramRepository {

    /**
     * 获取方案实体
     */
    PlusProgramEntity getById(Integer id);

    /**
     * 获取方案实体及权益包信息
     */
    PlusProgramEntity getProgramAndProfitsPackage(Integer id, PlusOrderEntity order);

    /**
     * 获取会员类型id所有生效的方案
     */
    List<PlusProgramEntity> getProgramByConfigId(Integer configId);

    /**
     * 修改方案价格
     */
    boolean updateProgramPrice(Integer programId, BigDecimal price);

    List<PlusProgramEntity> getMemberPlusProgramAll(PlusProgramQueryReq req);

    /**
     * 获取方案分页列表
     */
    PageResultEntity<PlusProgramListEntity> getProgramList(ProgramQueryReq req);

    /**
     * 判断方案后台名称是否存在
     */
    boolean existBackstageName(String backstageName, Integer programId, Integer channelId);

    /**
     * 保存会员方案
     */
    Integer addProgram(SavePlusProgramEvent event);

    /**
     * 编辑会员方案
     */
    void editProgram(SavePlusProgramEvent event);

    /**
     * 方案生效
     */
    void programEffective(SavePlusProgramEvent event);

    /**
     * 复制方案
     */
    void copyProgram(CopyProgramEvent event);

    /**
     * 方案详情
     * <p>后台方案详情调用，需要连表查询续费信息和返回权益列表信息</p>
     */
    ProgramDetailEntity getProgramDetail(Integer id);

    /**
     * 上架
     */
    Boolean upAct(String id);

    /**
     * 下架
     */
    Boolean downAct(String id);

    /**
     * 获取所有上架方案
     */
    List<PlusProgramEntity> getUpProgramList();
}
