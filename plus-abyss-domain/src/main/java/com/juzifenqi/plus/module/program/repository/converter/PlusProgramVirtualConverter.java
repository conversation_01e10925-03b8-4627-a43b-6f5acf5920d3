package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualTypeEntity;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.model.event.CreateVirtualTypeEvent;
import com.juzifenqi.plus.module.program.model.event.EditLyffVirtualGoodsEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PlusProgramVirtualConverter {

    PlusProgramVirtualConverter instance = Mappers.getMapper(PlusProgramVirtualConverter.class);

    List<PlusProgramVirtualTypeEntity> toPlusProgramVirtualTypeEntityList(
            List<PlusProgramVirtualTypePo> list);

    PlusProgramVirtualTypeEntity toPlusProgramVirtualTypeEntity(PlusProgramVirtualTypePo po);

    PlusProgramVirtualTypePo toPlusProgramVirtualTypePo(CreateVirtualTypeEvent event);

    PlusProgramVirtualPo toPlusProgramVirtualPo(CreateVirtualGoodsEvent event);

    List<PlusProgramVirtualEntity> toPlusProgramVirtualEntityList(List<PlusProgramVirtualPo> list);

    PlusProgramVirtualEntity toPlusProgramVirtualEntity(PlusProgramVirtualPo po);

    @Mappings({@Mapping(target = "updateUserId", source = "optId"),
            @Mapping(target = "updateUserNm", source = "optName")})
    PlusProgramVirtualPo toPlusProgramVirtualPo(EditLyffVirtualGoodsEvent event);

}
