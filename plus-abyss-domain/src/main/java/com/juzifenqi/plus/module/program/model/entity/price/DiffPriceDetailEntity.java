package com.juzifenqi.plus.module.program.model.entity.price;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * 差异化定价详情
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/12 18:07
 */
@Data
public class DiffPriceDetailEntity implements Serializable {

    private static final long serialVersionUID = 2079177851654030487L;

    /**
     * 定价主表id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 渠道标识
     */
    private Integer bizSource;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 会员类型id
     */
    private Integer configId;

    /**
     * 提额顺序 1_399-199  2_199-399
     */
    private Integer grade;

    /**
     * 差异化配置列表
     */
    private List<PlusDifferenceProgramPriceEntity> diffList;
}
