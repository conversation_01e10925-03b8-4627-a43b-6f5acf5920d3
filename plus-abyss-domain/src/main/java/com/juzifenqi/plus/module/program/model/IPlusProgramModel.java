package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.dto.req.admin.program.CopyProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramEditQueryReq;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramQueryReq;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusConfigEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.MultiplexChannelProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import java.math.BigDecimal;
import java.util.List;

/**
 * 方案model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 15:03
 */
public interface IPlusProgramModel {

    /**
     * 获取方案改价分页列表
     */
    List<PlusProgramEditPriceEntity> getProgramEditPriceList(PlusProgramEditQueryReq req);

    /**
     * 获取方案改价列表总数
     */
    Integer countProgramEditPrice(PlusProgramEditQueryReq req);

    /**
     * 新增方案改价任务
     */
    void addProgramEditPrice(CreateProgramEditPriceEvent event);

    /**
     * 处理改价任务
     */
    void executeProgramEditPriceTask();

    /**
     * 修改方案价格，同时修改商品中心对应方案商品价格
     */
    boolean updateProgramPrice(Integer programId, BigDecimal price);

    /**
     * 获取方案分页列表
     */
    PageResultEntity<PlusProgramListEntity> getProgramList(ProgramQueryReq req);

    /**
     * 判断方案后台名称是否存在
     */
    boolean existBackstageName(String backstageName, Integer programId, Integer channelId);

    /**
     * 新增方案
     */
    void addProgram(SavePlusProgramEvent event);

    /**
     * 编辑方案
     */
    void editProgram(SavePlusProgramEvent event);

    /**
     * 方案生效
     */
    void programEffective(SavePlusProgramEvent event);

    /**
     * 复用渠道方案
     */
    void multiplexChannelProgram(MultiplexChannelProgramEvent event);

    /**
     * 方案详情
     */
    ProgramDetailEntity getProgramDetail(Integer id);

    /**
     * 复制方案
     */
    void copyProgram(CopyProgramReq req);

    List<PlusProgramEntity> getMemberPlusProgramAll(PlusProgramQueryReq req);

    /**
     * 方案上架
     */
    Boolean upAct(String id);

    /**
     * 方案下架
     */
    Boolean downAct(String id);

    /**
     * 桔享Plus方案更新记录日志
     */
    void saveMemberPlusProgramLog(Integer programId, Integer operatingId, String operatingName,
            String event, String content);

    /**
     * 获取所有已上架方案
     */
    List<PlusProgramEntity> getUpProgramList();

    /**
     * 获取所有会员类型列表
     */
    List<PlusConfigEntity> loadPlusConf();
}
