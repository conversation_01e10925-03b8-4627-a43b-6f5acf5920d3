package com.juzifenqi.plus.module.program.model.entity.detail.land;

import java.util.List;
import lombok.Data;

/**
 * 方案标识信息
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/11/7 13:48
 */
@Data
public class LandFlagDetailEntity {

    /**
     * 标识是否是第一次购买该方案
     */
    private Boolean firstOrder;

    /**
     * 是否能买, 1能买，显示立即购买，2不能买，显示已售罄
     */
    private Integer canBuy;

    /**
     * 不能购买的code
     */
    private Integer buyCode;

    /**
     * 是否显示会员权益页按钮 1-展示；2-不展示
     */
    private Integer showPlusButton;

    /**
     * 认证状态  1_已认证、2_认证中 3_授权失败 4_未认证 5_认证项即将过期 7_认证项已过期 8_准入失败 13_取消认证
     */
    private Integer authState;

    /**
     * 开通方式
     */
    private List<String> openModeList;

    /**
     * 用户登录状态 1 是登录  0是未登录
     */
    private Integer isLogin = 0;
}
