package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.module.asserts.model.contract.entity.coupon.MemberCouponInfoEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusCouponIndexEntity;
import java.util.List;

/**
 * 会员方案优惠券配置查询model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 14:15
 */
public interface IPlusCouponIndexQueryModel {

    /**
     * 方案id和类型查询
     */
    List<PlusCouponIndexEntity> getByProgramId(Integer programId, Integer type);

    /**
     * 开卡礼优惠券信息
     */
    List<MemberCouponInfoEntity> getKklCouponByProgramId(Integer programId);

}
