package com.juzifenqi.plus.module.order.repository.converter;

import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundDetailEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.refund.PlusOrderRefundInfoEntity;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundDetailPo;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderRefundInfoPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/2 11:15
 */
@Mapper
public interface IPlusOrderRefundDetailRepositoryConverter {

    IPlusOrderRefundDetailRepositoryConverter instance = Mappers.getMapper(
            IPlusOrderRefundDetailRepositoryConverter.class);

    PlusOrderRefundDetailEntity toEntity(PlusOrderRefundDetailPo po);

    List<PlusOrderRefundDetailEntity> toEntityList(List<PlusOrderRefundDetailPo> poList);

}
