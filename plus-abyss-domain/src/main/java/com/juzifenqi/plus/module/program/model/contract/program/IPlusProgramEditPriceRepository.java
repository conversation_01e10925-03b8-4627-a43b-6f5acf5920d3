package com.juzifenqi.plus.module.program.model.contract.program;

import com.juzifenqi.plus.dto.req.admin.program.PlusProgramEditQueryReq;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import java.util.List;

/**
 * 方案改价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 15:07
 */
public interface IPlusProgramEditPriceRepository {

    /**
     * 获取方案改价分页列表
     */
    List<PlusProgramEditPriceEntity> getProgramEditPriceList(PlusProgramEditQueryReq req);

    /**
     * 获取方案改价列表总数
     */
    Integer countProgramEditPrice(PlusProgramEditQueryReq req);

    /**
     * 新增方案改价任务
     */
    void addProgramEditPrice(CreateProgramEditPriceEvent event);

    /**
     * 获取改价任务列表
     */
    List<PlusProgramEditPriceEntity> getJobList();

    /**
     * 修改任务信息
     */
    void updateProgramEditPrice(PlusProgramEditPriceEntity entity, boolean updateProgramResult);
}
