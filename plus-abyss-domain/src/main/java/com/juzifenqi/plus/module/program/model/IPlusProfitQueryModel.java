package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitModelBasicDetailEntity;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitQueryEvent;
import com.juzifenqi.plus.module.program.model.entity.PlusProductEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandModelBasicDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandVirtualProductTypeEntity;
import java.util.List;

/**
 * 会员方案配置的权益查询model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/8 15:20
 */
public interface IPlusProfitQueryModel {

    /**
     * 方案权益下是否配置了某个商品
     */
    boolean existNewProduct(Integer programId, Integer productId, Integer modelId);

    /**
     * 获取会员商品列表
     */
    List<PlusProductEntity> getPlusProductList();

    /**
     * 获取权益列表（包含权益配置数据）
     */
    List<LandModelBasicDetailEntity> getProfitModelList(Integer programId);

    /**
     * 获取虚拟商品列表
     */
    List<LandVirtualProductTypeEntity> getVirtualProductList(Integer programId);

    /**
     * 获取方案权益缓存列表
     */
    List<LandModelBasicDetailEntity> getProfitCachList(Integer programId);

    /**
     * 获取权益的基本信息（除了会员提额，其他权益一致）
     */
    ProfitModelBasicDetailEntity getProfitBasicInfo(HandleProfitQueryEvent event);

    /**
     * 是否会员商品
     */
    boolean isPlusProduct(Integer productId);

    /**
     * 是否一元购商品
     */
    boolean isYygProduct(Integer productId);
}
