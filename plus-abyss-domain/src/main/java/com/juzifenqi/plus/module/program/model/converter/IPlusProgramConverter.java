package com.juzifenqi.plus.module.program.model.converter;

import com.juzifenqi.magic.bean.dto.OpenCheckDTO;
import com.juzifenqi.plus.dto.req.detail.LandReq;
import com.juzifenqi.plus.dto.req.detail.VirtualQueryReq;
import com.juzifenqi.plus.module.asserts.model.entity.profit.ProfitVirtualProductTypeEntity;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitLandEvent;
import com.juzifenqi.plus.module.asserts.model.event.model.HandleProfitQueryEvent;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusDiscountEntity;
import com.juzifenqi.plus.module.order.model.event.order.ShuntEvent;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramCashbackEntity;
import com.juzifenqi.plus.module.program.model.converter.condtions.IPlusProgramConverterCondition;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandDiscountDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandOldDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandProgramBasicDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.detail.land.LandVirtualProductTypeEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo;
import java.math.BigDecimal;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.factory.Mappers;

/**
 * 方案转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/14 10:16
 */
@Mapper(uses = {IPlusProgramConverterCondition.class})
public interface IPlusProgramConverter {

    IPlusProgramConverter instance = Mappers.getMapper(IPlusProgramConverter.class);

    LandProgramBasicDetailEntity toProgramBasicDetailEntity(PlusProgramEntity program);

    @Mapping(target = "discountEndTime", source = "discountEndTime", qualifiedByName = "convertDiscountTime")
    LandDiscountDetailEntity toDiscountDetailEntity(PlusDiscountEntity entity);

    @Mappings({@Mapping(target = "channel", source = "channelId")})
    OpenCheckDTO toOpenCheckDTO(LandReq event);

    HandleProfitLandEvent toHandleProfitLandEvent(LandReq event, Integer modelId);

    @Mappings({@Mapping(target = "name", source = "programName")})
    LandOldDetailEntity toLandOldDetailEntity(PlusProgramEntity entity);

    ShuntEvent toShuntEvent(Integer userId, Integer channelId, BigDecimal amount, Integer configId);

    HandleProfitQueryEvent toHandleProfitQueryEvent(VirtualQueryReq req);

    List<LandVirtualProductTypeEntity> toLandVirtualProductTypeEntityList(
            List<ProfitVirtualProductTypeEntity> list);
}
