package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 拒就赔条件
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 16:45
 */
@Mapper
public interface IPlusProgramRejectionMapper {

    /**
     * Load查询
     */
    PlusProgramRejectionPo loadPlusProgramRejection(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusProgramRejectionPo> pageList(Integer programId, Integer startPage, Integer pageSize);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("programId") Integer programId);

    /**
     * 根据分案ID获取拒拒赔优惠券
     */
    List<PlusProgramRejectionPo> getRejectionCouponByProgramId(
            @Param("programId") Integer programId);

    List<PlusProgramRejectionPo> getByRejectionIds(@Param("ids") List<Integer> ids);

    /**
     * 校验方案下的某一类拒就赔没有设置过 排除ID
     */
    Integer checkRejectTypeZero(Integer type, Integer programId, Integer id);

    /**
     * 新增返回ID
     */
    Integer savePlusProgramRejection(PlusProgramRejectionPo plusProgramRejection);

    /**
     * 删除
     */
    Boolean deletePlusProgramRejection(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusProgramRejection(PlusProgramRejectionPo plusProgramRejection);

    /**
     * 批量保存
     */
    Boolean batchInsertPlusRejection(@Param("list") List<PlusProgramRejectionPo> list);
}
