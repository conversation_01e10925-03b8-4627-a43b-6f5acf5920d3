package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramSrlRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramSrlEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramSrlEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramSrlConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramSrlMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramSrlPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @createTime 2024/2/29 17:23
 * @description
 */
@Repository
@Slf4j
public class PlusProgramSrlRepositoryImpl implements IPlusProgramSrlRepository {

    PlusProgramSrlConverter converter = PlusProgramSrlConverter.instance;

    @Autowired
    private IPlusProgramSrlMapper srlMapper;

    @Override
    public void saveSrl(CreatePlusProgramSrlEvent event) {
        log.info("保存生日礼权益：{}", JSON.toJSONString(event));
        PlusProgramSrlPo plusProgramSrlPo = srlMapper.selectByProgramId(event.getProgramId());
        if (plusProgramSrlPo == null) {
            log.info("生日礼权益不存在，开始保存：{}", event.getProgramId());
            srlMapper.insert(converter.toPlusProgramSrlPo(event));
        } else {
            log.info("生日礼权益存在，开始修改：{}", event.getProgramId());
            plusProgramSrlPo.setUpdateUserId(event.getCreateUserId());
            plusProgramSrlPo.setUpdateUserName(event.getCreateUserName());
            plusProgramSrlPo.setImgUrl(event.getImgUrl());
            srlMapper.update(plusProgramSrlPo);
        }
        log.info("保存生日礼权益完成：{}", event.getProgramId());
    }

    @Override
    public PlusProgramSrlEntity getPlusProgramSrl(Integer programId) {
        return converter.toPlusProgramSrlEntity(srlMapper.selectByProgramId(programId));
    }
}
