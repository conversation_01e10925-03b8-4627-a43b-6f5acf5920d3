package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramPo;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;

/**
 * 方案续费配置
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/1/13 14:52
 */
public interface IPlusRenewRepository {

    /**
     * 获取方案续费配置
     */
    PlusRenewRelevancePo getByProgramId(Integer programId);

    /**
     * 保存续费信息
     */
    void addRenewRelevance(SavePlusProgramEvent po, Integer programId);

    /**
     * 编辑续费信息
     */
    void editRenewRelevance(SavePlusProgramEvent po, Integer programId);
}
