package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2022-12-13 13:26
 */
@Mapper
public interface IPlusChannelFunctionMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusChannelFunction(PlusChannelFunctionPo plusChannelFunction);

    /**
     * 新增
     */
    Integer insertPlusChannelFunction(
            @Param("plusChannelFunction") PlusChannelFunctionPo plusChannelFunction);

    /**
     * 删除
     */
    Integer deletePlusChannelFunction(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusChannelFunction(
            @Param("plusChannelFunction") PlusChannelFunctionPo plusChannelFunction);

    /**
     * Load查询
     */
    PlusChannelFunctionPo loadPlusChannelFunction(@Param("id") Integer id);

    /**
     * 分页查询Data
     */
    List<PlusChannelFunctionPo> pageList(@Param("offset") Integer offset,
            @Param("pagesize") Integer pagesize);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("offset") Integer offset, @Param("pagesize") Integer pagesize);


    /**
     * 通过渠道管理表id查询
     */
    List<PlusChannelFunctionPo> getByManageId(Integer manageId);

    /**
     * 通过 manageId 删除
     */
    void deleteByManageId(Integer manageId);

    /**
     * 通过渠道id查询
     */
    List<PlusChannelFunctionPo> getByChannelId(Integer channelId);
}
