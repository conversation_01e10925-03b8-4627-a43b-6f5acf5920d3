package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusProgramTxEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramTxEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTxPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PlusProgramTxLmkConverter {

    PlusProgramTxLmkConverter instance = Mappers.getMapper(PlusProgramTxLmkConverter.class);

    List<PlusProgramTxPo> toPlusProgramTxPoList(List<CreatePlusProgramTxEvent> list);

    List<PlusProgramTxEntity> toPlusProgramTxEntityList(List<PlusProgramTxPo> list);

    PlusProgramTxPo toPlusProgramTxPo(CreatePlusProgramTxEvent event);
}
