package com.juzifenqi.plus.module.program.model.contract.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 会员权益任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 14:09
 */
@Data
public class PlusProgramTaskEntity {

    /**
     * 主键
     */
    private int id;

    /**
     * 方案ID
     */
    private int programId;

    /**
     * 下单笔数
     */
    private int        ordersNumber;
    /**
     * 订单满足金额
     */
    private BigDecimal orderPrice;
    /**
     * 优惠券名称
     */
    private String     couponName;

    /**
     * 优惠券唯一索引标识
     */
    private Integer index;

    /**
     * 优惠券ID
     */
    private Integer couponId;
}
