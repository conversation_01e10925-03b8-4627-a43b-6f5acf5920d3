package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.admin.program.ChannelManagerQueryReq;
import com.juzifenqi.plus.module.program.application.converter.IPlusProgramAdminConverter;
import com.juzifenqi.plus.module.program.model.contract.IPlusChannelManagerRepository;
import com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelManagerEntity;
import com.juzifenqi.plus.module.program.repository.dao.IPlusChannelFunctionMapper;
import com.juzifenqi.plus.module.program.repository.dao.IPlusChannelManageMapper;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo;
import com.juzifenqi.plus.utils.RedisUtils;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 渠道管理
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 11:22
 */
@Slf4j
@Repository
public class PlusChannelManagerRepositoryImpl implements IPlusChannelManagerRepository {

    private final IPlusProgramAdminConverter converter = IPlusProgramAdminConverter.instance;

    @Autowired
    private RedisUtils                 redisUtils;
    @Autowired
    private IPlusChannelManageMapper   manageMapper;
    @Autowired
    private IPlusChannelFunctionMapper functionMapper;

    /**
     * 通过渠道和会员类型查询功能
     */
    @Override
    public PlusChannelFunctionPo getConfig(Integer channelId, Integer configId) {
        log.info("查询渠道配置，入参：{},{}", channelId, configId);
        String key = RedisConstantPrefix.CHANNEL_CACHE + channelId;
        String json = (String) redisUtils.hGet(key, String.valueOf(configId));
        if (StringUtils.isBlank(json)) {
            log.info("缓存中不存在 redisKey:{}", key);
            return null;
        }
        log.info("从缓存中获取到会员类型功能信息：{}", json);
        return JSONObject.parseObject(json, PlusChannelFunctionPo.class);
    }

    @Override
    public List<PlusChannelManagerEntity> getChannelList(ChannelManagerQueryReq req) {
        return manageMapper.getChannelList(req);
    }

    @Override
    public PlusChannelManagePo getByChannelId(Integer channelId) {
        return manageMapper.getByChannelId(channelId);
    }

    @Override
    public PlusChannelManagePo getById(Integer id) {
        return manageMapper.loadPlusChannelManage(id);
    }

    @Override
    public void savePlusChannelManage(PlusChannelManagePo po) {
        manageMapper.savePlusChannelManage(po);
    }

    @Override
    public List<PlusChannelFunctionPo> getFunctionList(Integer managerId) {
        return functionMapper.getByManageId(managerId);
    }

    @Override
    public void batchSaveFunction(List<PlusChannelFunctionPo> list) {
        // 先删
        functionMapper.deleteByManageId(list.get(0).getManageId());
        // 再加
        for (PlusChannelFunctionPo plusChannelFunctionPo : list) {
            functionMapper.insertPlusChannelFunction(plusChannelFunctionPo);
        }
    }

    @Override
    public void updateChannelManager(PlusChannelManagePo po) {
        manageMapper.updatePlusChannelManage(po);
    }

    @Override
    public PlusChannelManagerEntity getManagerAndFunctionById(Integer id) {
        PlusChannelManagePo po = this.getById(id);
        PlusChannelManagerEntity entity = converter.toPlusChannelManagerEntity(po);
        List<PlusChannelFunctionPo> functionList = this.getFunctionList(id);
        entity.setPlusChannelFunctions(converter.toPlusChannelFunctionEntityList(functionList));
        return entity;
    }

    @Override
    public List<PlusChannelManagerEntity> getChannelListPage(ChannelManagerQueryReq req) {
        List<PlusChannelManagePo> list = manageMapper.selectList(req);
        return converter.toPlusChannelManagerEntityList(list);
    }

    @Override
    public Integer countChannelListPage(ChannelManagerQueryReq req) {
        return manageMapper.coutList(req);
    }

    /**
     * 通过渠道id获取渠道功能配置
     */
    @Override
    public List<PlusChannelFunctionPo> getFunctionListByChannelId(Integer channelId) {
        return functionMapper.getByChannelId(channelId);
    }
}
