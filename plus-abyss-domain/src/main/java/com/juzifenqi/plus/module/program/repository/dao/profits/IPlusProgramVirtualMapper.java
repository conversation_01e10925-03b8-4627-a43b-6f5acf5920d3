package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import java.util.List;
import java.util.Map;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 方案虚拟商品关联Dao
 *
 * <AUTHOR>
 * @date 2022/01/18 19:24
 */
@Mapper
public interface IPlusProgramVirtualMapper {

    /**
     * 批量新增
     */
    Integer saveBatch(@Param("list") List<PlusProgramVirtualPo> virtualList);

    /**
     * 根据ID更新
     */
    Integer updateById(@Param("plusProgramVirtual") PlusProgramVirtualPo plusProgramVirtual);

    /**
     * 根据sku更新多个
     */
    int syncStatusBySku(@Param("plusProgramVirtual") PlusProgramVirtualPo plusProgramVirtual);

    /**
     * 根据ID删除
     */
    Integer deleteById(Integer id);

    /**
     * 根据ID查询
     */
    PlusProgramVirtualPo selectById(Integer id);

    /**
     * 分页查询Data
     */
    List<PlusProgramVirtualPo> pageList(@Param("queryMap") Map<String, String> queryMap,
            @Param("start") Integer start, @Param("size") Integer size);

    /**
     * 分页查询Count
     */
    Integer pageListCount(@Param("queryMap") Map<String, String> queryMap);


    /**
     * 根据方案id和sku查询
     */
    PlusProgramVirtualPo getByProgramAndSku(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId, @Param("productSku") String productSku);

    /**
     * 根据sku查询集合
     */
    List<PlusProgramVirtualPo> getBySku(@Param("productSku") String productSku);

    /**
     * 获取方案下的所有关联商品
     */
    List<PlusProgramVirtualPo> getByProgram(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    List<PlusProgramVirtualPo> pageListByQuery(@Param("param") PlusProgramVirtualPo queryVo,
            @Param("start") Integer start, @Param("size") Integer pageSize);

    /**
     * 分页查询Count
     */
    Integer pageListCountByQuery(Integer profitTypeId);

    /**
     * 查询当前方案，当前分类下面权益
     */
    List<PlusProgramVirtualPo> getByProgramAndType(Integer programId, Integer profitTypeId);

    /**
     * 删除当前方案，当前分类下面权益
     */
    void batchDelete(Integer programId, Integer profitTypeId);

    /**
     * 获取方案下的所有关联商品
     */
    List<PlusProgramVirtualPo> getVirtualByType(Integer profitTypeId);

    /**
     * 获取方案下的所有关联商品-返回实体-方案复制
     */
    List<PlusProgramVirtualPo> getVirtualEntityByType(Integer profitTypeId);

    /**
     * 根据id获取虚拟权益信息
     */
    PlusProgramVirtualPo getVirtualById(Integer id);

    /**
     * 根据programId、modelId分页查询
     */
    List<PlusProgramVirtualPo> getPageVirtualList(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId, @Param("start") Integer start,
            @Param("size") Integer pageSize);

    /**
     * 根据programId、modelIdc查询总数
     */
    Integer countList(Integer programId, Integer modelId);

    /**
     * 获取方案下的所有关联商品-方案复制
     */
    List<PlusProgramVirtualPo> getByProgramIdAndModelId(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    /**
     * 根据配置和商品SKU查询权益数据
     * @param configIdList
     * @param modelId
     * @param productSku
     * @return
     */
    List<PlusProgramVirtualPo> listBySkuAndConfig(@Param("configIdList") List<Integer> configIdList,
            @Param("modelId") Integer modelId,
            @Param("productSku") String productSku);

    /**
     * 批量更新营销图片
     * @param idList
     * @param imgUrl
     * @return
     */
    int updateImgUrlBatch(@Param("idList") List<Integer> idList, @Param("imgUrl") String imgUrl);

}
