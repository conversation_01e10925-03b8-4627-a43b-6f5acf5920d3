package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.dto.req.admin.program.PlusProgramQueryReq;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramQueryReq;
import com.juzifenqi.plus.enums.BusinessTypeEnum;
import com.juzifenqi.plus.enums.ChannelFunctionEnum;
import com.juzifenqi.plus.enums.GwfxCashTypeEnum;
import com.juzifenqi.plus.enums.JuziPlusEnum;
import com.juzifenqi.plus.enums.OptEventEnum;
import com.juzifenqi.plus.enums.PlusCouponTypeEnum;
import com.juzifenqi.plus.enums.PlusModelEnum;
import com.juzifenqi.plus.enums.PlusProfitsGiveTypeEnum;
import com.juzifenqi.plus.enums.PlusProgramLogNodeEnum;
import com.juzifenqi.plus.enums.PlusProgramSendTypeEnum;
import com.juzifenqi.plus.enums.PlusReachConditionTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.asserts.model.contract.entity.MemberPlusLmkVirtualEntity;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.IPlusProfitSystemLogRepository;
import com.juzifenqi.plus.module.common.IPlusProgramLogRepository;
import com.juzifenqi.plus.module.common.IProductExternalRepository;
import com.juzifenqi.plus.module.common.IVirtualGoodsExternalRepository;
import com.juzifenqi.plus.module.common.entity.PageResultEntity;
import com.juzifenqi.plus.module.common.entity.PlusProfitSystemLogEntity;
import com.juzifenqi.plus.module.order.model.PlusOrderSnapshtoQueryModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusRdzxRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderProgramSnapshotEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusRdzxServiceFeeEntity;
import com.juzifenqi.plus.module.order.model.contract.external.IRdzxExternalRepository;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderCreateEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusMqRdzxRecordPo;
import com.juzifenqi.plus.module.program.model.adapter.ProfitAdapter;
import com.juzifenqi.plus.module.program.model.contract.IPlusProModelRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusQjhkCouponConfigRepository;
import com.juzifenqi.plus.module.program.model.contract.IPlusRenewRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.RdzxEquityInfoEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.RdzxEquityInfoEntity.HkfxAmountInfo;
import com.juzifenqi.plus.module.program.model.entity.PlusProModelEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramVirtualEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusQjhkCouponConfigEntity;
import com.juzifenqi.plus.module.program.model.entity.RdzxEquitySwitchEntity;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.model.event.RdzxEquityConfigInfoEvent;
import com.juzifenqi.plus.module.program.model.event.program.CopyProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProModelMapper;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProgramExtendMapper;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProgramMapper;
import com.juzifenqi.plus.module.program.repository.dao.IPlusRenewRelevanceMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusCouponIndexMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusLiftAmountMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramCashbackMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramCashbackSnapshotMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductNewMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramProductTypeMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramRejectionMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramTaskMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramVirtualMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramVirtualTypeMapper;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramXFZKMapper;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCashbackObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCouponObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsCreditObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsPackageObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsVirtualGoodsObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramProfitsVirtualObject;
import com.juzifenqi.plus.module.program.repository.entity.PlusReachConditionObject;
import com.juzifenqi.plus.module.program.repository.po.PlusProModelPo;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramExtendPo;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramPo;
import com.juzifenqi.plus.module.program.repository.po.PlusRenewRelevancePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusCouponIndexPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusLiftAmountPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackSnapshotPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductTypePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramRejectionPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramXFZKPo;
import com.juzifenqi.plus.utils.RedisUtils;
import com.juzifenqi.plus.utils.Sign;
import com.juzifenqi.product.entity.MemberPlusGoodsVo;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class PlusProgramRepositoryImpl implements IPlusProgramRepository {

    @Autowired
    private IPlusProgramMapper                 plusProgramMapper;
    @Autowired
    private IPlusProgramExtendMapper           plusProgramExtendMapper;
    @Autowired
    private IPlusCouponIndexMapper             plusCouponIndexMapper;
    @Autowired
    private IPlusProgramCashbackSnapshotMapper plusProgramCashbackSnapshotMapper;
    @Autowired
    private IPlusLiftAmountMapper              plusLiftAmountMapper;
    @Autowired
    private IPlusProgramTaskMapper             taskMapper;
    @Autowired
    private IPlusProgramRejectionMapper        rejectionMapper;
    @Autowired
    private IPlusProgramXFZKMapper             xfzkMapper;
    @Autowired
    private IPlusRdzxRepository                rdzxRepository;
    @Autowired
    private IPlusProModelRepository            plusProModelRepository;
    @Autowired
    private IPlusQjhkCouponConfigRepository    configRepository;
    @Autowired
    private IRdzxExternalRepository            rdzxExternalRepository;
    @Autowired
    private RedisUtils                         redisUtils;
    @Autowired
    private IProductExternalRepository         productExternalRepository;
    @Autowired
    private IVirtualGoodsExternalRepository    virtualGoodsExternalRepository;
    @Autowired
    private IPlusRenewRepository               plusRenewRepository;
    @Autowired
    private IPlusRenewRelevanceMapper          relevanceMapper;
    @Autowired
    private IPlusProModelMapper                plusProModelMapper;
    @Autowired
    private IPlusProgramProductTypeMapper      programProductTypeMapper;
    @Autowired
    private IPlusProgramProductNewMapper       productNewMapper;
    @Autowired
    private IPlusProgramVirtualTypeMapper      plusProgramVirtualTypeMapper;
    @Autowired
    private IPlusProgramVirtualMapper          plusProgramVirtualMapper;
    @Autowired
    private IPlusProgramCashbackMapper         cashbackMapper;
    @Autowired
    private IPlusProgramLogRepository          programLogRepository;
    @Autowired
    private IIMRepository                      imRepository;
    @Autowired
    private PlusOrderSnapshtoQueryModel        plusOrderSnapshtoQueryModel;
    @Autowired
    private IPlusProfitSystemLogRepository plusProfitSystemLogRepository;
    @Autowired
    private ProfitAdapter profitAdapter;

    private final PlusProgramConverter converter = PlusProgramConverter.instance;

    /**
     * 获取方案实体 优先处理：加速、生活权益、提额、结清返现
     */
    @Override
    public PlusProgramEntity getById(Integer id) {
        // 方案主体信息
        PlusProgramPo plusProgram = plusProgramMapper.loadMemberPlusProgram(id);
        // 方案扩展信息
        PlusProgramExtendPo plusProgramExtend = plusProgramExtendMapper.getProgramExtendByPid(id);
        return converter.toPlusProgramEntity(plusProgram, plusProgramExtend, null);
    }

    /**
     * 获取方案实体, 带上订单号，部分需要取快照数据
     */
    @Override
    public PlusProgramEntity getProgramAndProfitsPackage(Integer id, PlusOrderEntity order) {
        // 方案主体信息
        PlusProgramPo plusProgram = plusProgramMapper.loadMemberPlusProgram(id);
        // 方案扩展信息
        PlusOrderProgramSnapshotEntity programSnapshotEntity = null;
        PlusProgramExtendPo plusProgramExtend = plusProgramExtendMapper.getProgramExtendByPid(id);
        if (PlusConstant.MERGE_CARD_LIST.contains(order.getConfigId())) {
            // 获取会员方案-发送节点快照
            log.info("获取会员方案快照数据 orderSn {} programId {}", order.getOrderSn(), id);
            programSnapshotEntity = plusOrderSnapshtoQueryModel.getOrderProgramSnapshot(
                    order.getOrderSn());
            if (Objects.nonNull(programSnapshotEntity) && Objects.nonNull(
                    programSnapshotEntity.getSendNode())) {
                if (Objects.isNull(plusProgramExtend)) {
                    plusProgramExtend = new PlusProgramExtendPo();
                    plusProgramExtend.setProgramId(id);
                }
                plusProgramExtend.setSendNode(programSnapshotEntity.getSendNode());
            }
        }
        if (Objects.isNull(programSnapshotEntity) && Objects.nonNull(plusProgramExtend)){
            // 无订单快照 - 走原始发放节点【开卡即发】
            plusProgramExtend.setSendNode(PlusProgramSendTypeEnum.NOW.getValue());
        }
        // 权益包信息
        List<PlusProgramProfitsPackageObject> profitsPackageObjects = getPlusProgramProfitsPackageObjects(
                id, order,
                Optional.ofNullable(plusProgramExtend).map(PlusProgramExtendPo::getSendNode)
                        .orElse(PlusProgramSendTypeEnum.NOW.getValue()));
        return converter.toPlusProgramEntity(plusProgram, plusProgramExtend, profitsPackageObjects);
    }

    @Override
    public List<PlusProgramEntity> getProgramByConfigId(Integer configId) {
        List<PlusProgramPo> list = plusProgramMapper.getMemberPlusProgramList(configId, null, null);
        return converter.toPlusProgramEntityList(list);
    }

    @Override
    public boolean updateProgramPrice(Integer programId, BigDecimal price) {
        log.info("修改方案价格开始：{},{}", programId, price);
        PlusProgramPo plusProgram = plusProgramMapper.loadMemberPlusProgram(programId);
        if (plusProgram == null) {
            log.info("修改方案价格获取方案信息为空：{}", programId);
            return false;
        }
        // 老价格
        BigDecimal oldPrice = plusProgram.getMallMobilePrice();
        Integer rows = plusProgramMapper.updateMallMobilePrice(programId, price);
        if (rows != null && rows > 0) {
            log.info("方案价格数据库修改成功：{}", programId);
            MemberPlusGoodsVo memberPlusGoodsVo = new MemberPlusGoodsVo();
            memberPlusGoodsVo.setId(plusProgram.getId());
            memberPlusGoodsVo.setName(plusProgram.getName());
            memberPlusGoodsVo.setMallMobilePrice(plusProgram.getMallMobilePrice());
            boolean result = productExternalRepository.updateMemberPlusGoods(memberPlusGoodsVo);
            if (!result) {
                log.info("修改商品中心方案价格失败,还原方案价格：{},{}", programId, oldPrice);
                plusProgramMapper.updateMallMobilePrice(programId, oldPrice);
            }
            return result;
        }
        log.info("修改方案价格结束：{},{}", programId, price);
        return false;
    }

    @Override
    public PageResultEntity<PlusProgramListEntity> getProgramList(ProgramQueryReq req) {
        PageResultEntity<PlusProgramListEntity> result = new PageResultEntity<>();
        result.setCount(plusProgramMapper.pageListCountNew(req));
        result.setList(plusProgramMapper.pageListNew(req, req.getStartPage(), req.getPageSize()));
        return result;
    }

    @Override
    public boolean existBackstageName(String backstageName, Integer programId, Integer channelId) {
        return plusProgramMapper.getBackstageNameCount(backstageName, programId, channelId) > 0;
    }

    @Override
    public Integer addProgram(SavePlusProgramEvent event) {
        // 保存方案基础信息
        PlusProgramPo plusProgram = converter.toPlusProgramPo(event);
        plusProgram.setSignProgram(getSignProgram());
        Integer rows = plusProgramMapper.saveMemberPlusProgram(plusProgram);
        Integer programId = plusProgram.getId();
        if (rows > 0) {
            // 保存方案扩展表
            PlusProgramExtendPo plusProgramExtend = converter.toPlusProgramExtendPo(event,
                    programId);
            // 保存会员方案-发放节点
            if (event.getConfigId() == JuziPlusEnum.RDZX_CARD.getCode() || Objects.nonNull(
                    event.getSendNode())) {
                // 保存配置
                plusProgramExtendMapper.savePlusProgramExtend(plusProgramExtend);
            }
            if (event.getConfigId() == JuziPlusEnum.RDZX_CARD.getCode()) {
                // 加载缓存
                reloadRdzxEquitySwitchCache(plusProgramExtend);
            }
            // 创建方案商品信息
            MemberPlusGoodsVo memberPlusGoodsVo = new MemberPlusGoodsVo();
            memberPlusGoodsVo.setId(programId);
            memberPlusGoodsVo.setName(plusProgram.getName());
            memberPlusGoodsVo.setMemberPrice(plusProgram.getMallMobilePrice());
            boolean result = productExternalRepository.createMemberPlusGoods(memberPlusGoodsVo);
            if (!result) {
                throw new PlusAbyssException("请求商品系统创建会员方案商品失败");
            }
            // 保存方案与权益关联信息
            String modelIds = plusProgram.getModelId();
            plusProModelRepository.saveProModel(programId,
                    Arrays.stream(modelIds.split(",")).map(Integer::valueOf)
                            .collect(Collectors.toList()));
            // 保存续费信息
            plusRenewRepository.addRenewRelevance(event, programId);
        }
        return programId;
    }

    @Override
    public void editProgram(SavePlusProgramEvent event) {
        // 修改商品信息
        PlusProgramPo plusProgram = converter.toPlusProgramPo(event);
        Integer programId = plusProgram.getId();
        Integer rows = plusProgramMapper.updatePlusProgram(plusProgram);
        if (rows > 0) {
            PlusProgramExtendPo plusProgramExtend = converter.toPlusProgramExtendPo(event,
                    programId);
            // 保存会员方案-发放节点
            if (event.getConfigId() == JuziPlusEnum.RDZX_CARD.getCode() || Objects.nonNull(
                    event.getSendNode())) {
                // 如果不存在则新增
                PlusProgramExtendPo exist = plusProgramExtendMapper.getProgramExtendByPid(
                        programId);
                if (exist == null) {
                    plusProgramExtendMapper.savePlusProgramExtend(plusProgramExtend);
                } else {
                    plusProgramExtend.setId(exist.getId());
                    plusProgramExtendMapper.updatePlusProgramExtend(plusProgramExtend);
                }
            }
            if (event.getConfigId() == JuziPlusEnum.RDZX_CARD.getCode()) {
                reloadRdzxEquitySwitchCache(plusProgramExtend);
            }
            // 修改方案与权益关联信息
            List<PlusProModelEntity> plusProModels = plusProModelRepository.getPlusProModelList(
                    programId);
            List<Integer> modelIds = plusProModels.stream().map(PlusProModelEntity::getModelId)
                    .collect(Collectors.toList());
            String[] ids = new String[modelIds.size()];
            for (int i = 0; i < modelIds.size(); i++) {
                ids[i] = String.valueOf(modelIds.get(i));
            }
            String oldModelIds = String.join(",", ids);
            String newModelIds = plusProgram.getModelId();
            if (!oldModelIds.equals(newModelIds)) {
                List<Integer> deleteList = new ArrayList<>();
                List<Integer> addList = new ArrayList<>();
                // 20201201 wangxl 删除更新后未选择的权益;增加新增后以前没有的权益
                List<String> oldModelList = Arrays.asList(oldModelIds.split(","));
                List<String> newModelList = Arrays.asList(newModelIds.split(","));
                oldModelList.forEach(modelId -> {
                    if (!newModelList.contains(modelId)) {
                        deleteList.add(Integer.parseInt(modelId));
                    }
                });
                // 删除关联信息
                plusProModelRepository.delProModel(programId, deleteList);
                newModelList.forEach(modelId -> {
                    if (!oldModelList.contains(modelId)) {
                        addList.add(Integer.valueOf(modelId));
                    }
                });
                // 新增关联信息
                plusProModelRepository.saveProModel(programId, addList);
            }
            // 修改方案商品信息
            MemberPlusGoodsVo memberPlusGoodsVo = new MemberPlusGoodsVo();
            memberPlusGoodsVo.setId(programId);
            memberPlusGoodsVo.setName(plusProgram.getName());
            memberPlusGoodsVo.setMemberPrice(plusProgram.getMallMobilePrice());
            boolean result = productExternalRepository.updateMemberPlusGoods(memberPlusGoodsVo);
            if (!result) {
                throw new PlusAbyssException("请求商品系统修改会员方案商品信息失败");
            }
            // 修改续费信息
            plusRenewRepository.editRenewRelevance(event, programId);
        }
    }

    @Override
    public void programEffective(SavePlusProgramEvent event) {
        plusProgramMapper.updateProgrammeEffective(event.getId());
    }

    @Override
    public List<PlusProgramEntity> getMemberPlusProgramAll(PlusProgramQueryReq req) {
        return converter.toPlusProgramEntityList(
                plusProgramMapper.getMemberPlusProgramList(req.getConfigId(), req.getId(),
                        req.getChannelId()));
    }

    /**
     * 获取方案唯一标识
     */
    private String getSignProgram() {
        String sign = Sign.getCharAndNum(10);
        Integer count = plusProgramMapper.checkSignProgram(sign);
        if (count > 0) {
            return getSignProgram();
        }
        return sign;
    }

    /**
     * 权益包信息
     */
    private List<PlusProgramProfitsPackageObject> getPlusProgramProfitsPackageObjects(
            Integer programId, PlusOrderEntity order, Integer sendNode) {
        String plusOrderSn = order.getOrderSn();
        List<PlusProgramProfitsPackageObject> profitsPackageObjects = new ArrayList<>();
        // 获取方案已配置的权益列表
        List<PlusProModelEntity> modelList = plusOrderSnapshtoQueryModel.listOrderModelByOrderBySort(order);
        if (CollectionUtils.isEmpty(modelList)) {
            log.info("获取方案配置的权益列表信息为空：{},{}", plusOrderSn, programId);
            return profitsPackageObjects;
        }
        List<Integer> modelIds = modelList.stream().map(PlusProModelEntity::getModelId)
                .collect(Collectors.toList());
        // 券（开卡礼、月享、品牌专区、还款优惠、生日关怀、专属好礼)
        List<PlusProgramProfitsPackageObject> coupons = listCoupons(programId, modelIds);
        if (!CollectionUtils.isEmpty(coupons)) {
            profitsPackageObjects.addAll(coupons);
        }
        // 提额
        PlusProgramProfitsPackageObject liftAmount = listLiftAmount(programId, modelIds);
        if (Objects.nonNull(liftAmount)) {
            profitsPackageObjects.add(liftAmount);
        }
        // 返现（购物、结清、还款）
        List<PlusProgramProfitsPackageObject> cashback = listCashback(programId, order, modelIds);
        if (!CollectionUtils.isEmpty(cashback)) {
            profitsPackageObjects.addAll(cashback);
        }
        // 多买多送
        PlusProgramProfitsPackageObject dmds = listDmds(programId, modelIds);
        if (Objects.nonNull(dmds)) {
            profitsPackageObjects.add(dmds);
        }
        // 拒就赔
        PlusProgramProfitsPackageObject jjp = listJjp(programId, modelIds);
        if (Objects.nonNull(jjp)) {
            profitsPackageObjects.add(jjp);
        }
        // 息费折扣
        PlusProgramProfitsPackageObject xfzk = listXfzk(programId, modelIds);
        if (Objects.nonNull(xfzk)) {
            profitsPackageObjects.add(xfzk);
        }
        // 区间还款
        PlusProgramProfitsPackageObject qjhk = listQjhk(programId, plusOrderSn, modelIds);
        if (Objects.nonNull(qjhk)) {
            profitsPackageObjects.add(qjhk);
        }
        // 虚拟货币（目前是生日关怀的桔豆）
        PlusProgramProfitsPackageObject srghJd = listSrghJd(programId, modelIds);
        if (Objects.nonNull(srghJd)) {
            profitsPackageObjects.add(srghJd);
        }
        // 权益0元发放
        PlusProgramProfitsPackageObject lyff = listLyff(programId, modelIds, order);
        if (Objects.nonNull(lyff)) {
            profitsPackageObjects.add(lyff);
        }
        // 联名卡虚拟权益
        PlusProgramProfitsPackageObject lmqy = lmqy(programId, modelIds, order);
        if (Objects.nonNull(lmqy)) {
            profitsPackageObjects.add(lmqy);
        }
        // 给权益设置发送方式，目前包含：开卡礼、拒就赔、多买多送、月享红包
        dealProfitSendControl(modelList, profitsPackageObjects, sendNode);
        return profitsPackageObjects;
    }

    /**
     * 给权益设置发送方式，目前包含：开卡礼、拒就赔、多买多送、月享红包
     */
    private void dealProfitSendControl(List<PlusProModelEntity> modelList,
            List<PlusProgramProfitsPackageObject> profitsPackageObjects, Integer sendNode) {
        if (CollectionUtils.isEmpty(profitsPackageObjects)) {
            log.info("会员方案权益列表为空，无需处理发送方式");
            return;
        }
        Map<Integer, PlusProfitsGiveTypeEnum> modelIdSendTypeMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(modelList)) {
            modelList.forEach(item -> {
                if (Objects.isNull(item.getSendType())) {
                    return;
                }
                PlusProfitsGiveTypeEnum plusProfitsGiveTypeEnum = PlusProfitsGiveTypeEnum.getByValue(
                        item.getSendType());
                if (Objects.nonNull(plusProfitsGiveTypeEnum)) {
                    modelIdSendTypeMap.put(item.getModelId(), plusProfitsGiveTypeEnum);
                }
            });
        }
        profitsPackageObjects.forEach(item -> {
            if (Objects.nonNull(sendNode)){
                item.setSendNode(sendNode);
            }
            if (!modelIdSendTypeMap.isEmpty()) {
                PlusProfitsGiveTypeEnum plusProfitsGiveTypeEnum = modelIdSendTypeMap.get(item.getModelId());
                if (Objects.nonNull(plusProfitsGiveTypeEnum)) {
                    item.setGiveType(plusProfitsGiveTypeEnum);
                }
            }
        });
    }

    /**
     * 券（开卡礼、月享、品牌专区、还款优惠、生日关怀、专属好礼)
     */
    private List<PlusProgramProfitsPackageObject> listCoupons(Integer programId,
            List<Integer> modelIds) {
        // 只获取方案已配置权益的优惠券配置
        List<Integer> couponType = modelIds.stream().map(PlusCouponTypeEnum::getCouponType)
                .collect(Collectors.toList());
        // 券（开卡礼、月享、品牌专区、还款优惠、生日关怀、专属好礼)
        List<PlusCouponIndexPo> couponIndexList = plusCouponIndexMapper.getCouponByProgramIdAndCouponType(
                programId, couponType);
        if (!CollectionUtils.isEmpty(couponIndexList)) {
            // 将券按权益包分组
            Map<Integer, List<PlusCouponIndexPo>> couponGroups = couponIndexList.stream()
                    .collect(Collectors.groupingBy(PlusCouponIndexPo::getType));
            return couponGroups.entrySet().stream()
                    .map(entry -> PlusProgramProfitsPackageObject.createFromCoupon(
                            converter.toCoupons(entry.getValue()),
                            PlusCouponTypeEnum.getPlusModelType(entry.getKey()), programId))
                    .collect(Collectors.toList());
        }
        return null;
    }

    /**
     * 多买多送
     */
    private PlusProgramProfitsPackageObject listDmds(Integer programId, List<Integer> modelIds) {
        if (!modelIds.contains(PlusModelEnum.DMDS.getModelId())) {
            log.info("获取多买多送配置,方案未配置：{}", programId);
            return null;
        }
        List<PlusProgramTaskPo> taskPoList = taskMapper.getTaskCouponByProgramId(programId);
        if (!CollectionUtils.isEmpty(taskPoList)) {
            List<PlusProgramProfitsCouponObject> collect = taskPoList.stream()
                    .map(e -> converter.toDmdsCoupon(e,
                            Collections.singletonList(getDmdsCondition(e.getOrderPrice()))))
                    .collect(Collectors.toList());
            return PlusProgramProfitsPackageObject.createFromCoupon(collect, PlusModelEnum.DMDS,
                    programId);
        }
        return null;
    }

    /**
     * 拒就赔
     */
    private PlusProgramProfitsPackageObject listJjp(Integer programId, List<Integer> modelIds) {
        if (!modelIds.contains(PlusModelEnum.JJP.getModelId())) {
            log.info("获取拒就赔配置,方案未配置：{}", programId);
            return null;
        }
        List<PlusProgramRejectionPo> rejectionCouponList = rejectionMapper.getRejectionCouponByProgramId(
                programId);
        if (!CollectionUtils.isEmpty(rejectionCouponList)) {
            List<PlusProgramProfitsCouponObject> collect = rejectionCouponList.stream()
                    .map(e -> converter.toJjpCoupon(e,
                            Collections.singletonList(getJJpCondition())))
                    .collect(Collectors.toList());
            return PlusProgramProfitsPackageObject.createFromCoupon(collect, PlusModelEnum.JJP,
                    programId);
        }
        return null;
    }

    /**
     * 息费折扣
     */
    private PlusProgramProfitsPackageObject listXfzk(Integer programId, List<Integer> modelIds) {
        if (!modelIds.contains(PlusModelEnum.XFZKQ.getModelId())) {
            log.info("获取息费折扣券配置,方案未配置：{}", programId);
            return null;
        }
        List<PlusProgramXFZKPo> xfzkPoList = xfzkMapper.getListByProgramId(programId);
        if (!CollectionUtils.isEmpty(xfzkPoList)) {
            List<PlusProgramProfitsCouponObject> collect = xfzkPoList.stream()
                    .map(converter::toXfzkCoupon).collect(Collectors.toList());
            return PlusProgramProfitsPackageObject.createFromCoupon(collect, PlusModelEnum.XFZKQ,
                    programId);
        }
        return null;
    }

    /**
     * 区间还款
     */
    private PlusProgramProfitsPackageObject listQjhk(Integer programId, String plusOrderSn,
            List<Integer> modelIds) {
        if (!modelIds.contains(PlusModelEnum.QJHK.getModelId())) {
            log.info("获取区间还款券配置,方案未配置：{}", programId);
            return null;
        }
        String cache = redisUtils.get(RedisConstantPrefix.RDZX_CREATE_EVET + plusOrderSn);
        log.info("获取融担咨询卡创单参数缓存结果：{},{}", plusOrderSn, cache);
        if (StringUtils.isBlank(cache)) {
            return null;
        }
        PlusOrderCreateEvent event = JSONObject.parseObject(cache, PlusOrderCreateEvent.class);
        PlusRdzxServiceFeeEntity fee = rdzxExternalRepository.getRdzxServiceFeeEntity(event);
        // 获取融担费用
        if (fee == null || fee.getDiscountAmount() == null || fee.getPeriodNum() == null) {
            log.info("获取区间还款券配置,减免金额或期数为空：{}", programId);
            return null;
        }
        Integer repayCouponId = intervalCoupon(fee.getDiscountAmount(), fee.getPeriodNum());
        if (repayCouponId == null) {
            return null;
        }
        PlusProgramProfitsCouponObject coupon = converter.toQjhk(programId, repayCouponId,
                event.getCreateOrderContext() != null ? event.getCreateOrderContext()
                        .getLoanOrderSn() : null);
        return PlusProgramProfitsPackageObject.createFromCoupon(Collections.singletonList(coupon),
                PlusModelEnum.QJHK, programId);
    }

    /**
     * 生日关怀权益的桔豆，券是在plus_coupon_index
     */
    private PlusProgramProfitsPackageObject listSrghJd(Integer programId, List<Integer> modelIds) {
        if (!modelIds.contains(PlusModelEnum.SRGH.getModelId())) {
            log.info("获取生日关怀桔豆配置,方案未配置：{}", programId);
            return null;
        }
        // 桔豆数量目前写死88，只有生日关怀在用
        PlusProgramProfitsVirtualObject virtual = converter.toSrghJd(programId,
                new BigDecimal("88"));
        return PlusProgramProfitsPackageObject.createFromVirtual(Collections.singletonList(virtual),
                PlusModelEnum.SRGH, programId);
    }

    /**
     * 权益0元发放
     */
    private PlusProgramProfitsPackageObject listLyff(Integer programId, List<Integer> modelIds,
            PlusOrderEntity order) {
        if (!modelIds.contains(PlusModelEnum.LYFF.getModelId())) {
            log.info("获取权益0元发放配置,方案未配置：{}", programId);
            return null;
        }
        String plusOrderSn = order.getOrderSn();
        String cache = redisUtils.get(RedisConstantPrefix.RDZX_CREATE_EVET + plusOrderSn);
        log.info("获取权益0元发放配置,获取融担咨询卡创单参数缓存结果：{},{}", plusOrderSn, cache);
        if (StringUtils.isBlank(cache)) {
            return null;
        }
        // 根据借款单号查询融担咨询卡创单任务
        PlusOrderCreateEvent event = JSONObject.parseObject(cache, PlusOrderCreateEvent.class);
        String loanOrderSn = event.getCreateOrderContext().getLoanOrderSn();
        PlusMqRdzxRecordPo orderRecordInfo = rdzxRepository.getOrderRecordInfo(loanOrderSn);
        if (orderRecordInfo == null) {
            log.info("查询融担咨询卡创单任务为空,loanOrderSn:{}", loanOrderSn);
            return null;
        }
        // 判断是否有弹窗标识
        Boolean rdEquityMark = orderRecordInfo.getRdEquityMark();
        if (rdEquityMark == null || !rdEquityMark) {
            log.info("权益0元发放,无弹窗标识,不生成发放计划,loanOrderSn:{}", loanOrderSn);
            return null;
        }
        // 是否匹配权益
        BigDecimal plusAmount = order.getOrderAmount();
        PlusProgramVirtualEntity entity = virtualGoodsExternalRepository.matchVirtual(programId,
                plusAmount);
        if (entity == null) {
            log.info("权益0元发放,未匹配到权益,programId:{},融担咨询费:{}", programId, plusAmount);
            return null;
        }
        // 把配置虚拟权益商品的主键id存入权益发放计划的权益值中
        Integer programVirtualId = entity.getId();
        PlusProgramProfitsVirtualGoodsObject virtualGoods = converter.toLyff(programId,
                String.valueOf(programVirtualId));
        return PlusProgramProfitsPackageObject.createFromVirtualGoods(
                Collections.singletonList(virtualGoods), PlusModelEnum.LYFF, programId);
    }

    /**
     * 生活联名卡
     */
    private PlusProgramProfitsPackageObject lmqy(Integer programId, List<Integer> modelIds,
            PlusOrderEntity order) {
        if (!modelIds.contains(PlusModelEnum.LMQY.getModelId())) {
            log.info("获取联名权益,方案未配置：{}", programId);
            return null;
        }
        String redisKey = RedisConstantPrefix.PLUS_LMK_CREATE_CACHE + order.getOrderSn();
        String cache = redisUtils.get(redisKey);
        log.info("联名权益缓存信息：{}", cache);
        if (StringUtils.isBlank(cache)) {
            log.info("联名权益获取缓存为空，不处理：{}", redisKey);
            return null;
        }
        MemberPlusLmkVirtualEntity virtual = JSONObject.parseObject(cache,
                MemberPlusLmkVirtualEntity.class);
        PlusProgramProfitsVirtualGoodsObject virtualGoods = converter.toLyff(programId,
                String.valueOf(virtual.getVirtualId()));
        return PlusProgramProfitsPackageObject.createFromVirtualGoods(
                Collections.singletonList(virtualGoods), PlusModelEnum.LMQY, programId);
    }

    /**
     * 提额
     */
    private PlusProgramProfitsPackageObject listLiftAmount(Integer programId,
            List<Integer> modelIds) {
        if (!modelIds.contains(PlusModelEnum.HYTE.getModelId())) {
            log.info("获取提额配置,方案未配置：{}", programId);
            return null;
        }
        PlusLiftAmountPo plusLiftAmountPo = plusLiftAmountMapper.getPlusLiftAmountByProgramId(
                programId);
        if (Objects.nonNull(plusLiftAmountPo)) {
            PlusProgramProfitsCreditObject creditObject = converter.toLiftAmount(plusLiftAmountPo);
            return PlusProgramProfitsPackageObject.createFromLiftAmount(
                    Collections.singletonList(creditObject), PlusModelEnum.HYTE, programId);
        }
        return null;
    }

    /**
     * 方案的返现权益（下单时有快照）
     */
    private List<PlusProgramProfitsPackageObject> listCashback(Integer programId,
            PlusOrderEntity order, List<Integer> modelIds) {
        List<PlusProgramProfitsPackageObject> result = new ArrayList<>();
        String plusOrderSn = order.getOrderSn();
        // 结清返现 + 购物返现（只获取方案已配置权益的返现配置）
        List<PlusProgramCashbackSnapshotPo> cashBacks = plusProgramCashbackSnapshotMapper.queryAuthSnapshots(
                plusOrderSn, programId, modelIds);
        if (!CollectionUtils.isEmpty(cashBacks)) {
            List<PlusProgramProfitsCashbackObject> cashbackObjects = cashBacks.stream()
                    .map(e -> this.cashback(e, order)).collect(Collectors.toList());
            // 将券按权益包分组
            Map<Integer, List<PlusProgramProfitsCashbackObject>> cashbackGroups = cashbackObjects.stream()
                    .collect(Collectors.groupingBy(PlusProgramProfitsCashbackObject::getModelId));
            List<PlusProgramProfitsPackageObject> collect = cashbackGroups.entrySet().stream()
                    .map(entry -> PlusProgramProfitsPackageObject.createFromCashBack(
                            entry.getValue(), PlusModelEnum.getPlusModelEnum(entry.getKey()),
                            programId)).collect(Collectors.toList());
            result.addAll(collect);
        }
        // 获取还款返现权益配置信息
        RdzxEquityInfoEntity hkfxInfo = getHkfxInfo(programId, modelIds, order);
        if (Objects.nonNull(hkfxInfo)) {
            List<HkfxAmountInfo> hkfxAmountInfos = hkfxInfo.getHkfxAmountInfos();
            if (!CollectionUtils.isEmpty(hkfxAmountInfos)) {
                hkfxAmountInfos.forEach(info -> {
                    // 返现权益包
                    PlusProgramProfitsCashbackObject cashbackObject = repayCashBack(programId,
                            PlusModelEnum.HKFX.getModelId(), info.getCashBackAmount(),
                            info.getPeriods());
                    // 权益包
                    PlusProgramProfitsPackageObject profitsPackage = PlusProgramProfitsPackageObject.createFromCashBack(
                            Collections.singletonList(cashbackObject), PlusModelEnum.HKFX,
                            programId);
                    result.add(profitsPackage);
                });
            }
        }
        return result;
    }

    /**
     * 获取还款返现权益配置信息
     */
    private RdzxEquityInfoEntity getHkfxInfo(Integer programId, List<Integer> modelIds,
            PlusOrderEntity order) {
        if (!modelIds.contains(PlusModelEnum.HKFX.getModelId())) {
            log.info("获取还款返现配置,方案未配置：{}", programId);
            return null;
        }
        String plusOrderSn = order.getOrderSn();
        String cache = redisUtils.get(RedisConstantPrefix.RDZX_CREATE_EVET + plusOrderSn);
        log.info("获取还款返现配置,获取融担咨询卡创单参数缓存结果：{},{}", plusOrderSn, cache);
        if (StringUtils.isBlank(cache)) {
            return null;
        }
        // 根据借款单号查询融担咨询卡创单任务
        PlusOrderCreateEvent event = JSONObject.parseObject(cache, PlusOrderCreateEvent.class);
        String loanOrderSn = event.getCreateOrderContext().getLoanOrderSn();
        PlusMqRdzxRecordPo orderRecordInfo = rdzxRepository.getOrderRecordInfo(loanOrderSn);
        if (orderRecordInfo == null) {
            log.info("获取还款返现配置,查询融担咨询卡创单任务为空,loanOrderSn:{}", loanOrderSn);
            return null;
        }
        // 判断是否有还款返现弹窗标识
        Boolean flag = orderRecordInfo.getRepayCashBack();
        if (flag == null || !flag) {
            log.info("还款返现,无弹窗标识,不生成发放计划,loanOrderSn:{}", loanOrderSn);
            return null;
        }
        // 匹配还款权益配置和计算返现金额
        BigDecimal loanAmount = event.getCreateOrderContext().getLoanAmount();
        Integer loanPeriod = event.getCreateOrderContext().getLoanPeriod();
        RdzxEquityConfigInfoEvent equityConfigInfoEvent = converter.toRdzxEquityConfigInfoEvent(
                loanAmount, loanPeriod);
        RdzxEquityInfoEntity hkfxInfo = rdzxExternalRepository.getHkfxInfo(programId,
                equityConfigInfoEvent);
        Boolean isMatch = hkfxInfo.getIsMatch();
        if (!isMatch) {
            log.info("还款返现,未匹配到权益,programId:{},借款金额:{},期数:{}", programId, loanAmount, loanPeriod);
            return null;
        }
        return hkfxInfo;
    }

    /**
     * 构建返现类权益对象-还款返现
     */
    private PlusProgramProfitsCashbackObject repayCashBack(Integer programId, Integer modelId,
            BigDecimal cashbackAmount, Integer periods) {
        List<PlusReachConditionObject> conditions = getPlusReachConditionObjects(modelId,
                cashbackAmount, periods);
        // 复用 orderNum 字段,存入期数
        return converter.toCashback(programId, cashbackAmount, modelId, null, conditions, periods,
                null);
    }

    /**
     * 构建返现类权益对象
     */
    private PlusProgramProfitsCashbackObject cashback(PlusProgramCashbackSnapshotPo cashbackPo,
            PlusOrderEntity order) {
        // 返现金额：购物返现类型=会员费用，需要获取会员订单金额
        BigDecimal cashbackAmount = cashbackPo.getCashbackAmount();
        if (cashbackPo.getCashbackType() != null && cashbackPo.getCashbackType()
                .equals(GwfxCashTypeEnum.PLUS_AMOUNT.getCode())) {
            // 联名卡：会员费，排除虚拟权益金额   普通卡：会员订单金额
            cashbackAmount = Objects.equals(order.getBusinessType(), BusinessTypeEnum.LMK.getCode())
                    ? order.getLmkPlusAmount() : order.getOrderAmount();
            log.info("购物返现发放计划会员费用值结果为：{},{}", cashbackPo.getPlusOrderSn(), cashbackAmount);
        }
        List<PlusReachConditionObject> conditions = getPlusReachConditionObjects(
                cashbackPo.getModelId(), cashbackAmount, null);
        return converter.toCashback(cashbackPo.getProgramId(), cashbackAmount,
                cashbackPo.getModelId(), cashbackPo.getId(), conditions, cashbackPo.getOrderNum(),
                cashbackPo.getCashbackType());
    }

    /**
     * 获取返现类权益达成条件
     */
    private List<PlusReachConditionObject> getPlusReachConditionObjects(Integer modelId,
            BigDecimal cashbackAmount, Integer periods) {
        PlusModelEnum plusModelEnum = PlusModelEnum.getPlusModelEnum(modelId);
        switch (plusModelEnum) {
            case JQFX: {
                // 结清返现达成条件
                return getSettleCashbackConditions();
            }
            case GWFX: {
                return getGwCashbackCondition(cashbackAmount);
            }
            case HKFX: {
                return getRepayCashbackCondition(periods);
            }
            default: {
                log.error("计算权益发放条件失败");
                return null;
            }

        }
    }

    /**
     * 融担咨询卡还款返现达成条件
     */
    private List<PlusReachConditionObject> getRepayCashbackCondition(Integer periods) {
        // 还款期数
        PlusReachConditionObject condition = PlusReachConditionObject.create(
                PlusReachConditionTypeEnum.REPAY_PERIODS.getValue(), String.valueOf(periods), "0",
                true);
        return Collections.singletonList(condition);
    }

    /**
     * 小额月卡的结清返现达成条件
     */
    private List<PlusReachConditionObject> getSettleCashbackConditions() {
        // 1. 支付两笔
        PlusReachConditionObject condition1 = PlusReachConditionObject.create(
                PlusReachConditionTypeEnum.XEYK_PAY_COUNT.getValue(), "2", "0", false);
        // 2. 结清
        PlusReachConditionObject condition2 = PlusReachConditionObject.create(
                PlusReachConditionTypeEnum.REL_LOAN_FINISH.getValue(), "1", "0", true);
        return Arrays.asList(condition1, condition2);
    }

    /**
     * 购物返现达成条件
     */
    private List<PlusReachConditionObject> getGwCashbackCondition(BigDecimal cashbackAmount) {
        // 1. 支付两笔
        return Collections.singletonList(PlusReachConditionObject.create(
                PlusReachConditionTypeEnum.GWFX_ORDER_MONEY.getValue(),
                String.valueOf(cashbackAmount), "0", true));
    }

    /**
     * 多买多送达成条件
     */
    private PlusReachConditionObject getDmdsCondition(BigDecimal money) {
        return PlusReachConditionObject.create(
                PlusReachConditionTypeEnum.DMDS_PRODUCT_ORDER_MONEY.getValue(),
                String.valueOf(money), "0", true);
    }

    /**
     * 多买多送达成条件
     */
    private PlusReachConditionObject getJJpCondition() {
        return PlusReachConditionObject.create(
                PlusReachConditionTypeEnum.JJP_ORDER_FK_REJECT.getValue(), "1", "0", true);
    }

    /**
     * 计算融单还款券
     */
    public Integer intervalCoupon(BigDecimal money, Integer periodNum) {
        try {
            List<PlusQjhkCouponConfigEntity> configList = configRepository.getEffectiveList();
            if (CollectionUtils.isEmpty(configList)) {
                log.info("匹配融担咨询卡区间还款优惠券配置列表为空");
                return null;
            }
            PlusQjhkCouponConfigEntity intervalCoupon = configList.stream()
                    .filter(e -> e.getMinAmount().compareTo(money) <= 0
                            && e.getMaxAmount().compareTo(money) > 0 && e.getPeriodNum()
                            .equals(periodNum)).findFirst().orElse(null);
            if (intervalCoupon == null) {
                log.info("未匹配融担咨询卡区间还款优惠券");
                imRepository.sendImMessage("未匹配融担咨询卡区间还款优惠券,减免金额:" + money + "期数:" + periodNum);
                return null;
            }
            return intervalCoupon.getCouponId();
        } catch (Exception e) {
            log.warn("匹配融担卡还款券id异常，分期期数：{}，减免金额：{}", periodNum, money, e);
            return null;
        }
    }

    /**
     * 复制方案
     */
    @Override
    public void copyProgram(CopyProgramEvent event) {
        log.info("方案复制入参：{}", JSON.toJSONString(event));
        Integer programId = event.getProgramId();
        // 获取方案
        PlusProgramPo plusProgram = plusProgramMapper.loadMemberPlusProgram(programId);
        if (plusProgram == null) {
            throw new PlusAbyssException("方案不存在无法复用，方案id：" + programId);
        }
        // 获取方案下所有权益
        List<PlusProModelEntity> plusProModels = plusProModelRepository.getProModelByProgramIdOrderBySort(
                plusProgram.getId());
        if (event.isCopyChannel()) {
            if (CollectionUtils.isEmpty(plusProModels)) {
                throw new PlusAbyssException("方案id：" + programId + " 没有配置任何权益，无法复用");
            }
            // 权益列表过滤，只复制目标渠道勾选的权益
            plusProModels = plusProModels.stream()
                    .filter(e -> event.getPlusProfitList().contains(String.valueOf(e.getModelId())))
                    .collect(Collectors.toList());
            // 复用渠道方案特殊赋值
            plusProgram.setChannel(event.getTargetChannelId());
            // 设置是否支持后付款：渠道管理勾选了后付款 + 源方案支持后付款
            plusProgram.setAfterPayState(event.getOpenModeList()
                    .contains(String.valueOf(ChannelFunctionEnum.PAY_LATER.getCode()))
                    && plusProgram.getAfterPayState() == 1 ? 1 : 2);
        } else {
            // 复制方案特殊赋值
            plusProgram.setEffectiveTime(new Date());
        }
        // 复制方案信息
        saveCopyProgram(plusProgram, event);
        // 新增方案对应的商品信息
        saveCopyCreatePlusGoods(plusProgram);
        // 复制方案权益关系
        savCopyPlusProModel(plusProModels, plusProgram);
        // 复制方案扩展信息
        saveCopyProgramExtend(programId, plusProgram);
        // 复制续费信息
        saveCopyPlusRenew(programId, plusProgram);
        // 复制权益明细
        saveCopyPlusModule(programId, plusProgram, event.isCopyChannel(), plusProModels);
        // hxf 2024.6.13 新增方案日志
        String content =
                event.isCopyChannel() ? PlusProgramLogNodeEnum.LOG_NODE_MULTIPLEX_PROGRAM.getName()
                        : PlusProgramLogNodeEnum.LOG_NODE_COPY_PROGRAM.getName();
        programLogRepository.saveLog(plusProgram.getId(), event.getOptUserId(), event.getOptUser(),
                content, content + ":" + JSON.toJSONString(event));
    }

    @Override
    public ProgramDetailEntity getProgramDetail(Integer id) {
        return plusProgramMapper.getPlusProgramDetailById(id);
    }

    @Override
    public Boolean upAct(String id) {
        return plusProgramMapper.upAct(id);
    }

    @Override
    public Boolean downAct(String id) {
        return plusProgramMapper.downAct(id);
    }

    @Override
    public List<PlusProgramEntity> getUpProgramList() {
        return converter.toPlusProgramEntityList(plusProgramMapper.getMemberPlusProgramListed());
    }

    /**
     * 保存复制方案信息
     */
    private void saveCopyProgram(PlusProgramPo plusProgram, CopyProgramEvent event) {
        // 新方案赋值
        plusProgram.setId(null);
        plusProgram.setCreateUserId(event.getOptUserId());
        plusProgram.setCreateUserName(event.getOptUser());
        plusProgram.setUpdateUserId(event.getOptUserId());
        plusProgram.setUpdateUserName(event.getOptUser());
        plusProgram.setSignProgram(getSignProgram());
        plusProgram.setBackstageName(
                plusProgram.getBackstageName() + "_" + plusProgram.getSignProgram());
        plusProgram.setStatus(CommonConstant.TWO);
        plusProgram.setProgrammeStatus(CommonConstant.ZERO);
        plusProgramMapper.saveMemberPlusProgram(plusProgram);
    }

    /**
     * 保存复制方案扩展表
     */
    private void saveCopyProgramExtend(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制方案扩展信息，原方案:{},新方案:{}", programId, plusProgram.getId());
        PlusProgramExtendPo plusProgramExtendPo = plusProgramExtendMapper.getProgramExtendByPid(
                programId);
        if (plusProgramExtendPo != null) {
            plusProgramExtendPo.setProgramId(plusProgram.getId());
            plusProgramExtendMapper.savePlusProgramExtend(plusProgramExtendPo);
        }
    }

    /**
     * 复制续费信息
     */
    private void saveCopyPlusRenew(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制方案续费信息，原方案:{},新方案:{}", programId, plusProgram.getId());
        // 复制续费信息
        PlusRenewRelevancePo renewInfoPo = relevanceMapper.getRenewInfoByProgramId(programId);
        if (renewInfoPo != null) {
            renewInfoPo.setProgramId(plusProgram.getId());
            relevanceMapper.savePlusRenewRelevance(renewInfoPo);
        }
    }

    /**
     * 复制新方案：创建对应的商品
     */
    private void saveCopyCreatePlusGoods(PlusProgramPo plusProgram) {
        log.info("复制方案，创建一条对应的商品，方案id {}", plusProgram.getId());
        if (plusProgram.getId() > 0) {
            MemberPlusGoodsVo memberPlusGoodsVo = new MemberPlusGoodsVo();
            memberPlusGoodsVo.setId(plusProgram.getId());
            memberPlusGoodsVo.setName(plusProgram.getName());
            memberPlusGoodsVo.setMemberPrice(plusProgram.getMallMobilePrice());
            boolean result = productExternalRepository.createMemberPlusGoods(memberPlusGoodsVo);
            if (!result) {
                throw new PlusAbyssException("复制方案，请求商品系统创建会员方案商品失败");
            }
        }
    }

    /**
     * 复制方案权益关系
     */
    private void savCopyPlusProModel(List<PlusProModelEntity> modelList,
            PlusProgramPo plusProgram) {
        log.info("复制方案权益关系信息，方案id：{} 关系列表:{}", plusProgram.getId(), JSON.toJSONString(modelList));
        if (!CollectionUtils.isEmpty(modelList)) {
            List<PlusProModelPo> proModelList = new ArrayList<>();
            for (PlusProModelEntity plusProModelEntity : modelList) {
                PlusProModelPo po = converter.toPlusProModelPo(plusProModelEntity,
                        plusProgram.getId());
                proModelList.add(po);
            }
            plusProModelMapper.batchInsertPlusProModel(proModelList);
        }
    }

    /**
     * 复制方案下一元购商品信息
     */
    private void saveCopyProgramProduct(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制方案下一元购商品信息，原方案:{},新方案:{}", programId, plusProgram.getId());
        List<PlusProgramProductNewPo> productNews = productNewMapper.selectByProgramId(programId,
                PlusModelEnum.YYG.getModelId());
        if (CollectionUtils.isEmpty(productNews)) {
            return;
        }
        productNews.forEach(x -> x.setProgramId(plusProgram.getId()));
        productNewMapper.batchSaveProductNew(productNews);
    }

    /**
     * 复制权益信息
     */
    private void saveCopyPlusModule(Integer programId, PlusProgramPo plusProgram,
            Boolean isChannelCopy, List<PlusProModelEntity> proModelList) {
        log.info("复制方案权益信息，原方案:{},新方案:{},是否复用渠道:{},权益列表:{}", programId, plusProgram.getId(),
                isChannelCopy, JSON.toJSONString(proModelList));
        if (isChannelCopy) {
            // 复用渠道只复制目标渠道勾选的权益，目前渠道只支持会员提额，生活权益和渠道生活权益
            for (PlusProModelEntity plusProModel : proModelList) {
                if (PlusModelEnum.HYTE.getModelId().equals(plusProModel.getModelId())) {
                    // 复制会员提额
                    saveCopyPlusLiftAmount(programId, plusProgram);
                } else if (PlusModelEnum.SHQY.getModelId().equals(plusProModel.getModelId())) {
                    // 复制生活权益
                    saveCopyPlusVirtual(programId, plusProgram);
                } else if (PlusModelEnum.isLevelVirtualProfitModel(plusProModel.getModelId())) {
                    // 复制渠道生活权益
                    saveCopyPlusVirtualLevel(programId, plusProgram, plusProModel.getModelId());
                }
            }
        } else {
            // 复制会员提额
            saveCopyPlusLiftAmount(programId, plusProgram);
            // 复制会员权益优惠券
            saveCopyPlusCouponIndex(programId, plusProgram);
            // 复制息费折扣券
            saveCopyPlusDisCoupon(programId, plusProgram);
            // 复制拒就赔
            saveCopyPlusRejection(programId, plusProgram);
            // 复制方案任务(多买多送)
            saveCopyPlusProgramTask(programId, plusProgram);
            // 复制生活权益
            saveCopyPlusVirtual(programId, plusProgram);
            // 复制渠道生活权益
            saveCopyPlusVirtualLevel(programId, plusProgram, PlusModelEnum.QDSHQY.getModelId());
            // 复制权益0元发放
            saveLyffVirtualCopy(programId, plusProgram.getId());
            // 复制返现类
            saveCashBackCopy(programId, plusProgram.getId());
            // 复制一元购
            saveCopyProgramProduct(programId, plusProgram);
            // 复制0元商品
            saveLyspProductCopy(programId, plusProgram.getId());
        }
    }

    /**
     * 复制0元商品
     */
    private void saveLyspProductCopy(Integer programId, Integer newProgramId) {
        log.info("方案复制-0元商品，programId={}，newProgramId={}", programId, newProgramId);
        List<PlusProgramProductTypePo> productTypes = programProductTypeMapper.selectTypeListByProgram(
                programId, PlusModelEnum.LYSP.getModelId());
        if (CollectionUtils.isEmpty(productTypes)) {
            return;
        }
        productTypes.forEach(e -> {
            Integer oldTypeId = e.getId();
            // 先新增分类
            e.setId(null);
            e.setProgramId(newProgramId);
            programProductTypeMapper.saveProductType(e);
            // 再新增商品
            List<PlusProgramProductNewPo> list = productNewMapper.selectByTypeId(oldTypeId,
                    PlusModelEnum.LYSP.getModelId());
            if (!CollectionUtils.isEmpty(list)) {
                list.forEach(p -> {
                    p.setTypeId(e.getId());
                    p.setProgramId(newProgramId);
                });
                productNewMapper.batchSaveProductNew(list);
            }
        });
    }

    /**
     * 复制返现类
     */
    private void saveCashBackCopy(Integer programId, Integer newProgramId) {
        log.info("方案复制-返现类配置，programId={}，newProgramId={}", programId, newProgramId);
        List<PlusProgramCashbackPo> cashbackList = cashbackMapper.listByProgramId(programId);
        log.info("方案复制-返现类配置-原有权益，programId={}，source={}", programId,
                JSONObject.toJSONString(cashbackList));
        if (CollectionUtils.isEmpty(cashbackList)) {
            return;
        }
        List<PlusProgramCashbackPo> cashbackAll = new ArrayList<>();
        cashbackList.forEach(cashback -> {
            cashback.setProgramId(newProgramId);
            cashbackAll.add(cashback);
        });
        log.info("方案复制-返现类配置-保存权益，programId={}，source={}", programId,
                JSONObject.toJSONString(cashbackAll));
        if (CollectionUtils.isEmpty(cashbackAll)) {
            return;
        }
        cashbackMapper.insertBatch(cashbackAll);
    }

    /**
     * 复制权益0元发放
     */
    private void saveLyffVirtualCopy(Integer programId, Integer newProgramId) {
        log.info("方案复制-复制权益0元发放，programId={}，newProgramId={}", programId, newProgramId);
        List<PlusProgramVirtualPo> virtualList = plusProgramVirtualMapper.getByProgramIdAndModelId(
                programId, PlusModelEnum.LYFF.getModelId());
        log.info("方案复制-权益0元发放-原有权益，programId={}，source={}", programId,
                JSONObject.toJSONString(virtualList));
        if (CollectionUtils.isEmpty(virtualList)) {
            return;
        }
        List<PlusProgramVirtualPo> virtualAll = new ArrayList<>();
        virtualList.forEach(virtual -> {
            virtual.setProgramId(newProgramId);
            virtualAll.add(virtual);
        });
        log.info("方案复制-权益0元发放-保存权益，programId={}，source={}", programId,
                JSONObject.toJSONString(virtualAll));
        if (CollectionUtils.isEmpty(virtualAll)) {
            return;
        }
        plusProgramVirtualMapper.saveBatch(virtualAll);
    }

    /**
     * 复制会员提额
     */
    private void saveCopyPlusLiftAmount(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制方案会员提额信息，原方案:{},新方案:{}", programId, plusProgram.getId());
        //复制会员提额
        PlusLiftAmountPo plusLiftAmount = plusLiftAmountMapper.getPlusLiftAmountByProgramId(
                programId);
        if (plusLiftAmount != null) {
            plusLiftAmount.setProgramId(plusProgram.getId());
            plusLiftAmountMapper.savePlusLiftAmount(plusLiftAmount);
        }
    }

    /**
     * 复制生活权益
     */
    private void saveCopyPlusVirtual(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制方案生活权益，原方案:{}，新方案:{}", programId, plusProgram.getId());
        //获取方案下的分类数据
        //huxf 2023.11.20 增加权益ID入参
        List<PlusProgramVirtualTypePo> virtualTypes = plusProgramVirtualTypeMapper.getTypeByProgram(
                programId, PlusModelEnum.SHQY.getModelId());
        log.info("复制方案生活权益，原有权益，programId={}，source={}", programId,
                JSONObject.toJSONString(virtualTypes));
        if (CollectionUtils.isEmpty(virtualTypes)) {
            return;
        }
        //权益数据
        List<PlusProgramVirtualPo> virtualAll = new ArrayList<>();
        virtualTypes.forEach(type -> {
            PlusProgramVirtualTypePo targetType = converter.toPlusProgramVirtualTypePo(type);
            targetType.setProgramId(plusProgram.getId());
            plusProgramVirtualTypeMapper.savePlusProgramVirtualType(targetType);
            //查询分类下的权益数据放到virtualAll-newTypeId
            List<PlusProgramVirtualPo> virtualByType = plusProgramVirtualMapper.getVirtualEntityByType(
                    type.getId());
            virtualByType.forEach(profit -> {
                profit.setProgramId(plusProgram.getId());
                profit.setProfitTypeId(targetType.getId());
                virtualAll.add(profit);
            });
        });
        //批量保存权益数据
        log.info("复制方案生活权益，保存权益，programId={}，source={}", programId,
                JSONObject.toJSONString(virtualAll));
        if (CollectionUtils.isEmpty(virtualAll)) {
            return;
        }
        plusProgramVirtualMapper.saveBatch(virtualAll);
    }

    /**
     * 复制虚拟权益-多级分类
     */
    private void saveCopyPlusVirtualLevel(Integer programId, PlusProgramPo plusProgram,
            Integer modelId) {
        log.info("复制方案多级分类生活权益，原方案:{}，新方案:{}", programId, plusProgram.getId());
        //获取方案下的分类数据
        //huxf 2023.11.20 增加权益ID入参
        List<PlusProgramVirtualTypePo> virtualTypes = plusProgramVirtualTypeMapper.getTypeByProgram(
                programId, modelId);
        log.info("复制方案多级分类生活权益，原有权益，programId={}，source={}", programId,
                JSONObject.toJSONString(virtualTypes));
        if (CollectionUtils.isEmpty(virtualTypes)) {
            return;
        }
        //权益数据
        List<PlusProgramVirtualPo> virtualAll = new ArrayList<>();
        virtualTypes.forEach(typeOne -> {
            // 一级分类
            PlusProgramVirtualTypePo targetTypeOne = converter.toPlusProgramVirtualTypePo(typeOne);
            targetTypeOne.setProgramId(plusProgram.getId());
            plusProgramVirtualTypeMapper.savePlusProgramVirtualType(targetTypeOne);
            // 查询二级分类
            List<PlusProgramVirtualTypePo> types = plusProgramVirtualTypeMapper.getTypeByParentId(
                    typeOne.getId());
            if (CollectionUtils.isEmpty(types)) {
                return;
            }
            types.forEach(type -> {
                PlusProgramVirtualTypePo targetType = converter.toPlusProgramVirtualTypePo(type);
                targetType.setProgramId(plusProgram.getId());
                targetType.setParentId(targetTypeOne.getId());
                plusProgramVirtualTypeMapper.savePlusProgramVirtualType(targetType);
                // 查询分类下的权益数据放到virtualAll-newTypeId
                List<PlusProgramVirtualPo> virtualByType = plusProgramVirtualMapper.getVirtualEntityByType(
                        type.getId());
                if (CollectionUtils.isEmpty(virtualByType)) {
                    return;
                }
                virtualByType.forEach(profit -> {
                    profit.setProgramId(plusProgram.getId());
                    profit.setProfitTypeId(targetType.getId());
                    virtualAll.add(profit);
                });
            });
        });
        //批量保存权益数据
        log.info("复制方案多级分类生活权益，保存权益，programId={}，source={}", programId,
                JSONObject.toJSONString(virtualAll));
        if (CollectionUtils.isEmpty(virtualAll)) {
            return;
        }
        plusProgramVirtualMapper.saveBatch(virtualAll);
    }

    /**
     * 复制会员权益优惠券
     */
    private void saveCopyPlusCouponIndex(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制会员权益优惠券，原方案:{}，新方案:{}", programId, plusProgram.getId());
        List<PlusCouponIndexPo> plusCouponIndexPoList = plusCouponIndexMapper.getCouponByProgramId(
                programId);
        if (!CollectionUtils.isEmpty(plusCouponIndexPoList)) {
            plusCouponIndexPoList.forEach(pusCouponIndex -> {
                pusCouponIndex.setProgramId(plusProgram.getId());
            });
            plusCouponIndexMapper.batchInsertPlusCouponIndex(plusCouponIndexPoList);

            Map<Integer, List<PlusCouponIndexPo>> logMap = plusCouponIndexPoList.stream()
                    .filter(item -> Objects.equals(PlusCouponTypeEnum.KKL.getCouponType(), item.getType()) || Objects.equals(PlusCouponTypeEnum.YX.getCouponType(), item.getType()))
                    .collect(Collectors.groupingBy(PlusCouponIndexPo::getType, Collectors.toList()));
            if (MapUtils.isNotEmpty(logMap)){
                logMap.entrySet().forEach(item -> {
                    List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(item.getValue().stream().map(PlusCouponIndexPo::getId).collect(Collectors.toList()),
                            PlusCouponTypeEnum.getPlusModelType(item.getKey()),
                            OptEventEnum.OPT_COUPON_ADD,
                            plusProgram.getCreateUserId(), plusProgram.getCreateUserName());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(logList)) {
                        plusProfitSystemLogRepository.saveBatch(logList);
                    }
                });
            }
        }
    }

    /**
     * 复制息费折扣券
     */
    private void saveCopyPlusDisCoupon(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制息费折扣券，原方案:{}，新方案:{}", programId, plusProgram.getId());
        List<PlusProgramXFZKPo> plusProgramXFZKPoList = xfzkMapper.getListByProgramId(programId);
        if (!CollectionUtils.isEmpty(plusProgramXFZKPoList)) {
            plusProgramXFZKPoList.forEach(plusProgramXFZK -> {
                plusProgramXFZK.setProgramId(plusProgram.getId());
            });
            xfzkMapper.batchInsertPlusProgramXFZK(plusProgramXFZKPoList);
        }
    }

    /**
     * 复制拒就赔
     */
    private void saveCopyPlusRejection(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制拒就赔，原方案:{}，新方案:{}", programId, plusProgram.getId());
        List<PlusProgramRejectionPo> plusProgramRejectionPoList = rejectionMapper.getRejectionCouponByProgramId(
                programId);
        if (!CollectionUtils.isEmpty(plusProgramRejectionPoList)) {
            plusProgramRejectionPoList.forEach(plusProgramRejection -> {
                plusProgramRejection.setProgramId(plusProgram.getId());
            });
            rejectionMapper.batchInsertPlusRejection(plusProgramRejectionPoList);
            // 添加日志
            List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(plusProgramRejectionPoList.stream().map(PlusProgramRejectionPo::getId).collect(Collectors.toList()),
                    PlusModelEnum.JJP,
                    OptEventEnum.OPT_COUPON_ADD,
                    plusProgram.getCreateUserId(), plusProgram.getCreateUserName());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(logList)){
                plusProfitSystemLogRepository.saveBatch(logList);
            }
        }
    }

    /**
     * 复制方案任务(多买多送)
     */
    private void saveCopyPlusProgramTask(Integer programId, PlusProgramPo plusProgram) {
        log.info("复制方案任务，原方案:{}，新方案:{}", programId, plusProgram.getId());
        List<PlusProgramTaskPo> plusProgramTaskPoList = taskMapper.getTaskCouponByProgramId(
                programId);
        if (!CollectionUtils.isEmpty(plusProgramTaskPoList)) {
            plusProgramTaskPoList.forEach(plusProgramTask -> {
                plusProgramTask.setProgramId(plusProgram.getId());
            });
            taskMapper.batchInsertPlusProgramTask(plusProgramTaskPoList);
            // 添加日志
            List<PlusProfitSystemLogEntity> logList = profitAdapter.buildCouponLog(plusProgramTaskPoList.stream().map(PlusProgramTaskPo::getId).collect(Collectors.toList()),
                    PlusModelEnum.DMDS,
                    OptEventEnum.OPT_COUPON_ADD,
                    plusProgram.getCreateUserId(), plusProgram.getCreateUserName());
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(logList)){
                plusProfitSystemLogRepository.saveBatch(logList);
            }
        }
    }

    /**
     * 加载融担咨询卡权益开关缓存
     */
    private void reloadRdzxEquitySwitchCache(PlusProgramExtendPo plusProgram) {
        RdzxEquitySwitchEntity dto = converter.toRdzxEquitySwitchEntity(plusProgram);
        String redisKey = String.format(RedisConstantPrefix.PLUS_PROGRAM_RDZX_EQUITY_SWITCH,
                plusProgram.getProgramId());
        redisUtils.set(redisKey, JSONObject.toJSONString(dto));
    }
}
