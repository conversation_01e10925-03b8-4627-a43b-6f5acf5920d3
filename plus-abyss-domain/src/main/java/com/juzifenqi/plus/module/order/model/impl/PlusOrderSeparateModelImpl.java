package com.juzifenqi.plus.module.order.model.impl;

import com.alibaba.fastjson.JSONObject;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.plus.config.ConfigProperties;
import com.juzifenqi.plus.constants.CommonConstant;
import com.juzifenqi.plus.constants.PlusConstant;
import com.juzifenqi.plus.constants.SerialNoPrefixConstant;
import com.juzifenqi.plus.enums.*;
import com.juzifenqi.plus.enums.pay.PayStateCodeEnum;
import com.juzifenqi.plus.enums.supplier.SeparateTypeEnum;
import com.juzifenqi.plus.enums.supplier.SupplierSeparateEnableStateEnum;
import com.juzifenqi.plus.enums.supplier.SupplierTypeEnum;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.IFmsRepository;
import com.juzifenqi.plus.module.common.IIMRepository;
import com.juzifenqi.plus.module.common.IPlusShuntRepository;
import com.juzifenqi.plus.module.common.entity.UserCardNewEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierEntity;
import com.juzifenqi.plus.module.common.entity.shunt.PlusShuntSupplierSeparateEntity;
import com.juzifenqi.plus.module.common.entity.shunt.ShutSupplierBriefEntity;
import com.juzifenqi.plus.module.order.application.converter.IPlusOrderApplicationConverter;
import com.juzifenqi.plus.module.order.model.PlusOrderSeparateModel;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderSeparateRepository;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderShuntRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDeductResEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemAdminEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateItemEntity;
import com.juzifenqi.plus.module.order.model.event.PlusDeductEvent;
import com.juzifenqi.plus.module.order.model.event.order.PlusOrderPayDeductRespEvent;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderShuntPo;
import com.juzifenqi.plus.utils.SerialNoUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 订单分账model实现
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/4 09:53
 */
@Slf4j
@Component
public class PlusOrderSeparateModelImpl implements PlusOrderSeparateModel {

    @Autowired
    private   IPlusOrderSeparateRepository separateRepository;
    @Autowired
    private   IIMRepository                imRepository;
    @Autowired
    private   IPlusShuntRepository         shuntRepository;
    @Autowired
    protected IPlusOrderShuntRepository    orderShuntRepository;
    @Autowired
    protected IFmsRepository               fmsRepository;
    @Autowired
    private   ConfigProperties             configProperties;

    private final IPlusOrderApplicationConverter converter = IPlusOrderApplicationConverter.instance;

    /**
     * 支付回调分账处理
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/4 10:33
     */
    @Override
    public void payNotify(NewPayResultCallbackEntity entity) {
        // 通过订单号+业务申请流水号查询分账记录表
        PlusOrderSeparateEntity separateEntity = separateRepository.getOrderSeparateByApplyNo(
                entity.getOrderId(), entity.getThirdPayNum());
        if (separateEntity == null) {
            throw new PlusAbyssException(
                    "支付回调处理分账，未查询到订单分账记录 " + entity.getOrderId() + ","
                            + entity.getThirdPayNum());
        }
        if (!SeparateStateEnum.PROCESSING.getCode().equals(separateEntity.getSeparateState())) {
            log.info("支付回调处理分账，分账记录已是终态无法处理 {} {}", entity.getOrderId(),
                    entity.getThirdPayNum());
            //            imRepository.sendImMessage(
            //                    "支付回调处理分账，分账记录已是终态无法处理，订单号:" + entity.getOrderId());
            return;
        }
        if (!PayStateCodeEnum.S.getCode().equals(entity.getState()) && !PayStateCodeEnum.F.getCode()
                .equals(entity.getState())) {
            throw new PlusAbyssException(
                    "支付回调处理分账，未知的回调状态 " + entity.getOrderId() + ","
                            + entity.getState());
        }
        log.info("新支付系统回调处理分账，查询分账记录 {} {}", separateEntity.getId(),
                separateEntity.getApplySerialNo());
        PlusOrderSeparateEntity updateSeparateEntity = new PlusOrderSeparateEntity();
        updateSeparateEntity.setId(separateEntity.getId());
        updateSeparateEntity.setPaySerialNo(entity.getSerialNumber());
        updateSeparateEntity.setPayCallbackTime(new Date());
        updateSeparateEntity.setSeparateState(PayStateCodeEnum.S.getCode().equals(entity.getState())
                ? SeparateStateEnum.SUCCESS.getCode() : SeparateStateEnum.FAIL.getCode());
        updateSeparateEntity.setRemark(entity.getPayProductCode());
        if (!PayStateCodeEnum.S.getCode().equals(entity.getState())) {
            if (StringUtils.isNotBlank(entity.getPayErrorMsg())
                    && entity.getPayErrorMsg().length() > 150) {
                updateSeparateEntity.setRemark(entity.getPayErrorMsg().substring(150));
            } else {
                updateSeparateEntity.setRemark(entity.getPayErrorMsg());
            }
        }
        separateRepository.updateOrderSeparate(updateSeparateEntity);
    }

    /**
     * 获取默认卡划扣申请
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/8/31 10:30
     */
    @Override
    public PlusOrderDeductResEntity getDeductApplyEventByDefaultCard(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan) {
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        // 获取划扣申请数据
        PlusOrderSeparateEntity separateEntity = getDeductApplyEntity(orderInfo, deductPlan);
        if (separateEntity == null) {
            // 异常则结束本次划扣
            resEntity.setRemark("获取划扣申请数据失败");
            resEntity.setOptStatus(DeductResultStateEnum.NO.getCode());
            return resEntity;
        }
        // 查询划扣银行卡
        Long cardId = getDeductBankId(deductPlan.getUserId(), orderInfo.getChannelId());
        if (cardId == null) {
            // 获取不到划扣卡，则进延迟划扣，需要组装支付返回res对象
            resEntity.setRemark("获取不到划扣银行卡，进延迟划扣");
            resEntity.setOptStatus(DeductResultStateEnum.FAIL.getCode());
            PlusOrderPayDeductRespEvent respEvent = new PlusOrderPayDeductRespEvent();
            resEntity.setDeductRespEvent(respEvent);
            respEvent.setState(PayStateCodeEnum.F.getCode());
            respEvent.setCode(CommonConstant.errorCardCode);
            respEvent.setMessage("查询用户卡信息，获取不到划扣银行卡");
            return resEntity;
        }
        separateEntity.setBankCardId(cardId);
        separateEntity.setChannelId(orderInfo.getChannelId());
        // 保存分账数据
        return getSavePlusOrderDeductApplyResult(resEntity, separateEntity);
    }

    /**
     * 获取指定卡划扣申请
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/8/31 10:30
     */
    @Override
    public PlusOrderDeductResEntity getDeductApplyEventByAppointCard(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan) {
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        // 获取划扣申请数据
        PlusOrderSeparateEntity separateEntity = getDeductApplyEntity(orderInfo, deductPlan);
        if (separateEntity == null) {
            // 异常则结束本次划扣
            resEntity.setRemark("获取划扣申请数据失败");
            resEntity.setOptStatus(DeductResultStateEnum.NO.getCode());
            return resEntity;
        }
        // 指定划扣银行卡
        separateEntity.setBankCardId(deductPlan.getBankId().longValue());
        separateEntity.setChannelId(orderInfo.getChannelId());
        // 保存分账数据
        return getSavePlusOrderDeductApplyResult(resEntity, separateEntity);
    }

    /**
     * 获取划扣申请数据
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/8/31 10:30
     */
    private PlusOrderSeparateEntity getDeductApplyEntity(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan) {
        try {
            // 获取分流主体信息
            PlusShuntSupplierEntity shuntSupplierEntity = getOrderShuntEntityNew(
                    orderInfo.getOrderSn(), orderInfo.getChannelId(), deductPlan.getConfigId());
            // 计算分账数据
            return getCalSeparate(orderInfo, deductPlan, shuntSupplierEntity);
        } catch (Exception e) {
            // 飞书报警，划扣失败
            LogUtil.printLog(e, "获取划扣申请数据异常");
            imRepository.sendImMessage(e.getMessage());
        }
        return null;
    }

    /**
     * 获取分流主体
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/8/31 09:45
     */
    private Integer getOrderShuntSupplierId(String plusOrderSn, Integer configId) {
        if (Objects.equals(configId, JuziPlusEnum.RDZX_CARD.getCode())) {
            return configProperties.defaultRdzxSupplierId;
        }
        // 非（桔享、加速、固额）默认是兜底主体
        if (!PlusConstant.SHUNT_CARD_LIST.contains(configId)) {
            return configProperties.defaultSupplierId;
        }
        // 查询订单的分流主体
        PlusOrderShuntPo shuntPo = orderShuntRepository.getByOrderSn(plusOrderSn);
        if (shuntPo == null || shuntPo.getPlanInSupplier() == null) {
            // 兼容逻辑，未配置也默认是兜底主体
            return configProperties.defaultSupplierId;
        }
        return shuntPo.getPlanInSupplier();
    }


    private ShutSupplierBriefEntity getOrderShuntSupplier(String plusOrderSn, Integer configId) {
        ShutSupplierBriefEntity entity = new ShutSupplierBriefEntity();
        if (Objects.equals(configId, JuziPlusEnum.RDZX_CARD.getCode())) {
            entity.setSupplierId(configProperties.defaultRdzxSupplierId);
            return entity;
        }
        // 非（桔享、加速、固额）默认是兜底主体
        if (!PlusConstant.SHUNT_CARD_LIST.contains(configId)) {
            entity.setSupplierId(configProperties.defaultSupplierId);
            return entity;
        }
        // 查询订单的分流主体
        PlusOrderShuntPo shuntPo = orderShuntRepository.getByOrderSn(plusOrderSn);
        if (shuntPo == null || shuntPo.getPlanInSupplier() == null) {
            // 兼容逻辑，未配置也默认是兜底主体
            entity.setSupplierId(configProperties.defaultSupplierId);
        } else {
            entity.setSupplierId(shuntPo.getPlanInSupplier());
            entity.setBusinessScene(shuntPo.getPlanInBusinessScene());
        }
        return entity;
    }

    /**
     * 获取分流主体信息和清分信息
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/8/31 09:45
     */
    private PlusShuntSupplierEntity getOrderShuntEntity(String plusOrderSn, Integer configId) {
        // 查询分流主体id
        Integer shuntSupplierId = getOrderShuntSupplierId(plusOrderSn, configId);
        // 从缓存查询分流主体信息
        PlusShuntSupplierEntity shuntSupplierCache = shuntRepository.getSupplierCache(
                shuntSupplierId);
        if (shuntSupplierCache == null || shuntSupplierCache.getPay() == null
                || StringUtils.isBlank(shuntSupplierCache.getPay().getBusinessScene())) {
            throw new PlusAbyssException("划扣查询分流主体，分流缓存数据异常 " + shuntSupplierId);
        }
        // 判断是否需要清分
        if (SupplierSeparateEnableStateEnum.YES.getCode()
                .equals(shuntSupplierCache.getSeparateEnableState())) {
            if (CollectionUtils.isEmpty(shuntSupplierCache.getSeparateList())) {
                throw new PlusAbyssException("划扣查询分流主体，分流主体配置异常，配置清分但清分列表为空 "
                        + shuntSupplierId);
            }
            // 查询清分主体缓存
            for (PlusShuntSupplierSeparateEntity s : shuntSupplierCache.getSeparateList()) {
                PlusShuntSupplierEntity separateSupplierCache = shuntRepository.getSupplierCache(
                        s.getSeparateSupplierId());
                if (separateSupplierCache == null || separateSupplierCache.getPay() == null
                        || StringUtils.isBlank(separateSupplierCache.getPay().getMerchantId())) {
                    throw new PlusAbyssException(
                            "划扣查询分流主体，清分主体缓存数据异常 " + s.getSeparateSupplierId());
                }
                s.setMerchantId(separateSupplierCache.getPay().getMerchantId());
            }
        }
        return shuntSupplierCache;
    }

    private PlusShuntSupplierEntity getOrderShuntEntityNew(String plusOrderSn, Integer channelId, Integer configId) {
        // 查询分流主体
        ShutSupplierBriefEntity orderShuntSupplier = getOrderShuntSupplier(plusOrderSn, configId);
        Integer shuntSupplierId = orderShuntSupplier.getSupplierId();
        String businessScene = orderShuntSupplier.getBusinessScene();
        PlusShuntSupplierEntity shuntSupplierCache;
        if (StringUtils.isNotBlank(orderShuntSupplier.getBusinessScene())) {
            shuntSupplierCache = shuntRepository.getSupplierCacheNew(channelId, shuntSupplierId, businessScene);
        } else {
            shuntSupplierCache = shuntRepository.getSupplierCache(shuntSupplierId);
        }
        // 判断是否需要清分
        if (SupplierSeparateEnableStateEnum.YES.getCode().equals(shuntSupplierCache.getSeparateEnableState())) {
            if (CollectionUtils.isEmpty(shuntSupplierCache.getSeparateList())) {
                throw new PlusAbyssException("划扣查询分流主体，分流主体配置异常，配置清分但清分列表为空 "
                        + shuntSupplierId);
            }
            // 查询清分主体缓存
            for (PlusShuntSupplierSeparateEntity s : shuntSupplierCache.getSeparateList()) {
                PlusShuntSupplierEntity separateSupplierCache = shuntRepository.getSupplierCache(
                        s.getSeparateSupplierId());
                if (separateSupplierCache == null || separateSupplierCache.getPay() == null
                        || StringUtils.isBlank(separateSupplierCache.getPay().getMerchantId())) {
                    throw new PlusAbyssException(
                            "划扣查询分流主体，清分主体缓存数据异常 " + s.getSeparateSupplierId());
                }
                s.setMerchantId(separateSupplierCache.getPay().getMerchantId());
            }
        }
        return shuntSupplierCache;
    }


    /**
     * 获取计算分账数据
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/8/31 11:13
     */
    private PlusOrderSeparateEntity getCalSeparate(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan, PlusShuntSupplierEntity shuntSupplierEntity) {
        // 分账主表
        PlusOrderSeparateEntity separateEntity = new PlusOrderSeparateEntity();
        separateEntity.setOrderSn(orderInfo.getOrderSn());
        separateEntity.setUserId(deductPlan.getUserId());
        separateEntity.setApplySerialNo(
                getApplySerialNo(deductPlan.getPayAction(), orderInfo.getOrderSn()));
        separateEntity.setOrderPayAction(deductPlan.getPayAction().getCode());
        separateEntity.setShuntSupplierId(shuntSupplierEntity.getId());
        separateEntity.setSeparateEnableState(shuntSupplierEntity.getSeparateEnableState());
        separateEntity.setSettleEnableState(shuntSupplierEntity.getSettleEnableState());
        separateEntity.setTotalSeparateAmount(orderInfo.getOrderAmount());
        String businessScene = shuntSupplierEntity.getPay().getBusinessScene();
        separateEntity.setBusinessScene(businessScene);
        separateEntity.setSeparateState(SeparateStateEnum.PROCESSING.getCode());
        // 分账明细表
        List<PlusOrderSeparateItemEntity> items = new ArrayList<>();
        if (SupplierSeparateEnableStateEnum.YES.getCode()
                .equals(shuntSupplierEntity.getSeparateEnableState())) {
            // 清分明细
            shuntSupplierEntity.getSeparateList().forEach(item -> {
                PlusOrderSeparateItemEntity itemEntity = new PlusOrderSeparateItemEntity();
                itemEntity.setOrderSn(separateEntity.getOrderSn());
                itemEntity.setApplySerialNo(separateEntity.getApplySerialNo());
                itemEntity.setSupplierType(SupplierTypeEnum.QF.getCode());
                itemEntity.setSupplierId(item.getSeparateSupplierId());
                itemEntity.setMerchantId(item.getMerchantId());
                itemEntity.setSeparateType(item.getSeparateType());
                itemEntity.setSeparateRate(item.getSeparateRate());
                // 计算清分金额
                if (SeparateTypeEnum.RATE.getCode().equals(item.getSeparateType())) {
                    if (item.getSeparateRate().compareTo(BigDecimal.ZERO) <= 0) {
                        throw new PlusAbyssException(
                                "分流主体金额计算异常，清分比例配置异常:" + item.getSeparateRate());
                    }
                    // 按比例清分，分账金额=订单金额*分流比例，保留2位小数四舍五入
                    itemEntity.setSeparateAmount(
                            separateEntity.getTotalSeparateAmount().multiply(item.getSeparateRate())
                                    .setScale(2, RoundingMode.HALF_UP));
                } else {
                    // 固定金额
                    itemEntity.setSeparateAmount(item.getSeparateAmount());
                }
                items.add(itemEntity);
            });
            // 分流明细
            PlusOrderSeparateItemEntity itemEntity = new PlusOrderSeparateItemEntity();
            itemEntity.setOrderSn(separateEntity.getOrderSn());
            itemEntity.setApplySerialNo(separateEntity.getApplySerialNo());
            itemEntity.setSupplierType(SupplierTypeEnum.FL.getCode());
            itemEntity.setSupplierId(shuntSupplierEntity.getId());
            PlusOrderSeparateItemEntity subItemEntity = items.get(0);
            itemEntity.setSeparateType(subItemEntity.getSeparateType());
            if (SeparateTypeEnum.RATE.getCode().equals(subItemEntity.getSeparateType())) {
                // 清分比例separate_rate=1-清分明细比例总和
                BigDecimal totalRate = items.stream()
                        .map(PlusOrderSeparateItemEntity::getSeparateRate)
                        .reduce(BigDecimal.ZERO, BigDecimal::add);
                itemEntity.setSeparateRate(BigDecimal.ONE.subtract(totalRate));
            }
            // 分账金额=订单金额-清分明细的分账金额总和
            BigDecimal totalAmount = items.stream()
                    .map(PlusOrderSeparateItemEntity::getSeparateAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal amount = separateEntity.getTotalSeparateAmount().subtract(totalAmount);
            if (amount.compareTo(BigDecimal.ZERO) < 0) {
                throw new PlusAbyssException(
                        "分流主体金额计算异常，主体id:" + shuntSupplierEntity.getId() + " 金额:"
                                + amount);
            }
            itemEntity.setSeparateAmount(amount);
            items.add(itemEntity);
        } else {
            // 只分流，不清分
            PlusOrderSeparateItemEntity itemEntity = new PlusOrderSeparateItemEntity();
            itemEntity.setOrderSn(separateEntity.getOrderSn());
            itemEntity.setApplySerialNo(separateEntity.getApplySerialNo());
            itemEntity.setSupplierType(SupplierTypeEnum.FL.getCode());
            itemEntity.setSupplierId(shuntSupplierEntity.getId());
            itemEntity.setSeparateAmount(separateEntity.getTotalSeparateAmount());
            items.add(itemEntity);
        }
        separateEntity.setItems(items);
        return separateEntity;
    }

    private String getApplySerialNo(OrderPayActionEnum payAction, String orderSn) {
        log.debug("【生成申请流水号】开始生成申请流水号 orderSn={}, payAction={}", orderSn, payAction);

        if (Objects.isNull(payAction) || StringUtils.isBlank(orderSn)) {
            log.error("【生成申请流水号】参数校验失败 orderSn={}, payAction={}", orderSn, payAction);
            throw new PlusAbyssException("生成支付流水信息缺少");
        }

        String applySerialNo;
        switch (payAction) {
            case PAY_ACTION_1:
                applySerialNo = SerialNoUtils.generateApplySerialNo(
                        SerialNoPrefixConstant.SERIAL_NO_PREFIX_ZD, orderSn);
                log.info("【生成申请流水号】主动支付流水号生成成功 orderSn={}, applySerialNo={}, prefix={}",
                        orderSn, applySerialNo, SerialNoPrefixConstant.SERIAL_NO_PREFIX_ZD);
                break;
            case PAY_ACTION_2:
                applySerialNo = SerialNoUtils.generateApplySerialNo(
                        SerialNoPrefixConstant.SERIAL_NO_PREFIX_HK, orderSn);
                log.info("【生成申请流水号】划扣支付流水号生成成功 orderSn={}, applySerialNo={}, prefix={}",
                        orderSn, applySerialNo, SerialNoPrefixConstant.SERIAL_NO_PREFIX_HK);
                break;
            default:
                log.error("【生成申请流水号】不支持的支付行为 orderSn={}, payAction={}", orderSn, payAction);
                throw new PlusAbyssException("订单支付行为无效");
        }

        log.debug("【生成申请流水号】申请流水号生成完成 orderSn={}, payAction={}, applySerialNo={}",
                orderSn, payAction, applySerialNo);

        return applySerialNo;
    }

    ;

    /**
     * 获取划扣卡id
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/8/31 10:30
     */
    private Long getDeductBankId(Integer userId, Integer channelId) {
        try {
            // 查询新支付系统用户的银行卡列表
            List<UserCardNewEntity> cardList = fmsRepository.getBankListByUserId(userId,
                    String.valueOf(channelId));
            if (CollectionUtils.isEmpty(cardList)) {
                throw new PlusAbyssException("划扣查询用户默认卡，无可用银行卡" + userId);
            }
            //2025-02-17 by axq  如果用户只有一张卡,返回这张卡的id
            if (cardList.size() == 1) {
                return Long.parseLong(cardList.get(0).getId());
            }
            // 剔除所有需要重新签约的卡 是否需要重新签约：Y-是，N-否
            List<UserCardNewEntity> bindList = cardList.stream()
                    .filter(card -> StringUtils.isBlank(card.getRebind()) || "N".equals(
                            card.getRebind())).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(bindList)) {
                // 优先取默认卡，存在多张默认卡取第一张，如果没有默认卡则取卡列表第一张
                UserCardNewEntity cardData = bindList.stream()
                        .filter(card -> "Y".equals(card.getIsDefault())).findFirst().orElse(null);
                return Long.parseLong(cardData != null ? cardData.getId() : bindList.get(0).getId());
            }
            UserCardNewEntity cardData = cardList.stream()
                    .filter(card -> "Y".equals(card.getIsDefault())).findFirst().orElse(null);
            return Long.parseLong(cardData != null ? cardData.getId() : cardList.get(0).getId());
        } catch (Exception e) {
            // 飞书报警，划扣失败
            LogUtil.printLog(e, "划扣查询用户默认卡，获取划扣卡id异常");
            imRepository.sendImMessage(e.getMessage());
        }
        return null;
    }

    /**
     * 获取保存分账数据结果
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/3 20:37
     */
    private PlusOrderDeductResEntity getSavePlusOrderDeductApplyResult(
            PlusOrderDeductResEntity resEntity, PlusOrderSeparateEntity separateEntity) {
        if (!saveDeductApply(separateEntity)) {
            // 保存数据失败则认为划扣失败
            resEntity.setRemark("保存分账数据失败");
            resEntity.setOptStatus(DeductResultStateEnum.NO.getCode());
            return resEntity;
        }
        resEntity.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
        resEntity.setSeparateEntity(separateEntity);
        return resEntity;
    }

    /**
     * 保存划扣申请数据
     *
     * <AUTHOR>
     * @version 1.0
     * @date 2024/9/4 10:35
     */
    private Boolean saveDeductApply(PlusOrderSeparateEntity separateEntity) {
        try {
            // 保存分账数据
            Long id = separateRepository.saveOrderSeparate(separateEntity);
            separateEntity.setId(id);
            return true;
        } catch (Exception e) {
            // 飞书报警，划扣失败
            LogUtil.printLog(e, "保存划扣申请数据异常");
            imRepository.sendImMessage(e.getMessage());
        }
        return false;
    }

    /**
     * 查询订单清分明细
     */
    @Override
    public List<PlusOrderSeparateItemAdminEntity> getOrderSeparateItemList(String orderSn) {
        return separateRepository.getOrderSeparateItemList(orderSn,
                SupplierSeparateEnableStateEnum.YES.getCode());
    }

    /**
     * 同步划扣结果更新分账记录的状态和流水号
     */
    @Override
    public void deductUpdateOrderSeparate(PlusOrderDeductResEntity resEntity) {
        if (resEntity == null) {
            return;
        }
        PlusOrderPayDeductRespEvent respEvent = resEntity.getDeductRespEvent();
        PlusOrderSeparateEntity separateEntity = resEntity.getSeparateEntity();
        // 更新分账记录的状态和流水号
        if (respEvent != null && separateEntity != null) {
            PlusOrderSeparateEntity updateEntity = new PlusOrderSeparateEntity();
            updateEntity.setId(separateEntity.getId());
            updateEntity.setPaySerialNo(respEvent.getSerialNumber());
            if (!PayStateCodeEnum.I.getCode().equals(respEvent.getState())) {
                updateEntity.setSeparateState(SeparateStateEnum.FAIL.getCode());
                if (StringUtils.isNotBlank(respEvent.getMessage())
                        && respEvent.getMessage().length() > 150) {
                    updateEntity.setRemark(respEvent.getMessage().substring(150));
                } else {
                    updateEntity.setRemark(respEvent.getMessage());
                }
            }
            separateRepository.updateOrderSeparate(updateEntity);
        }
    }

    @Override
    public PlusOrderDeductResEntity getPlusOrderSeparateInfo(PlusOrderEntity orderInfo,
            PlusDeductEvent deductPlan) {
        PlusOrderDeductResEntity resEntity = new PlusOrderDeductResEntity();
        // 获取划扣申请数据
        PlusOrderSeparateEntity separateEntity = getDeductApplyEntity(orderInfo, deductPlan);
        if (separateEntity == null) {
            // 异常则结束本次划扣
            resEntity.setRemark("获取支付申请数据失败");
            resEntity.setOptStatus(DeductResultStateEnum.NO.getCode());
            return resEntity;
        }
        // 保存分账数据
        return getSavePlusOrderDeductApplyResult(resEntity, separateEntity);
    }

    @Override
    public List<PlusOrderSeparateEntity> getPlusOrderSeparate(String orderSn) {
        return separateRepository.getOrderSeparateByOrderNo(orderSn);
    }

    @Override
    public PlusOrderDeductResEntity getSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity) {
        long startTime = System.currentTimeMillis();
        String orderSn = plusOrderEntity.getOrderSn();

        log.info("开始获取第二期支付分账信息 orderSn={}, userId={}, orderAmount={}, firstPayAmount={} ",
                orderSn, plusOrderEntity.getUserId(), plusOrderEntity.getOrderAmount(),
                plusOrderEntity.getFirstPayAmount());

        try {
            // 查询第一期支付的分账记录
            log.info("查询第一期支付分账记录 orderSn={}", orderSn);
            List<PlusOrderSeparateEntity> allSeparates = getPlusOrderSeparate(orderSn);
            log.info("查询到分账记录总数 orderSn={}, totalCount={}", orderSn, allSeparates.size());

            List<PlusOrderSeparateEntity> firstPeriodSeparates = allSeparates.stream()
                    .filter(s -> OrderPayActionEnum.PAY_ACTION_3.getCode().equals(s.getOrderPayAction())
                            && SeparateStateEnum.SUCCESS.getCode().equals(s.getSeparateState()))
                    .collect(Collectors.toList());

            log.info("筛选第一期分账记录结果 orderSn={}, firstPeriodCount={}", orderSn, firstPeriodSeparates.size());

            if (firstPeriodSeparates.isEmpty()) {
                log.error("未找到第一期支付的分账记录 orderSn={}, allSeparatesInfo={}",
                        orderSn, allSeparates.stream().map(s ->
                        "id=" + s.getId() + ",payAction=" + s.getOrderPayAction() + ",state=" + s.getSeparateState())
                        .collect(Collectors.joining("; ")));
                throw new PlusAbyssException("未找到第一期支付的分账记录，无法进行第二期支付");
            }

            // 获取第一期的分账信息作为模板
            PlusOrderSeparateEntity firstPeriodSeparate = firstPeriodSeparates.get(0);
            log.info("获取第一期分账模板 orderSn={}, separateId={}, shuntSupplierId={}, businessScene={}, totalAmount={}",
                    orderSn, firstPeriodSeparate.getId(), firstPeriodSeparate.getShuntSupplierId(),
                    firstPeriodSeparate.getBusinessScene(), firstPeriodSeparate.getTotalSeparateAmount());

            // 计算第二期支付金额
            BigDecimal secondPayAmount = getSecondPayAmount(plusOrderEntity);
            log.info("第二期支付金额计算完成 orderSn={}, secondPayAmount={}", orderSn, secondPayAmount);

            // 复用第一期的分流清分信息，但使用第二期的支付金额
            log.info("开始创建第二期分账信息 orderSn={}, secondPayAmount={}", orderSn, secondPayAmount);
            PlusOrderSeparateEntity secondPeriodSeparateInfo = createSecondPeriodSeparateInfo(plusOrderEntity, firstPeriodSeparate, secondPayAmount);
            //保存分账数据
            saveDeductApply(secondPeriodSeparateInfo);

            PlusOrderDeductResEntity result = new PlusOrderDeductResEntity();
            result.setOptStatus(DeductResultStateEnum.SUCCESS.getCode());
            result.setSeparateEntity(secondPeriodSeparateInfo);
            result.setRemark("第二期支付分账信息生成成功");

            long endTime = System.currentTimeMillis();
            log.info("完成获取第二期支付分账信息 orderSn={}, success={}, costTime={}ms",
                    orderSn, DeductResultStateEnum.SUCCESS.getCode().equals(result.getOptStatus()),
                    (endTime - startTime));

            return result;
        } catch (PlusAbyssException e) {
            long endTime = System.currentTimeMillis();
            log.error("获取第二期支付分账信息业务异常 orderSn={}, error={}, costTime={}ms",
                    orderSn, e.getMessage(), (endTime - startTime));
            throw e;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            log.error("获取第二期支付分账信息系统异常 orderSn={}, costTime={}ms",
                    orderSn, (endTime - startTime), e);
            throw new PlusAbyssException("获取第二期支付分账信息失败: " + e.getMessage());
        }
    }

    /**
     * 计算第二期支付金额
     */
    private BigDecimal getSecondPayAmount(PlusOrderEntity plusOrderEntity) {
        String orderSn = plusOrderEntity.getOrderSn();
        log.debug("【第二期金额计算】开始计算第二期支付金额 orderSn={}", orderSn);

        // 获取订单总金额和首付金额
        BigDecimal totalAmount = plusOrderEntity.getOrderAmount();
        BigDecimal firstPayAmount = plusOrderEntity.getFirstPayAmount();

        log.info("【第二期金额计算】获取金额信息 orderSn={}, totalAmount={}, firstPayAmount={}",
                orderSn, totalAmount, firstPayAmount);

        // 参数校验
        if (totalAmount == null || totalAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("【第二期金额计算】订单总金额无效 orderSn={}, totalAmount={}", orderSn, totalAmount);
            throw new PlusAbyssException("订单总金额不能为空或小于等于0");
        }

        if (firstPayAmount == null || firstPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("【第二期金额计算】首付金额无效 orderSn={}, firstPayAmount={}", orderSn, firstPayAmount);
            throw new PlusAbyssException("首付金额不能为空或小于等于0");
        }

        if (firstPayAmount.compareTo(totalAmount) >= 0) {
            log.error("【第二期金额计算】首付金额不能大于等于订单总金额 orderSn={}, firstPayAmount={}, totalAmount={}",
                    orderSn, firstPayAmount, totalAmount);
            throw new PlusAbyssException("首付金额不能大于等于订单总金额");
        }

        // 计算第二期支付金额 = 订单总金额 - 首付金额
        BigDecimal secondPayAmount = totalAmount.subtract(firstPayAmount);

        log.info("【第二期金额计算】第二期支付金额计算结果 orderSn={}, secondPayAmount={}", orderSn, secondPayAmount);

        if (secondPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("【第二期金额计算】第二期支付金额计算错误 orderSn={}, secondPayAmount={}, totalAmount={}, firstPayAmount={}",
                    orderSn, secondPayAmount, totalAmount, firstPayAmount);
            throw new PlusAbyssException("第二期支付金额计算错误，金额不能小于等于0");
        }

        log.debug("【第二期金额计算】第二期支付金额计算完成 orderSn={}, secondPayAmount={}", orderSn, secondPayAmount);
        return secondPayAmount;
    }
    
    /**
     * 创建第二期支付的分账信息
     */
    private PlusOrderSeparateEntity createSecondPeriodSeparateInfo(PlusOrderEntity plusOrderEntity,
                                                                 PlusOrderSeparateEntity firstPeriodTemplate, BigDecimal secondPayAmount) {

        String orderSn = plusOrderEntity.getOrderSn();
        Integer userId = plusOrderEntity.getUserId();

        log.info("【创建第二期分账】开始创建第二期支付分账信息 orderSn={}, userId={}, secondPayAmount={}",
                orderSn, userId, secondPayAmount);

        // 参数校验
        if (firstPeriodTemplate == null) {
            log.error("【创建第二期分账】第一期分账模板为空 orderSn={}", orderSn);
            throw new PlusAbyssException("第一期分账模板不能为空");
        }

        if (secondPayAmount == null || secondPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
            log.error("【创建第二期分账】第二期支付金额无效 orderSn={}, secondPayAmount={}", orderSn, secondPayAmount);
            throw new PlusAbyssException("第二期支付金额不能为空或小于等于0");
        }

        log.debug("【创建第二期分账】第一期分账模板信息 orderSn={}, firstSeparateEntity={}", orderSn, JSONObject.toJSONString(firstPeriodTemplate));

        // 创建第二期分账主记录
        log.debug("【创建第二期分账】开始创建第二期分账主记录 orderSn={}", orderSn);
        PlusOrderSeparateEntity secondPeriodSeparate = new PlusOrderSeparateEntity();
        secondPeriodSeparate.setOrderSn(orderSn);
        secondPeriodSeparate.setUserId(userId);

        // 生成申请流水号
        String applySerialNo = getApplySerialNo(OrderPayActionEnum.PAY_ACTION_1, orderSn);
        secondPeriodSeparate.setApplySerialNo(applySerialNo);
        secondPeriodSeparate.setOrderPayAction(OrderPayActionEnum.PAY_ACTION_4.getCode());

        log.info("【创建第二期分账】生成申请流水号 orderSn={}, applySerialNo={}, orderPayAction={}",
                orderSn, applySerialNo, OrderPayActionEnum.PAY_ACTION_4.getCode());

        // 复用第一期的分流主体信息
        log.debug("【创建第二期分账】复用第一期分流主体信息 orderSn={}", orderSn);
        secondPeriodSeparate.setShuntSupplierId(firstPeriodTemplate.getShuntSupplierId());
        secondPeriodSeparate.setSeparateEnableState(firstPeriodTemplate.getSeparateEnableState());
        secondPeriodSeparate.setSettleEnableState(firstPeriodTemplate.getSettleEnableState());
        secondPeriodSeparate.setBusinessScene(firstPeriodTemplate.getBusinessScene());
        secondPeriodSeparate.setTotalSeparateAmount(secondPayAmount);
        secondPeriodSeparate.setSeparateState(SeparateStateEnum.PROCESSING.getCode());

        log.info("【创建第二期分账】第二期分账主记录配置完成 orderSn={}, secondPeriodSeparate={}",
                orderSn, JSONObject.toJSONString(secondPeriodSeparate));

        // 复用第一期的清分主体配置，重新计算分账金额
        log.info("【创建第二期分账】开始创建第二期分账明细 orderSn={}, secondPayAmount={}", orderSn, secondPayAmount);
        List<PlusOrderSeparateItemEntity> secondPeriodItems = createSecondPeriodSeparateItems(orderSn,
                secondPeriodSeparate, secondPayAmount);

        secondPeriodSeparate.setItems(secondPeriodItems);
        return secondPeriodSeparate;
    }
    
    /**
     * 创建第二期支付的分账明细
     */
    private List<PlusOrderSeparateItemEntity> createSecondPeriodSeparateItems(
            String orderSn, PlusOrderSeparateEntity secondPeriodSeparate,
            BigDecimal secondPayAmount) {

        log.info("【创建第二期分账明细】开始创建第二期支付分账明细 orderSn={}, secondPayAmount={}", orderSn, secondPayAmount);

        try {
            // 查询第一期支付成功的清分主体明细记录
            log.info("【创建第二期分账明细】查询第一期支付成功的清分主体明细记录 orderSn={}, targetPayAction={}",
                    orderSn, OrderPayActionEnum.PAY_ACTION_3.getCode());

            List<PlusOrderSeparateItemEntity> firstPeriodItems = separateRepository.getSeparateItemsByOrderSnAndPayAction(
                    orderSn, OrderPayActionEnum.PAY_ACTION_3.getCode());

            log.info("【创建第二期分账明细】查询到第一期分账明细记录 orderSn={}, itemCount={}", orderSn,
                    firstPeriodItems != null ? firstPeriodItems.size() : 0);

            // 判断是否有清分主体（QF类型）记录
            boolean qfFlag = CollectionUtils.isEmpty(firstPeriodItems) || firstPeriodItems.stream()
                    .noneMatch(item -> SupplierTypeEnum.QF.getCode().equals(item.getSupplierType()));

            log.info("【创建第二期分账明细】清分主体判断结果 orderSn={}, qfFlag={}", orderSn, qfFlag);

            List<PlusOrderSeparateItemEntity> secondPeriodItems = new ArrayList<>();

            if (!qfFlag) {
                List<PlusOrderSeparateItemEntity> plusOrderSeparateItemEntities = handleQf(orderSn, secondPeriodSeparate, secondPayAmount, firstPeriodItems);
                secondPeriodItems.addAll(plusOrderSeparateItemEntities);
            } else {
                // 只分流，不清分的情况
                log.info("【创建第二期分账明细】处理只分流不清分的情况 orderSn={}", orderSn);
                PlusOrderSeparateItemEntity shuntItem = getPlusOrderSeparateItemEntity(secondPeriodSeparate, secondPayAmount);
                secondPeriodItems.add(shuntItem);
                log.info("【创建第二期分账明细】只分流明细创建完成 orderSn={}, shuntAmount={}", orderSn, secondPayAmount);
            }

            log.info("【创建第二期分账明细】第二期支付分账明细创建完成 orderSn={}, totalItemCount={}, hasQfItems={}",
                    orderSn, secondPeriodItems.size(), !qfFlag);

            return secondPeriodItems;

        } catch (Exception e) {
            log.error("【创建第二期分账明细】创建第二期支付分账明细异常 orderSn={}, error={}", orderSn, e.getMessage(), e);
            throw new PlusAbyssException("创建第二期支付分账明细失败：" + e.getMessage());
        }
    }

    /**
     * 处理有清分主体的情况
     */
    private  List<PlusOrderSeparateItemEntity> handleQf(String orderSn, PlusOrderSeparateEntity secondPeriodSeparate, BigDecimal secondPayAmount, List<PlusOrderSeparateItemEntity> firstPeriodItems) {
        // 有清分主体的情况：需要处理清分明细和分流明细
        log.info("【创建第二期分账明细】开始处理有清分主体的情况 orderSn={}", orderSn);

        List<PlusOrderSeparateItemEntity> secondPeriodItems = new ArrayList<>();
        // 处理清分主体（QF类型）明细
        for (PlusOrderSeparateItemEntity firstItem : firstPeriodItems) {
            if (SupplierTypeEnum.QF.getCode().equals(firstItem.getSupplierType())) {
                log.debug("【创建第二期分账明细】处理清分主体明细 orderSn={}, supplierId={}, separateType={}, separateRate={}, separateAmount={}",
                        orderSn, firstItem.getSupplierId(), firstItem.getSeparateType(),
                        firstItem.getSeparateRate(), firstItem.getSeparateAmount());

                PlusOrderSeparateItemEntity secondItem = getPlusOrderSeparateItemEntity(secondPeriodSeparate, secondPayAmount, firstItem);
                secondPeriodItems.add(secondItem);
            }
        }

        log.info("【创建第二期分账明细】清分主体明细处理完成 orderSn={}, qfItemCount={}", orderSn, secondPeriodItems.size());

        // 创建分流明细
        log.debug("【创建第二期分账明细】开始创建分流明细 orderSn={}", orderSn);
        PlusOrderSeparateItemEntity shuntItem = new PlusOrderSeparateItemEntity();
        shuntItem.setOrderSn(secondPeriodSeparate.getOrderSn());
        shuntItem.setApplySerialNo(secondPeriodSeparate.getApplySerialNo());
        shuntItem.setSupplierType(SupplierTypeEnum.FL.getCode());
        shuntItem.setSupplierId(secondPeriodSeparate.getShuntSupplierId());

        Integer separateType = firstPeriodItems.get(0).getSeparateType();
        shuntItem.setSeparateType(separateType);

        log.debug("【创建第二期分账明细】分流明细基础信息设置完成 orderSn={}, supplierId={}, separateType={}",
                orderSn, shuntItem.getSupplierId(), separateType);

        if (SeparateTypeEnum.RATE.getCode().equals(separateType)) {
            // 按比例分账：分流比例 = 1 - 清分明细比例总和
            BigDecimal totalClearingRate = secondPeriodItems.stream()
                    .map(PlusOrderSeparateItemEntity::getSeparateRate)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal shuntRate = BigDecimal.ONE.subtract(totalClearingRate);
            shuntItem.setSeparateRate(shuntRate);

            log.info("【创建第二期分账明细】按比例分账设置完成 orderSn={}, totalClearingRate={}, shuntRate={}",
                    orderSn, totalClearingRate, shuntRate);
        }

        // 分流金额 = 第二期总金额 - 清分明细的分账金额总和
        BigDecimal totalClearingAmount = secondPeriodItems.stream()
                .map(PlusOrderSeparateItemEntity::getSeparateAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal shuntAmount = secondPayAmount.subtract(totalClearingAmount);

        log.info("【创建第二期分账明细】分流金额计算完成 orderSn={}, totalClearingAmount={}, shuntAmount={}",
                orderSn, totalClearingAmount, shuntAmount);

        if (shuntAmount.compareTo(BigDecimal.ZERO) < 0) {
            log.error("【创建第二期分账明细】分流金额计算异常 orderSn={}, shuntSupplierId={}, shuntAmount={}, totalClearingAmount={}, secondPayAmount={}",
                    orderSn, secondPeriodSeparate.getShuntSupplierId(), shuntAmount, totalClearingAmount, secondPayAmount);
            throw new PlusAbyssException(
                    "第二期支付分流主体金额计算异常，分流主体id:" + secondPeriodSeparate.getShuntSupplierId()
                            + " 金额:" + shuntAmount);
        }

        shuntItem.setSeparateAmount(shuntAmount);
        shuntItem.setRemark("第二期支付分流明细，复用第一期分流主体配置");
        secondPeriodItems.add(shuntItem);

        log.info("【创建第二期分账明细】分流明细创建完成 orderSn={}, shuntAmount={}", orderSn, shuntAmount);
        return  secondPeriodItems;
    }

    private  PlusOrderSeparateItemEntity getPlusOrderSeparateItemEntity(PlusOrderSeparateEntity secondPeriodSeparate, BigDecimal secondPayAmount) {
        PlusOrderSeparateItemEntity shuntItem = new PlusOrderSeparateItemEntity();
        shuntItem.setOrderSn(secondPeriodSeparate.getOrderSn());
        shuntItem.setApplySerialNo(secondPeriodSeparate.getApplySerialNo());
        shuntItem.setSupplierType(SupplierTypeEnum.FL.getCode());
        shuntItem.setSupplierId(secondPeriodSeparate.getShuntSupplierId());
        shuntItem.setSeparateAmount(secondPayAmount);
        shuntItem.setRemark("第二期支付分流明细，只分流不清分");
        return shuntItem;
    }

    private PlusOrderSeparateItemEntity getPlusOrderSeparateItemEntity(PlusOrderSeparateEntity secondPeriodSeparate, BigDecimal secondPayAmount, PlusOrderSeparateItemEntity firstItem) {
        String orderSn = secondPeriodSeparate.getOrderSn();
        
        log.debug("【创建清分明细项】开始创建第二期清分明细项 orderSn={}, firstItem={}",orderSn, JSONObject.toJSONString(firstItem));

        try {
            PlusOrderSeparateItemEntity secondItem = new PlusOrderSeparateItemEntity();

            // 复用第一期的配置信息
            secondItem.setOrderSn(orderSn);
            secondItem.setApplySerialNo(secondPeriodSeparate.getApplySerialNo());
            secondItem.setSupplierType(firstItem.getSupplierType());
            secondItem.setSupplierId(firstItem.getSupplierId());
            secondItem.setMerchantId(firstItem.getMerchantId());
            secondItem.setSeparateType(firstItem.getSeparateType());
            secondItem.setSeparateRate(firstItem.getSeparateRate());

            log.debug("【创建清分明细项】基础配置信息复用完成 orderSn={}, secondItem={}",orderSn, JSONObject.toJSONString(secondItem));

            // 重新计算分账金额
            BigDecimal newSeparateAmount;
            if (SeparateTypeEnum.RATE.getCode().equals(firstItem.getSeparateType())) {
                // 按比例分账：第二期金额 * 分账比例
                newSeparateAmount = secondPayAmount.multiply(firstItem.getSeparateRate())
                        .setScale(2, RoundingMode.HALF_UP);

                log.info("【创建清分明细项】按比例分账计算完成 orderSn={}, supplierId={}, secondPayAmount={}, separateRate={}, newSeparateAmount={}",
                        orderSn, firstItem.getSupplierId(), secondPayAmount, firstItem.getSeparateRate(), newSeparateAmount);
            } else {
                // 固定金额分账：使用第一期的固定金额
                newSeparateAmount = firstItem.getSeparateAmount();

                log.info("【创建清分明细项】固定金额分账设置完成 orderSn={}, supplierId={}, fixedAmount={}",
                        orderSn, firstItem.getSupplierId(), newSeparateAmount);
            }

            // 金额校验
            if (newSeparateAmount == null || newSeparateAmount.compareTo(BigDecimal.ZERO) < 0) {
                log.error("【创建清分明细项】分账金额计算异常 orderSn={}, supplierId={}, newSeparateAmount={}, secondPayAmount={}, separateRate={}",
                        orderSn, firstItem.getSupplierId(), newSeparateAmount, secondPayAmount, firstItem.getSeparateRate());
                throw new PlusAbyssException("清分明细分账金额计算异常，金额不能为负数");
            }

            secondItem.setSeparateAmount(newSeparateAmount);
            secondItem.setRemark("第二期支付清分明细，复用第一期清分主体配置");

            log.debug("【创建清分明细项】第二期清分明细项创建完成 orderSn={}, supplierId={}, separateAmount={}",
                    orderSn, secondItem.getSupplierId(), secondItem.getSeparateAmount());

            return secondItem;

        } catch (Exception e) {
            log.error("【创建清分明细项】创建第二期清分明细项异常 orderSn={}, supplierId={}, error={}",
                    orderSn, firstItem.getSupplierId(), e.getMessage(), e);
            throw new PlusAbyssException("创建第二期清分明细项失败：" + e.getMessage());
        }
    }

    @Override
    public PlusOrderDeductResEntity getInstallmentPaymentSeparateInfo(PlusOrderEntity plusOrderEntity) {

        // 判断是否为分期支付
        boolean isInstallmentPayment = PlusOrderPayTypeEnum.PAY_FIRST_PERIOD.getValue().equals(plusOrderEntity.getPayType());

        PayParam payParam = getPayParam(plusOrderEntity.getOrderSn(), plusOrderEntity, isInstallmentPayment);

        if (OrderPayActionEnum.PAY_ACTION_3.equals(payParam.payAction)) {
            // 第一期支付：使用 firstPayAmount 生成分账信息
            return getFirstPeriodSeparateInfo(plusOrderEntity, payParam.deductPlan);
        } else if (OrderPayActionEnum.PAY_ACTION_4.equals(payParam.payAction)) {
            // 第二期支付：复用第一期的分流清分信息，计算剩余金额
            return getSecondPeriodSeparateInfo(plusOrderEntity);
        } else {
            throw new PlusAbyssException("不支持的分期支付类型");
        }
    }

    @Override
    public PlusOrderDeductResEntity getFirstPeriodSeparateInfo(PlusOrderEntity plusOrderEntity,
            PlusDeductEvent deductPlan) {
        // 使用 firstPayAmount 作为支付金额
        BigDecimal firstPayAmount = plusOrderEntity.getFirstPayAmount();
        if (firstPayAmount == null || firstPayAmount.compareTo(BigDecimal.ZERO) <= 0) {
            throw new PlusAbyssException("分期支付首付金额不能为空或小于等于0");
        }

        // 临时修改订单金额为首付金额，用于分账计算
        plusOrderEntity.setOrderAmount(firstPayAmount);
        return getPlusOrderSeparateInfo(plusOrderEntity, deductPlan);
    }

    @Override
    public PayParam getPayParam(String orderSn, PlusOrderEntity plusOrderEntity, boolean isInstallmentPayment) {
        OrderPayActionEnum payAction;
        String tradeName;
        if (isInstallmentPayment) {
            // 分期支付：判断是第一期还是第二期
            List<PlusOrderSeparateEntity> existingSeparates = getPlusOrderSeparate(orderSn);
            boolean isFirstPeriod = existingSeparates.isEmpty() ||
                existingSeparates.stream().noneMatch(s ->
                    OrderPayActionEnum.PAY_ACTION_3.getCode().equals(s.getOrderPayAction()) && SeparateStateEnum.SUCCESS.getCode().equals(s.getSeparateState()));

            if (isFirstPeriod) {
                // 分期首付
                payAction = OrderPayActionEnum.PAY_ACTION_3;
                tradeName = "会员分期首付";
            } else {
                // 分期二付
                payAction = OrderPayActionEnum.PAY_ACTION_4;
                tradeName = "会员分期二付";
            }
        } else {
            // 主动支付
            payAction = OrderPayActionEnum.PAY_ACTION_1;
            tradeName = "会员主动支付";
        }

        PlusDeductEvent deductPlan = converter.toPlusDeductEvent(plusOrderEntity, tradeName, payAction);
        return new PayParam(payAction, deductPlan);
    }

}
