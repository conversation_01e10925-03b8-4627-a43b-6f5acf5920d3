package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramQjhkPo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 区间还款表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/25 10:06
 */
@Mapper
public interface IPlusProgramQjhkMapper {

    /**
     * 新增返回ID
     */
    Integer save(PlusProgramQjhkPo plusProgramQjhk);


    /**
     * 更新
     */
    Integer update(@Param("plusProgramQjhk") PlusProgramQjhkPo plusProgramQjhk);

    /**
     * 详情
     */
    PlusProgramQjhkPo getByProgramId(Integer programId);

    /**
     * 详情
     */
    PlusProgramQjhkPo getById(Integer id);

}
