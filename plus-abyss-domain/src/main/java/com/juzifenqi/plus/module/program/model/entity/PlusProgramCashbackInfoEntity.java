package com.juzifenqi.plus.module.program.model.entity;


import java.util.Date;
import lombok.Data;

/**
 * PlusProgramCashbackInfoEntity 
 * <AUTHOR> 
 * @Description  
 * @Date 2024/06/21 15:17
**/
@Data
public class PlusProgramCashbackInfoEntity {

    /**
     * 主键
     */
    private Integer id;

    /**
     * 用户ID
     */
    private Integer userId;

    /**
     * 渠道id
     */
    private Integer channelId;

    /**
     * 会员类型ID
     */
    private Integer configId;

    /**
     * 方案ID
     */
    private Integer programId;

    /**
     * 会员订单号
     */
    private String plusOrderSn;

    /**
     * 开店状态 1=开店成功 2=开店失败
     */
    private Integer openStore;

    /**
     * 开店失败原因
     */
    private String openStoreFailMsg;

    /**
     * 开店重试次数
     */
    private Integer openStoreRetryCount;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;
}
