package com.juzifenqi.plus.module.program.repository.impl;

import com.alibaba.fastjson.JSON;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramZskfRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramZskfEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramZskfEvent;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramZskfConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramZskfMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramZskfPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * <AUTHOR>
 * @createTime 2024/2/29 17:23
 * @description
 */
@Repository
@Slf4j
public class IPlusProgramZskfRepositoryImpl implements IPlusProgramZskfRepository {
    private final PlusProgramZskfConverter converter = PlusProgramZskfConverter.instance;

    @Autowired
    private IPlusProgramZskfMapper zskfMapper;

    @Override
    public PlusProgramZskfEntity getByProgramId(Integer programId) {
        log.info("获取专属客服权益信息：{}", programId);
        PlusProgramZskfPo plusProgramZskfPo = zskfMapper.selectByProgramId(programId);
        log.info("获取专属客服权益信息返回：{}", JSON.toJSONString(plusProgramZskfPo));
        return converter.toPlusProgramZskfEntity(plusProgramZskfPo);
    }

    @Override
    public void saveZskf(CreatePlusProgramZskfEvent event) {
        log.info("保存专属客服权益信息：{}", JSON.toJSONString(event));
        PlusProgramZskfPo plusProgramZskfPo = zskfMapper.selectByProgramId(event.getProgramId());
        if (plusProgramZskfPo == null) {
            zskfMapper.insert(converter.toPlusProgramZskfPo(event));
            log.info("专属客服权益新增：{}", event.getProgramId());
        } else {
            plusProgramZskfPo.setUpdateUserId(event.getCreateUserId());
            plusProgramZskfPo.setUpdateUserName(event.getCreateUserName());
            plusProgramZskfPo.setReturnUrl(event.getReturnUrl());
            zskfMapper.update(plusProgramZskfPo);
            log.info("专属客服权益更新：{}，{}", event.getProgramId(), plusProgramZskfPo.getId());
        }
    }
}
