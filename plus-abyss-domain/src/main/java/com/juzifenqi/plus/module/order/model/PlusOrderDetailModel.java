package com.juzifenqi.plus.module.order.model;

import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailMemberEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailProfitEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderDetailRefundEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.PlusOrderEntity;
import com.juzifenqi.plus.module.order.model.contract.entity.separate.PlusOrderSeparateEntity;
import com.sun.tools.corba.se.idl.constExpr.Plus;

import java.util.List;

/**
 * 订单详情领域model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/2 15:25
 */
public interface PlusOrderDetailModel {

    /**
     * 获取订单信息
     */
    PlusOrderDetailOrderEntity getOrderInfo(PlusOrderEntity orderEntity);

    /**
     * 获取订单支付信息
     */
    List<PlusOrderSeparateEntity> getOrderSeparateInfo(PlusOrderEntity orderEntity);

    /**
     * 获取退款信息
     */
    PlusOrderDetailRefundEntity getRefundInfo(PlusOrderEntity orderEntity);

    /**
     * 获取退款信息列表
     * @param orderEntity
     * @return
     */
    List<PlusOrderDetailRefundEntity> getFirstRefundList(PlusOrderEntity orderEntity);

    /**
     * 获取用户信息
     */
    PlusOrderDetailMemberEntity getMemberInfo(PlusOrderEntity orderEntity);

    /**
     * 获取权益信息
     */
    PlusOrderDetailProfitEntity getProfitInfo(PlusOrderEntity orderEntity);
}
