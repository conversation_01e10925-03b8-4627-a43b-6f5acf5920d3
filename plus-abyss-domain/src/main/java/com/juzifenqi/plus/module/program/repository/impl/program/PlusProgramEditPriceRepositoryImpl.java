package com.juzifenqi.plus.module.program.repository.impl.program;

import com.juzifenqi.plus.dto.req.admin.program.PlusProgramEditQueryReq;
import com.juzifenqi.plus.enums.program.PlusProgramEditPriceResultEnum;
import com.juzifenqi.plus.module.program.model.contract.program.IPlusProgramEditPriceRepository;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import com.juzifenqi.plus.module.program.repository.converter.IPlusProgramEditPriceConverter;
import com.juzifenqi.plus.module.program.repository.dao.IPlusProgramEditPriceMapper;
import com.juzifenqi.plus.module.program.repository.po.PlusProgramEditPricePo;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 方案改价
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 15:08
 */
@Repository
public class PlusProgramEditPriceRepositoryImpl implements IPlusProgramEditPriceRepository {

    private final IPlusProgramEditPriceConverter converter = IPlusProgramEditPriceConverter.instance;

    @Autowired
    private IPlusProgramEditPriceMapper editPriceMapper;

    @Override
    public List<PlusProgramEditPriceEntity> getProgramEditPriceList(PlusProgramEditQueryReq req) {
        List<PlusProgramEditPricePo> list = editPriceMapper.getMemberPlusEditPriceByProgramPage(
                req.getPragramId(), req.getStartPage(), req.getPageSize());
        return converter.toPlusProgramEditPriceEntityList(list);
    }

    @Override
    public Integer countProgramEditPrice(PlusProgramEditQueryReq req) {
        return editPriceMapper.countMemberPlusEditPriceByProgram(req.getPragramId());
    }

    @Override
    public void addProgramEditPrice(CreateProgramEditPriceEvent event) {
        PlusProgramEditPricePo po = converter.toPlusProgramEditPricePo(event);
        po.setResult(PlusProgramEditPriceResultEnum.WAIT.getCode());
        editPriceMapper.saveMemberPlusEditPrice(po);
    }

    @Override
    public List<PlusProgramEditPriceEntity> getJobList() {
        List<PlusProgramEditPricePo> list = editPriceMapper.getLastDataByResultAndTime(
                PlusProgramEditPriceResultEnum.WAIT.getCode());
        return converter.toPlusProgramEditPriceEntityList(list);
    }

    @Override
    public void updateProgramEditPrice(PlusProgramEditPriceEntity entity,
            boolean updateProgramResult) {
        editPriceMapper.updateResultById(entity.getId(),
                updateProgramResult ? PlusProgramEditPriceResultEnum.SUCCESS.getCode()
                        : PlusProgramEditPriceResultEnum.FAIL.getCode());
        editPriceMapper.updateResultByTimeAndProgramIdAndIgnoreId(entity.getProgramId(),
                entity.getId(), PlusProgramEditPriceResultEnum.WAIT.getCode(),
                PlusProgramEditPriceResultEnum.FAIL_NOT.getCode());
    }
}
