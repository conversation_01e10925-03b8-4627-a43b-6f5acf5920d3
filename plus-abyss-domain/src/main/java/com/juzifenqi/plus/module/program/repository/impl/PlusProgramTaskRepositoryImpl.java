package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.module.program.model.contract.IPlusProgramTaskRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramTaskEntity;
import com.juzifenqi.plus.module.program.repository.converter.PlusProgramTaskConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramTaskMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramTaskPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 会员权益任务
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/27 14:13
 */
@Repository
@Slf4j
public class PlusProgramTaskRepositoryImpl implements IPlusProgramTaskRepository {

    private final PlusProgramTaskConverter converter = PlusProgramTaskConverter.instance;

    @Autowired
    private IPlusProgramTaskMapper taskMapper;

    @Override
    public List<PlusProgramTaskEntity> getByProgramId(Integer programId) {
        List<PlusProgramTaskPo> task = taskMapper.getTaskCouponByProgramId(programId);
        return converter.toPlusProgramTaskEntity(task);
    }
}
