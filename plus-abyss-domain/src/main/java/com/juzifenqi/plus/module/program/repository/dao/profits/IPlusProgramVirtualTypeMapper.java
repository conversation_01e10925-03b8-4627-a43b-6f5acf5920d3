package com.juzifenqi.plus.module.program.repository.dao.profits;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramVirtualTypePo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * @Author:gaoyu
 * @Date: 2022-05-19 10:28:29
 * @Description: 生活权益分类表
 */
@Mapper
public interface IPlusProgramVirtualTypeMapper {

    /**
     * 新增返回ID
     */
    Integer savePlusProgramVirtualType(PlusProgramVirtualTypePo plusProgramVirtualType);

    /**
     * 删除
     */
    Integer deletePlusProgramVirtualType(@Param("id") Integer id);

    /**
     * 更新
     */
    Integer updatePlusProgramVirtualType(
            @Param("plusProgramVirtualType") PlusProgramVirtualTypePo plusProgramVirtualType);

    /**
     * Load查询
     */
    PlusProgramVirtualTypePo loadPlusProgramVirtualType(@Param("id") Integer id);

    /**
     * 根据方案查询vo
     */
    List<PlusProgramVirtualTypePo> getVirtualTypeByProgram(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    /**
     * 根据方案查询实体-复制用
     */
    List<PlusProgramVirtualTypePo> getTypeByProgram(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId);

    /**
     * 根据方案查询vo
     */
    List<PlusProgramVirtualTypePo> getVirtualTypeByLevel(@Param("programId") Integer programId,
            @Param("modelId") Integer modelId, @Param("typeLevel") Integer typeLevel);

    /**
     * 查询分类排序是否存在
     */
    Integer checkVirtualTypeRankNumExists(
            @Param("plusProgramVirtualType") PlusProgramVirtualTypePo plusProgramVirtualType);

    /**
     * 查询父分类的子分类列表
     */
    List<PlusProgramVirtualTypePo> getTypeByParentId(@Param("parentId") Integer parentId);
}
