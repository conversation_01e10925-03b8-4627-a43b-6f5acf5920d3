package com.juzifenqi.plus.module.order.repository.impl;

import com.juzifenqi.plus.dto.req.admin.OrderBillQueryReq;
import com.juzifenqi.plus.enums.bill.ContractUploadStateEnum;
import com.juzifenqi.plus.enums.bill.SignStateEnum;
import com.juzifenqi.plus.enums.bill.TrailStateEnum;
import com.juzifenqi.plus.module.order.model.contract.IPlusOrderBillRepository;
import com.juzifenqi.plus.module.order.model.contract.entity.bill.PlusOrderBillEntity;
import com.juzifenqi.plus.module.order.repository.converter.IPlusOrderBillRepositoryConverter;
import com.juzifenqi.plus.module.order.repository.dao.IPlusOrderBillMapper;
import com.juzifenqi.plus.module.order.repository.po.PlusOrderBillPo;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 订单对账
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/19 10:43
 */
@Slf4j
@Repository
public class PlusOrderBillRepositoryImpl implements IPlusOrderBillRepository {

    private final IPlusOrderBillRepositoryConverter converter = IPlusOrderBillRepositoryConverter.instance;

    @Autowired
    private IPlusOrderBillMapper plusOrderBillMapper;

    @Override
    public void saveOrderBill(PlusOrderBillEntity entity) {
        PlusOrderBillPo orderBill = converter.toPlusOrderBillPo(entity);
        orderBill.setTrailState(TrailStateEnum.DUCT_ING.getCode());
        orderBill.setSignState(SignStateEnum.SIGN_STATE_1.getCode());
        orderBill.setContractUploadState(ContractUploadStateEnum.WAIT_UPLOAD.getCode());
        orderBill.setSignRetryCount(0);
        orderBill.setInRetryCount(0);
        orderBill.setOutRetryCount(0);
        orderBill.setContractUploadRetryCount(0);
        plusOrderBillMapper.savePlusOrderBill(orderBill);
    }

    @Override
    public PlusOrderBillEntity getByOrderSn(String orderSn) {
        PlusOrderBillPo po = plusOrderBillMapper.selectByOrderSn(orderSn);
        return converter.toPlusOrderBillEntity(po);
    }

    @Override
    public PlusOrderBillEntity getByOrderSnAndSerialNumber(String orderSn, String serialNumber) {
        PlusOrderBillPo po = plusOrderBillMapper.selectByOrderSnAndSerialNumber(orderSn, serialNumber);
        return converter.toPlusOrderBillEntity(po);
    }

    @Override
    public void updatePlusOrderBill(PlusOrderBillEntity entity) {
        PlusOrderBillPo plusOrderBillPo = converter.toPlusOrderBillPo(entity);
        plusOrderBillMapper.updatePlusOrderBill(plusOrderBillPo);
    }

    /**
     * 分页查询Data 只查询入账成功的
     */
    @Override
    public List<PlusOrderBillEntity> pageList(OrderBillQueryReq req) {
        return converter.toPlusOrderBillEntityList(plusOrderBillMapper.pageList(req));
    }

    /**
     * 分页查询Count 只查询入账成功的
     */
    @Override
    public Integer pageListCount(OrderBillQueryReq req) {
        return plusOrderBillMapper.pageListCount(req);
    }

    @Override
    public List<PlusOrderBillEntity> getInComeRetryList(Integer size) {
        List<PlusOrderBillPo> plusOrderBillPos = plusOrderBillMapper.selectInComeRetryList(size);
        return converter.toPlusOrderBillEntityList(plusOrderBillPos);
    }

    @Override
    public void updateInStateBatch(List<Integer> ids, Integer inState) {
        plusOrderBillMapper.updateInStateBatch(ids, inState);
    }

    @Override
    public List<PlusOrderBillEntity> getOutComeRetryList(Integer size) {
        List<PlusOrderBillPo> plusOrderBillPos = plusOrderBillMapper.selectOutComeRetryList(size);
        return converter.toPlusOrderBillEntityList(plusOrderBillPos);
    }

    @Override
    public void updateOutStateBatch(List<Integer> ids, Integer outState) {
        plusOrderBillMapper.updateOutStateBatch(ids, outState);
    }

    @Override
    public List<PlusOrderBillEntity> getContractReTryList(Integer size) {
        List<PlusOrderBillPo> plusOrderBillPos = plusOrderBillMapper.selectContractReTryList(size);
        return converter.toPlusOrderBillEntityList(plusOrderBillPos);
    }

    @Override
    public void updateContractStateBatch(List<Integer> ids, Integer contractState) {
        plusOrderBillMapper.updateContractStateBatch(ids, contractState);
    }

    @Override
    public List<PlusOrderBillEntity> getContractUploadReTryList(Integer size) {
        List<PlusOrderBillPo> plusOrderBillPos = plusOrderBillMapper.selectContractUploadReTryList(
                size);
        return converter.toPlusOrderBillEntityList(plusOrderBillPos);
    }

    @Override
    public void updateContractUploadStateBatch(List<Integer> ids, Integer contractState) {
        plusOrderBillMapper.updateContractUploadStateBatch(ids, contractState);
    }

    @Override
    public List<PlusOrderBillEntity> selectDeductIncomeRetryList(Integer limit) {
        return converter.toPlusOrderBillEntityList(
                plusOrderBillMapper.selectDeductIncomeRetryList(limit));
    }
}
