package com.juzifenqi.plus.module.program.repository.dao;

import com.juzifenqi.plus.module.program.repository.po.PlusQjhkCouponConfigPo;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 区间还款优惠券配置表
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/4/9 17:31
 */
@Mapper
public interface IPlusQjhkCouponConfigMapper {

    /**
     * 新增返回ID
     */
    Integer save(PlusQjhkCouponConfigPo plusQjhkCouponConfig);

    /**
     * 获取生效的配置
     */
    List<PlusQjhkCouponConfigPo> selectEffectiveList();

    /**
     * 分页查询
     */
    List<PlusQjhkCouponConfigPo> getPageList(@Param("periodNum") Integer periodNum,
            @Param("start") Integer start, @Param("size") Integer size);

    /**
     * 总数量
     */
    Integer countList(@Param("periodNum") Integer periodNum);

    /**
     * 根据id查询
     */
    PlusQjhkCouponConfigPo getById(Integer id);

    /**
     * 根据id修改
     */
    int updateById(@Param("config") PlusQjhkCouponConfigPo config);

    /**
     * 根据id修改
     */
    int updateStateById(@Param("id") Integer id, @Param("state") Integer state);

}
