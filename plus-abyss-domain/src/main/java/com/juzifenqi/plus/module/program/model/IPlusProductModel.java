package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.dto.req.PlusVirtualProductReq;
import com.juzifenqi.plus.module.program.model.contract.entity.LyffVirtualInfoEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProductDetailEntity;
import com.juzifenqi.plus.module.program.model.contract.entity.RdzxEquityInfoEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusVirtualProductEntity;
import com.juzifenqi.plus.module.program.model.event.LyffVirtualInfoEvent;
import com.juzifenqi.plus.module.program.model.event.PlusProductDetailEvent;
import java.math.BigDecimal;

/**
 * 会员商品model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/4 17:34
 */
public interface IPlusProductModel {

    /**
     * 获取会员商品详情页信息
     */
    PlusProductDetailEntity getProductDetail(PlusProductDetailEvent event);

    /**
     * 获取权益0元发放虚拟商品信息,三期上线之后废弃
     */
    LyffVirtualInfoEntity getLyffVirtualInfo(LyffVirtualInfoEvent event);

    /**
     * 修改会员方案对应商品状态
     */
    void updateMemberPlusGoodsState(String id, Integer type);

    /**
     * 调服务查询商品信息
     */
    PlusVirtualProductEntity doGetProductDetail(PlusVirtualProductReq req);

    /**
     * 获取权益0元发放虚拟商品信息
     */
    RdzxEquityInfoEntity getLyffVirtualInfo(Integer programId, BigDecimal serviceFee);

}
