package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.module.program.model.event.CreateChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EditChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EnableChannelManageEvent;

/**
 * 渠道管理model
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2023/12/20 11:31
 */
public interface IPlusChannelManagerModel {

    /**
     * 创建渠道管理配置
     */
    void createChannelManager(CreateChannelManagerEvent event);

    /**
     * 编辑渠道管理配置
     */
    void editChannelManager(EditChannelManagerEvent event);

    /**
     * 启用和停用渠道管理配置
     */
    void enableChannelManager(EnableChannelManageEvent event);
}
