package com.juzifenqi.plus.module.program.model;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProductInfoPo;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramProductNewPo;
import java.util.List;

/**
 * 会员商品查询
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/2/5 11:41
 */
public interface IPlusProductQueryModel {

    /**
     * 获取权益下配置的商品
     */
    PlusProgramProductNewPo getProgramProduct(String productSku, Integer programId,
            Integer modelId);

    /**
     * 获取会员商品池数据（新）
     */
    List<PlusProductInfoPo> getPlusProductBySkus(List<String> skus);
}
