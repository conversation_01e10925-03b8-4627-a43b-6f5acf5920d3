package com.juzifenqi.plus.module.program.model.contract.entity;

import java.math.BigDecimal;
import lombok.Data;

/**
 * 权益0元发放虚拟商品配置信息
 *
 * <AUTHOR>
 * @date 2024/5/24 下午2:33
 */
@Data
public class LyffVirtualDetailEntity {

    /**
     * 营销图片地址
     */
    private String imgUrl;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 会员价(售价 * 会员商品折扣)
     */
    private BigDecimal plusPrice;

    /**
     * 市场价
     */
    private BigDecimal plusMarketPrice;

    /**
     * 商品详情页内容(富文本)
     */
    private String productDetail;

    /**
     * 领取按钮状态
     *
     * @see com.juzifenqi.plus.enums.BuyButtonStateEnum
     */
    private Integer buyButtonState;

    /**
     * 充值code
     */
    private Integer rechargeCode;

    /**
     * 充值类型名称
     */
    private String rechargeName;

    /**
     * 充值方式 1_直充 2_卡密 3_酒店券直充
     */
    private Integer rechargeType;

}
