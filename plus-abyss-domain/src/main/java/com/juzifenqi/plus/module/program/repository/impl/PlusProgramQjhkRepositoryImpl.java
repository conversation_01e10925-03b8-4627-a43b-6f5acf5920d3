package com.juzifenqi.plus.module.program.repository.impl;

import com.juzifenqi.plus.constants.RedisConstantPrefix;
import com.juzifenqi.plus.module.program.model.contract.IPlusProgramQjhkRepository;
import com.juzifenqi.plus.module.program.model.contract.entity.PlusProgramQjhkEntity;
import com.juzifenqi.plus.module.program.model.event.CreateQjhkEvent;
import com.juzifenqi.plus.module.program.repository.converter.IPlusProgramQjhkConverter;
import com.juzifenqi.plus.module.program.repository.dao.profits.IPlusProgramQjhkMapper;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramQjhkPo;
import com.juzifenqi.plus.utils.RedisUtils;
import java.math.BigDecimal;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

/**
 * 区间还款
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/3/25 10:11
 */
@Slf4j
@Repository
public class PlusProgramQjhkRepositoryImpl implements IPlusProgramQjhkRepository {

    private final IPlusProgramQjhkConverter converter = IPlusProgramQjhkConverter.instance;

    @Autowired
    private IPlusProgramQjhkMapper qjhkMapper;
    @Autowired
    private RedisUtils             redisUtils;

    @Override
    public Integer save(CreateQjhkEvent event) {
        PlusProgramQjhkPo plusProgramQjhkPo = converter.toPo(event);
        qjhkMapper.save(plusProgramQjhkPo);
        return plusProgramQjhkPo.getId();
    }

    @Override
    public void update(CreateQjhkEvent event) {
        qjhkMapper.update(converter.toPo(event));
    }

    @Override
    public PlusProgramQjhkEntity getByProgramId(Integer programId) {
        return converter.toEntity(qjhkMapper.getByProgramId(programId));
    }

    @Override
    public PlusProgramQjhkEntity getById(Integer id) {
        return converter.toEntity(qjhkMapper.getById(id));
    }


    @Override
    public BigDecimal getQjhkDiscountRate(Integer programId) {
        // 查询redis缓存
        String cache = redisUtils.get(
                String.format(RedisConstantPrefix.PROGRAM_QJHK_CACHE, programId));
        log.info("获取区间还款折扣配置缓存:{}", cache);
        if (StringUtils.isNotBlank(cache)) {
            return new BigDecimal(cache);
        }
        log.info("未获取到配置折扣，默认使用0.95：{}", programId);
        return new BigDecimal("0.95");
    }
}
