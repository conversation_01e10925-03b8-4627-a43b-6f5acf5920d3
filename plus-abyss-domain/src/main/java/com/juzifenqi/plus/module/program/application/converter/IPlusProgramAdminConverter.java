package com.juzifenqi.plus.module.program.application.converter;

import com.juzifenqi.plus.dto.req.admin.program.MultiplexChannelProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.CreateChannelManagerReq;
import com.juzifenqi.plus.dto.req.admin.program.CreateProgramEditPriceReq;
import com.juzifenqi.plus.dto.req.admin.program.EditChannelManagerReq;
import com.juzifenqi.plus.dto.req.admin.program.EnableChannelManageReq;
import com.juzifenqi.plus.dto.req.admin.program.SaveDefaultPriceReq;
import com.juzifenqi.plus.dto.req.admin.program.SaveDiffPriceReq;
import com.juzifenqi.plus.dto.req.admin.program.SavePlusProgramReq;
import com.juzifenqi.plus.dto.req.admin.program.UpProgramReq;
import com.juzifenqi.plus.dto.resp.admin.PlusConfigResp;
import com.juzifenqi.plus.dto.resp.admin.program.DefaultPriceDetailResp;
import com.juzifenqi.plus.dto.resp.admin.program.DiffPriceDetailResp;
import com.juzifenqi.plus.dto.resp.admin.program.PlusChannelManagerResp;
import com.juzifenqi.plus.dto.resp.admin.program.PlusProgramEditPriceResp;
import com.juzifenqi.plus.dto.resp.admin.program.PlusProgramListResp;
import com.juzifenqi.plus.dto.resp.admin.program.PlusProgramPriceListResp;
import com.juzifenqi.plus.dto.resp.admin.program.ProgramDetailResp;
import com.juzifenqi.plus.dto.resp.admin.program.PlusProgramResp;
import com.juzifenqi.plus.module.program.model.entity.PlusConfigEntity;
import com.juzifenqi.plus.module.program.model.entity.PlusProgramEditPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelFunctionEntity;
import com.juzifenqi.plus.module.program.model.entity.manager.PlusChannelManagerEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DefaultPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.DiffPriceDetailEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity;
import com.juzifenqi.plus.module.program.model.event.CreateChannelFunctionEvent;
import com.juzifenqi.plus.module.program.model.event.CreateChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EditChannelManagerEvent;
import com.juzifenqi.plus.module.program.model.event.EnableChannelManageEvent;
import com.juzifenqi.plus.module.program.model.event.ProgramDetailEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.MultiplexChannelProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.CreateProgramEditPriceEvent;
import com.juzifenqi.plus.module.program.model.event.program.SavePlusProgramEvent;
import com.juzifenqi.plus.module.program.model.event.program.UpProgramEvent;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramListEntity;
import com.juzifenqi.plus.module.program.repository.entity.PlusProgramEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelFunctionPo;
import com.juzifenqi.plus.module.program.repository.po.PlusChannelManagePo;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.mapstruct.AfterMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 方案后台转换器
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/3 15:01
 */
@Mapper
public interface IPlusProgramAdminConverter {

    IPlusProgramAdminConverter instance = Mappers.getMapper(IPlusProgramAdminConverter.class);

    List<PlusProgramEditPriceResp> toPlusProgramEditPriceRespList(
            List<PlusProgramEditPriceEntity> list);

    CreateProgramEditPriceEvent toCreateProgramEditPriceEvent(CreateProgramEditPriceReq req);

    List<PlusChannelManagerResp> toPlusChannelManagerRespList(List<PlusChannelManagerEntity> list);

    CreateChannelManagerEvent toCreateChannelManagerEvent(CreateChannelManagerReq req);

    PlusChannelManagePo toPlusChannelManagePo(CreateChannelManagerEvent event, Integer state);

    EditChannelManagerEvent toEditChannelManagerEvent(EditChannelManagerReq req);

    List<PlusChannelFunctionPo> toPlusChannelFunctionPoList(List<CreateChannelFunctionEvent> list);

    PlusChannelManagerEntity toPlusChannelManagerEntity(PlusChannelManagePo po);

    List<PlusChannelFunctionEntity> toPlusChannelFunctionEntityList(
            List<PlusChannelFunctionPo> list);

    EnableChannelManageEvent toEnableChannelManageEvent(EnableChannelManageReq req);

    PlusChannelManagerResp toPlusChannelManagerResp(PlusChannelManagerEntity entity);

    List<PlusChannelManagerEntity> toPlusChannelManagerEntityList(List<PlusChannelManagePo> list);

    SaveDiffPriceEvent toSaveDiffPriceEvent(SaveDiffPriceReq req);

    DiffPriceDetailResp toDiffPriceDetailResp(DiffPriceDetailEntity entity);

    SaveDefaultPriceEvent toSaveDefaultPriceEvent(SaveDefaultPriceReq req);

    DefaultPriceDetailResp toDefaultPriceDetailResp(DefaultPriceDetailEntity entity);

    List<PlusProgramPriceListResp> toPlusProgramPriceListRespList(
            List<PlusProgramPriceListEntity> list);

    List<PlusProgramResp> toPlusProgramRespList(List<PlusProgramEntity> list);



    List<PlusProgramListResp> toPlusProgramListRespList(List<PlusProgramListEntity> list);

    SavePlusProgramEvent toSavePlusProgramEvent(SavePlusProgramReq req);

    MultiplexChannelProgramEvent toMultiplexChannelProgramEvent(MultiplexChannelProgramReq req);

    @Mapping(target = "payTypes", ignore = true)
    ProgramDetailResp toProgramDetailResp(ProgramDetailEntity entity);

    UpProgramEvent toSaveProgramEvent(UpProgramReq req);

    List<PlusConfigResp> toPlusConfigRespList(List<PlusConfigEntity> plusConfigEntityList);


    @AfterMapping
    default void ofProgramDetailResp(@MappingTarget ProgramDetailResp resp, ProgramDetailEntity entity) {
        if (StringUtils.isNotBlank(entity.getPayTypes())) {
            List<Integer> payTypes = Arrays.stream(entity.getPayTypes().split(",")).map(Integer::valueOf).collect(Collectors.toList());
            resp.setPayTypes(payTypes);
        }
    }
}
