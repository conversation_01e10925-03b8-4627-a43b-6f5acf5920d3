package com.juzifenqi.plus.module.program.repository.converter;


import com.juzifenqi.plus.module.program.model.entity.PlusConfigEntity;
import com.juzifenqi.plus.module.program.repository.po.PlusConfigPo;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * IPlusConfigRepositoryConverter 
 * <AUTHOR> 
 * @Description  
 * @Date 2024/06/14 11:28
**/
@Mapper
public interface IPlusConfigRepositoryConverter {

    IPlusConfigRepositoryConverter instance = Mappers.getMapper(IPlusConfigRepositoryConverter.class);

    PlusConfigPo toPlusConfigPo(PlusConfigEntity plusConfigEntity);

    PlusConfigEntity toPlusConfigEntity(PlusConfigPo plusConfigPo);

    List<PlusConfigEntity> toPlusConfigEntityList(List<PlusConfigPo> plusConfigPoList);
}
