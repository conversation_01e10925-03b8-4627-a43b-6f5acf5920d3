package com.juzifenqi.plus.module.program.model.contract;

import com.juzifenqi.plus.dto.req.admin.program.PriceQueryReq;
import com.juzifenqi.plus.module.program.model.entity.price.PlusDefaultProgramPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusDifferenceProgramPriceEntity;
import com.juzifenqi.plus.module.program.model.entity.price.PlusProgramPriceListEntity;
import com.juzifenqi.plus.module.program.model.event.price.SaveDefaultPriceEvent;
import com.juzifenqi.plus.module.program.model.event.price.SaveDiffPriceEvent;
import com.juzifenqi.plus.module.program.repository.po.price.PlusProgramPricePo;
import java.util.List;

/**
 * 会员差异化/默认方案
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/6/11 10:27
 */
public interface IPlusPriceRepository {

    /**
     * 按渠道和会员类型判重
     */
    boolean checkByChannelAndConfigId(Integer channelId, Integer configId,Integer bizSource, Integer priceType);

    /**
     * 保存差异化配置
     */
    Integer saveDiffPrice(SaveDiffPriceEvent event);

    /**
     * 获取配置
     */
    PlusProgramPricePo getById(Integer id);

    /**
     * 编辑差异化配置
     */
    void updateDiffPrice(SaveDiffPriceEvent event);

    /**
     * 获取差异化配置
     */
    List<PlusDifferenceProgramPriceEntity> getDiffPrices(Integer priceId);

    /**
     * 保存默认方案配置
     */
    Integer saveDefaultPrice(SaveDefaultPriceEvent event);

    /**
     * 编辑默认方案配置
     */
    void editDefaultPrice(SaveDefaultPriceEvent event);

    /**
     * 获取默认方案配置列表
     */
    List<PlusDefaultProgramPriceEntity> getDefaultPrices(Integer priceId);

    /**
     * 分页列表
     */
    List<PlusProgramPriceListEntity> getPriceList(PriceQueryReq req);

    /**
     * 分页总数量
     */
    Integer countPriceList(PriceQueryReq req);

}
