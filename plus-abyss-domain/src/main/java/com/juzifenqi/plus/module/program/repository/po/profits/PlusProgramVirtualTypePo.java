package com.juzifenqi.plus.module.program.repository.po.profits;

import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * @Author:gaoyu
 * @Date: 2022-05-19 10:28:29
 * @Description: 生活权益分类表
 */
@Data
public class PlusProgramVirtualTypePo implements Serializable {

    private static final long serialVersionUID = 42L;

    /**
     * 主键
     */
    private Integer id;

    /**
     * 会员类型
     */
    private Integer configId;

    /**
     * 会员方案ID
     */
    private Integer programId;

    /**
     * 分类名称
     */
    private String typeName;

    /**
     * 购买次数限制 0_不限制购买次数 1_每月购买一次 2_限会员有效期购买一次 3_每周期购买一次（member_plus_detail_periods）
     */
    private Integer numLimit;

    /**
     * 购买分类下权益种类数量限制 0_不限制 1_限制
     */
    private Integer typeLimit;

    /**
     * 可选分类下权益种类数量
     */
    private Integer typeLimitNum;

    /**
     * 排序序号
     */
    private Integer rankNum;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 修改时间
     */
    private Date updateTime;

    /**
     * 修改人ID
     */
    private Integer updateUserId;

    /**
     * 修改人姓名
     */
    private String updateUserNm;

    /**
     * 父级ID
     */
    private Integer parentId;

    /**
     * 分类层级
     */
    private Integer typeLevel;

    /**
     * 权益id
     */
    private Integer modelId;

    /**
     * 营销文案
     */
    private String marketContent;
}