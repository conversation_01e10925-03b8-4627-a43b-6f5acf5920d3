package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusProgramSrlEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramSrlEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramSrlPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface PlusProgramSrlConverter {

    PlusProgramSrlConverter instance = Mappers.getMapper(PlusProgramSrlConverter.class);

    PlusProgramSrlPo toPlusProgramSrlPo(CreatePlusProgramSrlEvent event);
    PlusProgramSrlEntity toPlusProgramSrlEntity(PlusProgramSrlPo po);
}
