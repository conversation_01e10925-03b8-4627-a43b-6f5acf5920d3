package com.juzifenqi.plus.module.program.repository.converter;

import com.juzifenqi.plus.module.program.model.entity.PlusProgramZskfEntity;
import com.juzifenqi.plus.module.program.model.event.CreatePlusProgramZskfEvent;
import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramZskfPo;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
@Mapper
public interface PlusProgramZskfConverter {

    PlusProgramZskfConverter instance = Mappers.getMapper(PlusProgramZskfConverter.class);

    PlusProgramZskfEntity toPlusProgramZskfEntity(PlusProgramZskfPo po);
    PlusProgramZskfPo toPlusProgramZskfPo(CreatePlusProgramZskfEvent event);

}
