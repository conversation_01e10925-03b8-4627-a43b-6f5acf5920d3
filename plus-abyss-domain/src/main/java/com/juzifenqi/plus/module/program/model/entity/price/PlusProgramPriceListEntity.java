package com.juzifenqi.plus.module.program.model.entity.price;

import lombok.Data;

/**
 * 定价entity
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2022/12/19 13:33
 */
@Data
public class PlusProgramPriceListEntity {

    /**
     * id
     */
    private Integer id;

    /**
     * 渠道id
     */
    private Integer channelId;
    /**
     * 渠道标识
     */
    private Integer bizSource;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 方案id
     */
    private Integer configId;

    /**
     * 定价类型 1：默认方案价 2：差异化定价
     */
    private Integer priceType;

    /**
     * 提额等级顺序 1_399-199  2_199-399
     */
    private Integer grade;

    /**
     * 默认方案id
     */
    private Integer defaultProgramId;
}
