package com.juzifenqi.plus.module.program.model.event;

import com.juzifenqi.plus.module.program.repository.po.profits.PlusProgramCashbackPo;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @createTime 2024/3/1 19:01
 * @description
 */
@Data
public class CreatePlusGwfxProgramEvent {

    /**
     * 创建人ID
     */
    private Integer createUserId;

    /**
     * 创建人姓名
     */
    private String createUserName;

    /**
     * 配置id
     */
    private Integer configId;

    /**
     * 方案id
     */
    private Integer programId;

    /**
     * 权益id
     */
    private Integer modelId;

    private List<PlusProgramCashbackPo> cashbacks;
}
