package com.juzifenqi.plus.exception;


import com.groot.utils.exception.GlobalException;
import com.juzifenqi.plus.enums.VipErrorEnum;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class PlusAbyssException extends GlobalException {

    private String errorCode;

    public PlusAbyssException(String errorCode, String message) {
        super(message);
        this.setErrorCode(errorCode);
    }

    public PlusAbyssException(String errorCode, String message, Throwable cause) {
        super(message, cause);
        this.setErrorCode(errorCode);
    }

    public PlusAbyssException(String message) {
        super(message);
        this.setErrorCode("500");
    }

    public PlusAbyssException(VipErrorEnum vipErrorEnum) {
        super(vipErrorEnum.getMessage());
        this.errorCode = String.valueOf(vipErrorEnum.getCode());
    }

    public PlusAbyssException(String message, Throwable cause) {
        super(message, cause);
        this.setErrorCode("500");
    }

}
