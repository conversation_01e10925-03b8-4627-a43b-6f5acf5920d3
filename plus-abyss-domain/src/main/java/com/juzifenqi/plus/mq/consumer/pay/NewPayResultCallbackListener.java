package com.juzifenqi.plus.mq.consumer.pay;

import com.alibaba.fastjson.JSONObject;
import com.aliyun.openservices.ons.api.ConsumeContext;
import com.aliyun.openservices.ons.api.Message;
import com.aliyun.openservices.ons.api.bean.ConsumerBean;
import com.groot.utils.exception.LogUtil;
import com.juzifenqi.mq.consumer.normal.NormalConsumerClient;
import com.juzifenqi.plus.config.PlusMqConfig;
import com.juzifenqi.plus.constants.PaySourceConstant;
import com.juzifenqi.plus.exception.PlusAbyssException;
import com.juzifenqi.plus.module.common.repository.external.acl.IMRepositoryAcl;
import com.juzifenqi.plus.module.order.application.IPlusOrderApplication;
import com.juzifenqi.plus.module.order.model.contract.entity.callback.pay.NewPayResultCallbackEntity;
import java.nio.charset.StandardCharsets;
import javax.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

/**
 * 新支付系统支付结果回调
 *
 * <AUTHOR>
 * @version 1.0
 * @date 2024/9/2 19:50
 */
@Slf4j
@Configuration
public class NewPayResultCallbackListener extends NormalConsumerClient {

    @Autowired
    private PlusMqConfig          mqConfig;
    @Autowired
    private IMRepositoryAcl       imRepositoryAcl;
    @Autowired
    private IPlusOrderApplication orderApplication;

    @PostConstruct
    public ConsumerBean initNewPayResultCallbackListener() {
        // tag按source处理
        return initConsumer(mqConfig.getGidNewPayResult(), mqConfig.getTopicNewPayResult(),
                PaySourceConstant.PAY_SOURCE_MEMBER);
    }

    @Override
    protected boolean dealMessage(Message message, ConsumeContext consumeContext) {
        try {
            String body = new String(message.getBody(), StandardCharsets.UTF_8);
            log.info("新支付系统支付结果回调内容：{}", body);
            NewPayResultCallbackEntity entity = JSONObject.parseObject(body, NewPayResultCallbackEntity.class);
            orderApplication.newPayResultCallback(entity);
            log.info("新支付系统支付结果回调处理完成：{}", body);
            return true;
        } catch (Exception e) {
            LogUtil.printLog("新支付系统支付结果回调处理异常", e);
            imRepositoryAcl.sendImMessage("新支付系统支付结果回调处理异常,请及时处理！");
            // 业务异常不重复消费处理
            return e instanceof PlusAbyssException;
        }
    }
}
